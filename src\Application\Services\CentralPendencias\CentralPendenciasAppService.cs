using System;
using System.Collections.Generic;
using System.Globalization;
using System.Linq;
using System.Linq.Dynamic.Core;
using System.Net.Http;
using System.Text;
using System.Threading.Tasks;
using AutoMapper.QueryableExtensions;
using Microsoft.EntityFrameworkCore;
using Newtonsoft.Json;
using NLog;
using SistemaInfo.BBC.Application.Email.Abastecimento;
using SistemaInfo.BBC.Application.Interface.CentralPendencias;
using SistemaInfo.BBC.Application.Interface.PagamentoEvento;
using SistemaInfo.BBC.Application.Interface.Pedagio;
using SistemaInfo.BBC.Application.Interface.Transacao;
using SistemaInfo.BBC.Application.Interface.Viagem;
using SistemaInfo.BBC.Application.Objects.Api.Transacao;
using SistemaInfo.BBC.Application.Objects.Api.Transferencia;
using SistemaInfo.BBC.Application.Objects.Api.Viagem;
using SistemaInfo.BBC.Application.Objects.Base;
using SistemaInfo.BBC.Application.Objects.Web.CentralPendencias;
using SistemaInfo.BBC.Domain.Components.Email;
using SistemaInfo.BBC.Domain.Enum;
using SistemaInfo.BBC.Domain.External.Conductor.DTO;
using SistemaInfo.BBC.Domain.External.Conductor.DTO.Cartao;
using SistemaInfo.BBC.Domain.External.Conductor.Interface;
using SistemaInfo.BBC.Domain.External.Movida.DTO.Movida;
using SistemaInfo.BBC.Domain.External.Movida.Interface;
using SistemaInfo.BBC.Domain.Grid;
using SistemaInfo.BBC.Domain.Helper;
using SistemaInfo.BBC.Domain.Models.Abastecimento.Repository;
using SistemaInfo.BBC.Domain.Models.Empresa.Repository;
using SistemaInfo.BBC.Domain.Models.PagamentoEvento.Commands;
using SistemaInfo.BBC.Domain.Models.PagamentoEvento.Repository;
using SistemaInfo.BBC.Domain.Models.Parametros.Repository;
using SistemaInfo.BBC.Domain.Models.Portador.Repository;
using SistemaInfo.BBC.Domain.Models.Posto.Repository;
using SistemaInfo.BBC.Domain.Models.Transacao.Repository;
using SistemaInfo.BBC.Domain.Models.Usuario.Repository;
using SistemaInfo.Framework.DomainDrivenDesign.Application.Service;
using SistemaInfo.Framework.DomainDrivenDesign.Domain.Engine;
using SistemaInfo.Framework.Utils;

namespace SistemaInfo.BBC.Application.Services.CentralPendencias
{
    public class CentralPendenciasAppService 
        : AppService<Domain.Models.PagamentoEvento.PagamentoEvento, IPagamentoEventoReadRepository, IPagamentoEventoWriteRepository>, 
            ICentralPendenciasAppService
    {
        private readonly IViagemAppService _viagemAppService;
        private readonly IParametrosReadRepository _parametrosReadRepository;
        private readonly ITransacaoReadRepository _transacaoReadRepository;
        private readonly ITransacaoRegisterAppService _transacaoAppService;
        private readonly ICartaoRepository _cartaoRepository;
        private readonly IPedagioAppService _pedagioAppService;
        private readonly IConductorPixRepository _conductorPixRepository;
        private readonly IAbastecimentoReadRepository _abastecimentoReadRepository;
        private readonly IEmpresaReadRepository _empresaReadRepository;
        private readonly IPortadorReadRepository _portadorReadRepository;
        private readonly IPostoReadRepository _postoReadRepository;
        private readonly IAbastecimentoMovidaRepository _movidaRepository;
        private readonly INotificationEmailExecutor _notificationEmailExecutor;
        private readonly IUsuarioReadRepository _usuarioReadRepository;
        private IPagamentoEventoAppSerivce _pagamentoEventoAppSerivce;

        public CentralPendenciasAppService(
            IAppEngine engine,
            IPagamentoEventoReadRepository readRepository,
            IPagamentoEventoWriteRepository writeRepository,
            IViagemAppService viagemAppService, 
            IParametrosReadRepository parametrosReadRepository, 
            ITransacaoReadRepository transacaoReadRepository, 
            ICartaoRepository cartaoRepository, 
            ITransacaoRegisterAppService transacaoAppService, 
            IConductorPixRepository conductorPixRepository, 
            IAbastecimentoReadRepository abastecimentoReadRepository, 
            IEmpresaReadRepository empresaReadRepository, 
            IPortadorReadRepository portadorReadRepository, 
            IPostoReadRepository postoReadRepository,
            IUsuarioReadRepository usuarioReadRepository,
            IAbastecimentoMovidaRepository movidaRepository, 
            INotificationEmailExecutor notificationEmailExecutor, IPagamentoEventoAppSerivce pagamentoEventoAppSerivce, IPedagioAppService pedagioAppService) : base(
            engine, readRepository, writeRepository)
        {
            _viagemAppService = viagemAppService;
            _parametrosReadRepository = parametrosReadRepository;
            _transacaoReadRepository = transacaoReadRepository;
            _cartaoRepository = cartaoRepository;
            _transacaoAppService = transacaoAppService;
            _conductorPixRepository = conductorPixRepository;
            _abastecimentoReadRepository = abastecimentoReadRepository;
            _empresaReadRepository = empresaReadRepository;
            _portadorReadRepository = portadorReadRepository;
            _postoReadRepository = postoReadRepository;
            _movidaRepository = movidaRepository;
            _notificationEmailExecutor = notificationEmailExecutor;
            _pagamentoEventoAppSerivce = pagamentoEventoAppSerivce;
            _pedagioAppService = pedagioAppService;
            _usuarioReadRepository = usuarioReadRepository;
        }

        public ConsultarGridCentralPendenciasResponse ConsultarGridCentralPendencias(int empresaId, String dataInicial, String dataFinal, int perfil, int take, int page, OrderFilters orderFilters, List<QueryFilters> filters)
        {
         
            IQueryable<Domain.Models.PagamentoEvento.PagamentoEvento> lCentralPendencias = Repository.Query.
                Where(pe => pe.Status != StatusPagamento.Aberto && pe.Status != StatusPagamento.Fechado && pe.Status != StatusPagamento.Cancelado )
                .Include(v=>v.Viagem)
                .ThenInclude(viagem => viagem.Empresa);
            
            var dtIni = dataInicial.ToDateTime();
            var dtFim = dataFinal.ToDateTime().AddDays(1).AddSeconds(-1);

            if(User.EmpresaId != 0) 
                lCentralPendencias = lCentralPendencias.Where(e => e.Viagem.EmpresaId == User.EmpresaId);
            
            var grupoEmpresaId = _usuarioReadRepository.AsNoTracking().FirstOrDefault(x => x.Id == Engine.User.Id)?.GrupoEmpresaId;
            if (grupoEmpresaId != null)
            {
                lCentralPendencias = lCentralPendencias.Where(x => x.Viagem.Empresa.GrupoEmpresaId == grupoEmpresaId);
            }
            
            if (empresaId != 0)
            {
                if (User.EmpresaId == empresaId || User.EmpresaId == 0)
                    lCentralPendencias = lCentralPendencias.Where(e => e.Viagem.EmpresaId == empresaId);

                else
                    throw new InvalidOperationException("Usuário sem permissões para visualizar pagamentos desta empresa.");
            }
            
            lCentralPendencias = lCentralPendencias.Where(p => p.DataCadastro >= dtIni && p.DataCadastro <= dtFim);
            
            lCentralPendencias = lCentralPendencias.AplicarFiltrosDinamicos(filters);
            lCentralPendencias = string.IsNullOrWhiteSpace(orderFilters?.Campo)
                ? lCentralPendencias.OrderByDescending(o => o.Id)
                : lCentralPendencias.OrderBy($"{orderFilters.Campo} {orderFilters.Operador.DescriptionAttr()}");

            var lCount = lCentralPendencias.Count();
            var retorno = lCentralPendencias.Skip((page - 1) * take)
                .Take(take).ProjectTo<ConsultarGridCentralPendencias>(Engine.Mapper.ConfigurationProvider).ToList();

            return new ConsultarGridCentralPendenciasResponse
            {
                items = retorno,
                totalItems = lCount
            };
        }
        
        public async Task<ConsultarGridCentralPendenciasValePedagioResponse> ConsultarGridCentralPendenciasValePedagio(
        ConsultarGridCentralPendenciasValePedagioRequest request)
    {
        try
        {
            return await _pedagioAppService.ConsultarGridCentralPendenciasValePedagio(request);
        }
        catch (Exception e)
        {
            LogManager.GetCurrentClassLogger().Error(e.Message);
            throw;
        }
    }

        public async Task<ConsultarGridCentralPendenciasMovidaResponse> ConsultarGridCentralPendenciasMovida(
            ConsultarGridCentralPendenciasMovida request)
        {
            var abastecimentos = _abastecimentoReadRepository
                .Where(e => e.IntegracaoMovida == 0 || e.IntegracaoMovida == 2);

            if (request.EmpresaId.HasValue)
            {
                if (User.EmpresaId == request.EmpresaId || User.EmpresaId == 0)
                    abastecimentos = abastecimentos.Where(e => e.EmpresaId == request.EmpresaId);

                else
                    throw new InvalidOperationException("Usuário sem permissões para visualizar abastecimentos desta empresa.");
            }

            else if(User.EmpresaId != 0) 
                abastecimentos = abastecimentos.Where(e => e.EmpresaId == User.EmpresaId);
            
            request.DtInicial = request.DtInicial.ToShortDateString().ToDateTime();
            
            request.DtFinal = request.DtFinal.ToShortDateString().ToDateTime().AddDays(1).AddSeconds(-1);

            abastecimentos = abastecimentos.Where(p => p.DataCadastro >= request.DtInicial && p.DataCadastro <= request.DtFinal);
            
            abastecimentos = abastecimentos.AplicarFiltrosDinamicos(request.Filters);
            
            abastecimentos = string.IsNullOrWhiteSpace(request.Order?.Campo)
                ? abastecimentos.OrderByDescending(o => o.Id)
                : abastecimentos.OrderBy($"{request.Order?.Campo} {request.Order?.Operador.DescriptionAttr()}");

            var count = await abastecimentos.CountAsync();
            
            var retorno = await abastecimentos.Skip((request.Page - 1) * request.Take)
                .Take(request.Take).ProjectTo<ConsultarGridCentralPendenciasMovidaItem>(Engine.Mapper.ConfigurationProvider)
                .ToListAsync();

            return new ConsultarGridCentralPendenciasMovidaResponse
            {
                Items = retorno,
                TotalItems = count
            };
        }

        public CentralPendenciasResponse ConsultarPorId(int idCentralPendencias)
        {
            return Mapper.Map<CentralPendenciasResponse>(Repository.Query
                .FirstOrDefault(a => a.Id == idCentralPendencias));
        }

        public async Task<RespPadrao> ReenviarPagamentoEvento(int pagamentoEventoId, bool automatico = false, bool pixCompletado = false)
        {

            var lLog = LogManager.GetCurrentClassLogger();
            try
            {
                var lPagamentoEventoParaReenvio = await Repository.Query.ConsultarPorIdIncludeViagemPortador(pagamentoEventoId);

                if ((lPagamentoEventoParaReenvio.FormaPagamento == FormaPagamentoEvento.Deposito &&
                     lPagamentoEventoParaReenvio.Status == StatusPagamento.Processando) && !automatico) 
                {
                    return new RespPadrao
                    {
                        sucesso = false,
                        mensagem = "Pagamento em processamento automático!"
                    };
                }
                
                if ((lPagamentoEventoParaReenvio.Status != StatusPagamento.Pendente && lPagamentoEventoParaReenvio.FormaPagamento ==FormaPagamentoEvento.Deposito) && automatico)
                {
                    return new RespPadrao
                    {
                        sucesso = false,
                        mensagem = "Status Invalido para realizar reenvio!"
                    };
                }
                
                var lMensagem = "Reenvio automático inciado!";
                
                var lPagamentoEventoAposRegistroDePendencia = await _viagemAppService
                    .RegistrarPendenciaPagamento(lPagamentoEventoParaReenvio.Id, lMensagem, StatusPagamento.Processando, 
                        null, null, null, true, false, true);
                
                if (lPagamentoEventoAposRegistroDePendencia.Status != StatusPagamento.Processando)
                {
                    return new RespPadrao
                    {
                        sucesso = false,
                        mensagem = "Status Invalido para realizar reenvio!"
                    };
                }
                
                //Map de integrar e ajustes de valores
                var lPagamentoIntegrarRequest = Mapper.Map<PagamentoViagemIntegrarRequest>(lPagamentoEventoParaReenvio.Viagem);
                
                lPagamentoIntegrarRequest.Valor = lPagamentoEventoParaReenvio.Valor;
                lPagamentoIntegrarRequest.RecebedorAutorizado = lPagamentoEventoParaReenvio.RecebedorAutorizado;
                lPagamentoIntegrarRequest.FormaPagamento = lPagamentoEventoParaReenvio.FormaPagamento;
                lPagamentoIntegrarRequest.Tipo = lPagamentoEventoParaReenvio.Tipo;
                lPagamentoIntegrarRequest.CpfCnpjContratado = lPagamentoEventoParaReenvio.Viagem.PortadorProprietario.CpfCnpj;
                lPagamentoIntegrarRequest.CpfMotorista = lPagamentoEventoParaReenvio.Viagem.PortadorMotorista.CpfCnpj;
                lPagamentoIntegrarRequest.PagamentoExternoId = lPagamentoEventoParaReenvio.PagamentoExternoId;
                
                var lRetornoIntegracaoDePagamento = await _viagemAppService
                    .IntegrarPagamentoViagem(lPagamentoIntegrarRequest, servicoReenvio:true, pagamentoEventoId: pagamentoEventoId);
                
                if (lRetornoIntegracaoDePagamento.Pagamento != null)
                {
                    if (lRetornoIntegracaoDePagamento.Pagamento.StatusPagamento == StatusPagamento.Fechado.GetHashCode())
                    {
                        return new RespPadrao()
                        {
                            sucesso = true,
                            mensagem = lRetornoIntegracaoDePagamento.Mensagem
                        };
                    }
                
                    if (lRetornoIntegracaoDePagamento.Pagamento.StatusPagamento == StatusPagamento.Erro.GetHashCode()
                        || lRetornoIntegracaoDePagamento.Pagamento.StatusPagamento == StatusPagamento.Pendente.GetHashCode())
                    {
                        return new RespPadrao()
                        {
                            sucesso = false,
                            mensagem = lRetornoIntegracaoDePagamento.Pagamento.Mensagem
                        };
                    }
                }

                return new RespPadrao()
                {
                    sucesso = false,
                    mensagem = lRetornoIntegracaoDePagamento.Pagamento != null ? lRetornoIntegracaoDePagamento.Pagamento.Mensagem : lRetornoIntegracaoDePagamento.Mensagem
                };
            }
            catch (Exception e)
            {
                lLog.Error("Erro ao reenviar pagamento codigo: "+ pagamentoEventoId);
                lLog.Error(e);
                return new RespPadrao
                {
                    sucesso = false,
                    mensagem = "Erro ao executar reenvio: " + e.Message
                };
            }
        }

        public async Task<RespPadrao> ReenviarAbastecimentoMovida(int abastecimentoId)
        {
            try
            {
                var abastecimento = await _abastecimentoReadRepository.GetByIdIncludeVeiculoAsync(abastecimentoId);

                if (abastecimento.IntegracaoMovida == 1)
                    return new RespPadrao(true, $"Abastecimento {abastecimentoId} já integrado com sucesso à Movida. ");

                var empresa = await _empresaReadRepository.GetByIdIncludeTipoEmpersaAsync(abastecimento.EmpresaId.ToIntSafe());

                if (empresa == null)
                    return new RespPadrao(false, $"Empresa do abastecimento {abastecimentoId} não encontrada. ");

                var portador = await _portadorReadRepository.GetByIdAsync(abastecimento.PortadorId);

                if (portador == null)
                    return new RespPadrao(false, $"Portador do abastecimento {abastecimentoId} não encontrado. ");

                var posto = await _postoReadRepository.GetByIdAsync(abastecimento.PostoId);

                if (posto == null)
                    return new RespPadrao(false, $"Posto do abastecimento {abastecimentoId} não encontrado. ");

                var abastecimentoEnvio = new MovidaReq()
                {
                    Placa = abastecimento.Veiculo.Placa,
                    DataAbastecimento = abastecimento.DataCadastro.ToString(CultureInfo.CurrentCulture),
                    LitragemAbastecida = abastecimento.Litragem,
                    CnpjPosto = posto.Cnpj,
                    ValorAbastecimento = abastecimento.ValorAbastecimento.Round(),
                    IntegracaoID = abastecimento.AutorizacaoAbastecimentoId.ToIntSafe(),
                    AbastecimentoID = abastecimento.Id,
                    ValorTaxaBBC = empresa.TaxaAbastecimento,
                    CpfFuncionario = portador.CpfCnpj,
                    OdometroInformado = abastecimento.Odometro
                };

                var retornoEnvioMovida = await _movidaRepository
                    .RealizaEnvioAbastecimentoMovida(abastecimentoEnvio, empresa);

                if (retornoEnvioMovida == null)
                    return new RespPadrao(false, "Erro fatal ao tentar integrar o abastecimento. Não foi possível obter resposta.");

                abastecimento.DataIntegracaoMovida = DateTime.Now;
                abastecimento.JsonEnvioMovida = JsonConvert.SerializeObject(abastecimentoEnvio);
                abastecimento.RetornoMovida = JsonConvert.SerializeObject(retornoEnvioMovida);
                abastecimento.ContadorIntegracaoMovida = abastecimento.ContadorIntegracaoMovida != null
                    ? abastecimento.ContadorIntegracaoMovida + 1 : 0;
                
                var quantidadeTentativasParaFalha = await GetQuantidadeTentativasReenvioAbastecimentoMovida();
                abastecimento.IntegracaoMovida = retornoEnvioMovida.Success ? 1 : 0;
                if (abastecimento.ContadorIntegracaoMovida >= quantidadeTentativasParaFalha) abastecimento.IntegracaoMovida = 2;

                await Repository.Command.SaveChangesAsync();

                var mensagem = retornoEnvioMovida.Success
                    ? $"Abastecimento {abastecimentoId} integrado com sucesso."
                    : $"Integração do abastecimento {abastecimentoId} não realizada. Resposta: " + retornoEnvioMovida.Msg;

                return new RespPadrao(retornoEnvioMovida.Success, mensagem);
            }
            catch (Exception e)
            {
                LogManager.GetCurrentClassLogger().Error(e);
                return new RespPadrao(false, e.Message);
            }
        }

        public async Task ServiceReenviarAbastecimentosMovida()
        {
            var log = LogManager.GetCurrentClassLogger();
            try
            {
                #region Reenvios

                var quantidadeTentativasParaFalha = await GetQuantidadeTentativasReenvioAbastecimentoMovida();
                
                var abastecimentos = await _abastecimentoReadRepository
                    .Where(c => c.IntegracaoMovida == 0)
                    .Include(c => c.Veiculo)
                    .ThenInclude(c => c.Filial)
                    .Include(c => c.Empresa)
                        .ThenInclude(c => c.TipoEmpresa)
                    .Include(c => c.Portador)
                    .Include(c => c.Posto)
                    .ToListAsync();

                foreach (var abastecimento in abastecimentos)
                {
                    var empresa = abastecimento.Empresa;

                    if (empresa == null)
                    {
                        log.Error($"BAT_CTPD_01 ERRO: Abastecimento {abastecimento.Id} não possui empresa.");
                        continue;
                    }

                    var portador = abastecimento.Portador;

                    if (portador == null)
                    {
                        log.Error($"BAT_CTPD_01 ERRO: Abastecimento {abastecimento.Id} não possui portador.");
                        continue;
                    }

                    var posto = abastecimento.Posto;

                    if (posto == null)
                    {
                        log.Error($"BAT_CTPD_01 ERRO: Abastecimento {abastecimento.Id} não possui posto.");
                        continue;
                    }

                    var abastecimentoEnvio = new MovidaReq()
                    {
                        Placa = abastecimento.Veiculo.Placa,
                        DataAbastecimento = abastecimento.DataCadastro.ToString(CultureInfo.CurrentCulture),
                        LitragemAbastecida = abastecimento.Litragem,
                        CnpjPosto = posto.Cnpj,
                        ValorAbastecimento = abastecimento.ValorAbastecimento.Round(),
                        IntegracaoID = abastecimento.AutorizacaoAbastecimentoId.ToIntSafe(),
                        AbastecimentoID = abastecimento.Id,
                        ValorTaxaBBC = empresa.TaxaAbastecimento,
                        CpfFuncionario = portador.CpfCnpj,
                        OdometroInformado = abastecimento.Odometro
                    };

                    //Abastecimentos que estão além do limite parametrizado porem ainda nao foram marcados como Nao Concluidos
                    //Pq alguem diminuiu o parametro antes de ter chego no limite parametrizado anteriormente
                    if (abastecimento.ContadorIntegracaoMovida.HasValue &&
                        abastecimento.ContadorIntegracaoMovida >= quantidadeTentativasParaFalha)
                    {
                        abastecimento.IntegracaoMovida = 2;
                        await Repository.Command.SaveChangesAsync();
                        continue;
                    }

                    var retornoEnvioMovida = await _movidaRepository.RealizaEnvioAbastecimentoMovida(abastecimentoEnvio, empresa);

                    if (retornoEnvioMovida == null)
                    {
                        log.Fatal($"BAT_CTPD_01 FATAL: Erro interno ao tentar fazer uma requisição à Movida para o " +
                                  $"Abastecimento {abastecimento.Id} e Empresa {abastecimento.EmpresaId}. ");
                        continue;
                    }

                    abastecimento.DataIntegracaoMovida = DateTime.Now;
                    abastecimento.JsonEnvioMovida = JsonConvert.SerializeObject(abastecimentoEnvio);
                    abastecimento.RetornoMovida = JsonConvert.SerializeObject(retornoEnvioMovida);
                    abastecimento.ContadorIntegracaoMovida = abastecimento.ContadorIntegracaoMovida != null ? abastecimento.ContadorIntegracaoMovida + 1 : 0;
                    abastecimento.IntegracaoMovida = retornoEnvioMovida.Success ? 1 : 0;

                    if (!retornoEnvioMovida.Success && abastecimento.ContadorIntegracaoMovida >= quantidadeTentativasParaFalha)
                        abastecimento.IntegracaoMovida = 2;
                    
                    await Repository.Command.SaveChangesAsync();
                }

                #endregion
                
                #region Envio de e-mails dos reenvios falhos
                
                var parametroHorarioEnvioEmail = await _parametrosReadRepository.GetParametrosAsync(
                    -1, Domain.Models.Parametros.Parametros.TipoDoParametro.HorarioEnvioEmailGestorAbastecimentosMovida,
                    Domain.Models.Parametros.Parametros.TipoDoValor.Number);

                if (string.IsNullOrWhiteSpace(parametroHorarioEnvioEmail?.Valor)) return;

                int.TryParse(parametroHorarioEnvioEmail.Valor, out var horarioEnvioEmail);

                if (DateTime.Now.Hour != horarioEnvioEmail) return;

                var abastecimentosComErro = await _abastecimentoReadRepository
                    .Include(c => c.Veiculo)
                    .Include(c => c.Posto)
                    .Include(c => c.Combustivel)
                    .Where(c => c.IntegracaoMovida == 2 && c.ContadorIntegracaoMovida >= 3 && c.DataCadastro <= DateTime.Now.AddDays(-1).EndOfDay())
                    .ToListAsync();

                if (!abastecimentosComErro.Any()) return;

                var errosIntegracao = string.Empty;
                var qtdErros = 0;
                
                foreach (var abastecimento in abastecimentosComErro)
                {
                    try
                    {
                        errosIntegracao += $@"
                            <!--START ABASTECIMENTO {abastecimento.Id}--><div class=""container-filho"">
                            <!--Placa--><div class=""dados-filho"">{abastecimento.Veiculo.Placa}</div>
                            <!--DataAbastecimento--><div class=""dados-filho"">{abastecimento.DataCadastro.ToShortDateString()}</div>
                            <!--Litragem Abastecida--><div class=""dados-filho"">{abastecimento.Litragem.ToString("F2")}</div>
                            <!--Cnpj Posto--><div class=""dados-filho"">{abastecimento.Posto.Cnpj}</div>
                            <!--Valor Abastecimento--><div class=""dados-filho"">R$ {abastecimento.ValorAbastecimento.ToString("F3")}</div>
                            <!--Combustivel--><div class=""dados-filho"">{abastecimento.Combustivel.Nome}</div>
                            <!--Integracao Id--><div class=""dados-filho"">{abastecimento.AutorizacaoAbastecimentoId}</div>
                            <!--Abastecimento Id--><div class=""dados-filho"">{abastecimento.Id}</div>
                            <!--Erro Movida--><div class=""dados-filho"">{abastecimento.RetornoMovida}</div>
                            <!--Dias Sendo Tratado--><div class=""dados-filho"">{(DateTime.Now - abastecimento.DataCadastro).Days}</div>
                            <!--END ABASTECIMENTO {abastecimento.Id}--></div>";
                        qtdErros++;
                    }
                    catch (Exception e)
                    {
                        log.Error(e, "BAT_CTPD_01 ERRO");
                    }
                }

                if (!string.IsNullOrWhiteSpace(errosIntegracao))
                {
                    var emailGestorReenviosMovida = await _parametrosReadRepository.GetParametrosAsync(
                        -1, Domain.Models.Parametros.Parametros.TipoDoParametro.EmailGestorAbastecimentosMovida,
                        Domain.Models.Parametros.Parametros.TipoDoValor.String);

                    if (string.IsNullOrWhiteSpace(emailGestorReenviosMovida?.Valor)) return;
                    
                    var result = await EmailReenvioMovidaFalha.EnviarEmailReenvioMovidaFalha(_notificationEmailExecutor, 
                        emailGestorReenviosMovida.Valor, errosIntegracao, qtdErros);

                    if (!result.Sucesso)
                    {
                        log.Error("BAT_CTPD_01 ERRO AO ENVIAR EMAIL: " + result.Resposta);
                    }
                }
                
                #endregion
            }
            catch (Exception e)
            {
                log.Error(e, "BAT_CTPD_01 ERRO");
            }
        }

        private async Task<int> GetQuantidadeTentativasReenvioAbastecimentoMovida()
        {
            var parametroQuantidadeTentativasParaFalha = await _parametrosReadRepository.GetParametrosAsync(
                -1, Domain.Models.Parametros.Parametros.TipoDoParametro.QuantidadeTentativasReenvioAbastecimentoMovida,
                Domain.Models.Parametros.Parametros.TipoDoValor.Number);

            int quantidadeTentativasParaFalha;
            int.TryParse(parametroQuantidadeTentativasParaFalha?.Valor, out quantidadeTentativasParaFalha);
            if (quantidadeTentativasParaFalha == 0) quantidadeTentativasParaFalha = 3;

            return quantidadeTentativasParaFalha;
        }

        public async Task ServiceReenviarPagamentosPendentes()
        {
            var lLog = LogManager.GetCurrentClassLogger();
            try
            {
                var lParametroQuantidadedeenvioPagamentoEvento = ((await _parametrosReadRepository
                    .GetByTipoDoParametroAsync(Domain.Models.Parametros.Parametros
                        .TipoDoParametro.ConfiguracaoTentativaReenvioPagamentoFrete))?.Valor ?? "5").ToInt();

                var lParametroReenvioPagamentoEvento = ((await _parametrosReadRepository
                    .GetByTipoDoParametroAsync(Domain.Models.Parametros.Parametros
                        .TipoDoParametro.CodigoReenvioPagamentoEvento))?.Valor);
                
                if (lParametroReenvioPagamentoEvento == null)
                {
                    lLog.Error("BAT_PGTO_01 ERRO: Parâmetro CodigoReenvioPagamentoEvento inexistente.");
                    return;
                }
                
                if (lParametroReenvioPagamentoEvento.Equals("0")) return;

                var lPagamentosPendentes = await Repository.Query.GetPagamentosPendentes();

                if (lPagamentosPendentes == null || !lPagamentosPendentes.Any()) return;

                foreach (var lPagamentoEvento in lPagamentosPendentes)
                {
                    RespPadrao lResultado;
                    if (lPagamentoEvento.ContadorReenvio >= lParametroQuantidadedeenvioPagamentoEvento.ToIntSafe()) continue;
                    if (!await EmpresaPermiteReprocessar(lPagamentoEvento.EmpresaId)) 
                    {
                        lPagamentoEvento.Status = StatusPagamento.NaoExecutado;
                        await Repository.Command.SaveChangesAsync();
                        continue;
                    }
                    if (lPagamentoEvento.Status == StatusPagamento.Processando)
                    {
                        if(!(await IsValidParaReprocessar(lPagamentoEvento))) continue;
                        lResultado = await ReenviarPagamentoEvento(lPagamentoEvento.Id, automatico:true);
                    }
                    else
                    {
                        lResultado = await ReenviarPagamentoEvento(lPagamentoEvento.Id, automatico:true);
                    }
                   

                    if (!lResultado.sucesso)
                    {
                        lLog.Error($"BAT_PGTO_01 ERRO: Pagamento evento {lPagamentoEvento.Id} {lResultado.mensagem}");
                    }
                }
            }
            catch (Exception e)
            {
                lLog.Error(e, "BAT_PGTO_01 ERRO");
            }
        }

        private async Task<bool> EmpresaPermiteReprocessar(int? pagamentoEventoEmpresaId)
        {
            if (pagamentoEventoEmpresaId is null) return false;

            var empresaReprocessa = await _empresaReadRepository.GetParametroPermiteReprocesso(pagamentoEventoEmpresaId);
            var grupoEmpresaReprocessa = await _empresaReadRepository.GetParametroPermiteReprocessoGrupoEmpresa(pagamentoEventoEmpresaId);
            return grupoEmpresaReprocessa || empresaReprocessa;
        }

        private async Task<bool> IsValidParaReprocessar(Domain.Models.PagamentoEvento.PagamentoEvento pagamentoEvento)
        {
            var lLog = LogManager.GetCurrentClassLogger();
            try
            {
                var lTransacoes = _transacaoReadRepository
                    .GetTransacoesByIdPagamentoEvento(pagamentoEvento.Id).ToList();

                var statusInicial = pagamentoEvento.Status;
                
                if (lTransacoes.Count < 0) return true;
                
                if (lTransacoes.Count == 1 && pagamentoEvento.CobrancaTarifa == 1) return true;

                foreach (var transacao in lTransacoes.Where(transacao => transacao.Status == StatusPagamento.Processando))
                {
                    switch (transacao.FormaPagamento)
                    {
                        case FormaPagamentoEvento.Deposito:
                        {
                            #region Atualiza a transacao p2p caso tenha perdido conexão com a dock

                            var lRequestConsultaContas = new ConsultarPagamentosContaRequest()
                            {
                                ContaDestino = transacao.Origem.ToStringSafe(),
                                ContaOrigem = transacao.Destino.ToStringSafe(),
                                Valor = transacao.Valor.ToString().Replace(',', '.'),
                                DataPagamento = transacao.DataCadastro.ToString("yyyy-MM-dd") + " 00:00:00"
                            };
                            
                            var lRetornoCartao = await _cartaoRepository.ConsultarPagamentosConta(lRequestConsultaContas);
                            
                            var lTransferencia = JsonConvert.DeserializeObject<List<ConsultarPagamentosContaRetorno>>(lRetornoCartao,
                                new JsonSerializerSettings { DateTimeZoneHandling = DateTimeZoneHandling.Local });

                            if (lTransferencia[0] == null) return true;

                            if (lTransferencia[0].status == "APPROVED")
                            {
                                transacao.Status = StatusPagamento.Fechado;

                                var lAlterarTransacao = new TransacaoAlterarStatusRequest()
                                {
                                    Id = transacao.Id,
                                    Status = StatusPagamento.Fechado,
                                    Descricao = "Pagamento P2P fechado via reprocessamento",
                                    DataBaixa = DateTime.Now,
                                    JsonRespostaDock = lRetornoCartao,
                                    DataRetornoDock = DateTime.Now,
                                    ResponseCodeDock = 200
                                };
                                await _transacaoAppService.AtualizarTransacao(lAlterarTransacao);
                            }

                            #endregion

                            break;
                        }
                        case FormaPagamentoEvento.Pix:
                        {
                            if (transacao.IdEndToEnd == null) return false;
                            
                            #region Atualiza transacao pix caso tenha perdido conexão com a dock

                            var lTransacaoPix = await _conductorPixRepository
                                .ListarTransferenciaPix(transacao.IdEndToEnd);

                            if (lTransacaoPix == null) return true;

                            if (lTransacaoPix.Retorno.Items.Count == 0) return true;
                            
                            switch (lTransacaoPix.Retorno.Items[0].TransactionStatus)
                            {
                                case "EXECUTED":
                                    pagamentoEvento.Status = StatusPagamento.Fechado;
                                    pagamentoEvento.DataBaixa = DateTime.Now;
                                    transacao.Status = StatusPagamento.Fechado;
                                    break;
                                case "NOT_EXECUTED":
                                    transacao.Status = StatusPagamento.NaoExecutado;
                                    pagamentoEvento.Status = StatusPagamento.NaoExecutado;
                                    return false;
                                case "PENDING":
                                    pagamentoEvento.Status = StatusPagamento.Pendente;
                                    transacao.Status = StatusPagamento.Pendente;
                                    return false;
                            }

                            var lAlterarTransacao = new TransacaoAlterarStatusRequest()
                            {
                                Id = transacao.Id,
                                Status = transacao.Status,
                                Descricao = "Pagamento Pix atualizado via reprocessamento",
                                DataBaixa = transacao.Status == StatusPagamento.Fechado ? DateTime.Now : null,
                                JsonRespostaDock = JsonConvert.SerializeObject(lTransacaoPix),
                                
                            };
                            await _transacaoAppService.AtualizarTransacao(lAlterarTransacao);
                            Repository.Command.Update(pagamentoEvento);
                            await Repository.Command.SaveChangesAsync();
                            if(statusInicial != pagamentoEvento.Status)
                                await _pagamentoEventoAppSerivce.EnviaWebHookAtualizacaoStatus(pagamentoEvento);
                            
                            #endregion

                            break;
                        }
                    }
                }

                var lCommandSalvar = Mapper.Map<PagamentoEventoSalvarComRetornoCommand>(pagamentoEvento);
                if (lTransacoes.All(x => x.Status == StatusPagamento.Fechado))
                {
                    lCommandSalvar.Status = StatusPagamento.Fechado;
                    lCommandSalvar.DataBaixa = DateTime.Now;
                    await Engine.CommandBus.SendCommandAsync<Domain.Models.PagamentoEvento.PagamentoEvento>(
                        lCommandSalvar);
                    return false;
                }

                lCommandSalvar.ContadorReenvio += 1;
                await Engine.CommandBus.SendCommandAsync<Domain.Models.PagamentoEvento.PagamentoEvento>(
                    lCommandSalvar);
                return false;
            }
            catch (Exception e)
            {
                lLog.Error("BAT_PGTO_01 ERRO:");
                lLog.Error(e);
                throw;
            }
        }

        public async Task<RespPadrao> Save(CentralPendenciasRequest lCentralPendenciasReq, bool integracao = false)
        {
            try
            {
           
                var command = Mapper.Map<PagamentoEventoSalvarComRetornoCommand>(lCentralPendenciasReq);
                var retorno = await Engine.CommandBus.SendCommandAsync<Domain.Models.PagamentoEvento.PagamentoEvento>(command);

                return new RespPadrao
                {
                    id = retorno.Id,
                    sucesso = true,
                    mensagem = "Registro salvo com sucesso!"
                };
            }
            catch (Exception e)
            {
                return new RespPadrao
                {
                    sucesso = false,
                    mensagem = e.Message
                };
            }
        }
    }
}
