using System;
using System.Collections.Generic;
using System.IO;
using System.Linq;
using System.Net;
using System.Net.Mail;
using System.Net.Mime;
using System.Text;
using System.Threading.Tasks;
using Microsoft.Extensions.Configuration;
using SistemaInfo.BBC.Domain.Components.Email;
using SistemaInfo.BBC.Domain.External.Email;
using SistemaInfo.BBC.Domain.Models.Parametros;
using SistemaInfo.BBC.Domain.Models.Parametros.Repository;
using SistemaInfo.Framework.Utils;

namespace SistemaInfo.BBC.Infra.Data.Components.Email
{
    public class NotificationEmailExecutor : INotificationEmailExecutor
    {
        private readonly IParametrosReadRepository _parametrosReadRepository;

        public NotificationEmailExecutor(IConfiguration configuration,
            IParametrosReadRepository parametrosReadRepository)
        {
            _parametrosReadRepository = parametrosReadRepository;
        }

        public async Task<NotificacaoExecutorResult> ExecuteAsync(Domain.Components.Email.Email email)
        {
            try
            {
                await EnviarEmail(email);
                return new NotificacaoExecutorResult
                {
                    Sucesso = true,
                    DataResposta = DateTime.Now,
                    Resposta = "OK"
                };
            }
            catch (Exception e)
            {
                return new NotificacaoExecutorResult
                {
                    Sucesso = false,
                    DataResposta = DateTime.Now,
                    Resposta = e.GetFullMessage()
                };
            }
        }

        public Task EnviarEmail(Domain.Components.Email.Email email)
        {
            /*var busParams = JsonUtils.ConvertoJsonToObject<NotificacaoEmailBusRequest>(notificacao.BusObject);
            var smtpParams = busParams.Smtp;
            var messageParams = busParams.Message;*/

            var mail = new MailMessage();

            #region Mail/Body config

            //Setting From , To and CC
            var mailDisplayNameEncoding = email.SubjectEncoding.IsNullOrWhiteSpace()
                ? Encoding.UTF8
                : Encoding.GetEncoding(email.SubjectEncoding);

            if (email.From.Address.HasValue())
                mail.From = new MailAddress(email.From.Address, email.From.DisplayName, mailDisplayNameEncoding);
            else
                mail.From = new MailAddress("<EMAIL>", "BBC - Ciot", mailDisplayNameEncoding);

            if (email.To != null)
                foreach (var to in email.To)
                    mail.To.Add(new MailAddress(to.Address, to.DisplayName, mailDisplayNameEncoding));

            if (email.Copy != null)
                foreach (var copy in email.Copy)
                    mail.CC.Add(new MailAddress(copy.Address, copy.DisplayName, mailDisplayNameEncoding));

            if (email.HiddenCopy != null)
                foreach (var hiddenCopy in email.HiddenCopy)
                    mail.Bcc.Add(new MailAddress(hiddenCopy.Address, hiddenCopy.DisplayName, mailDisplayNameEncoding));

            if (email.AlternateView != null)
                mail.AlternateViews.Add(email.AlternateView);

            mail.Subject = email.Subject;
            mail.Body = email.Body;
            mail.IsBodyHtml = email.IsBodyHtml;
            mail.Priority = email.Priority;

            mail.SubjectEncoding = email.SubjectEncoding.IsNullOrWhiteSpace()
                ? Encoding.UTF8
                : Encoding.GetEncoding(email.SubjectEncoding);

            mail.BodyEncoding = email.BodyEncoding.IsNullOrWhiteSpace()
                ? Encoding.UTF8
                : Encoding.GetEncoding(email.BodyEncoding);

            List<MemoryStream> attachementStreams = null;
            if (email.Attachments != null && email.Attachments.Any())
            {
                attachementStreams = new List<MemoryStream>();
                foreach (var attachmentParam in email.Attachments)
                {
                    var stream = new MemoryStream();
                    attachementStreams.Add(stream);

                    var buffer = Convert.FromBase64String(attachmentParam.ContentBase64);

                    stream.Write(buffer, 0, buffer.Length);
                    stream.Flush();
                    stream.Position = 0;

                    var mediaType = attachmentParam.MediaType.IsNullOrWhiteSpace()
                        ? MediaTypeNames.Application.Octet
                        : attachmentParam.MediaType;

                    var attach = new Attachment(stream, attachmentParam.Name, mediaType)
                    {
                        TransferEncoding = TransferEncoding.Base64
                    };

                    mail.Attachments.Add(attach);
                }
            }

            #endregion

            #region Parametros de e-mail

            var host = "";

            var parametroEmailSmtpClient = _parametrosReadRepository
                .GetParametrosAsync(-1, Parametros.TipoDoParametro.EmailSmtpClient, Parametros.TipoDoValor.Criptografia)
                ?
                .Result;

            if (parametroEmailSmtpClient != null)
                host = Encoding.UTF8.GetString(Convert.FromBase64String(parametroEmailSmtpClient.ValorCriptografado));

            var port = 0;

            var parametroEmailPort = _parametrosReadRepository
                .GetParametrosAsync(-1, Parametros.TipoDoParametro.EmailPort, Parametros.TipoDoValor.Number)?
                .Result;

            if (parametroEmailPort != null)
                port = parametroEmailPort.Valor.ToInt();

            var userName = "";

            var parametroEmailUsuario = _parametrosReadRepository
                .GetParametrosAsync(-1, Parametros.TipoDoParametro.EmailUsuario, Parametros.TipoDoValor.Criptografia)?
                .Result;

            if (parametroEmailUsuario != null)
                userName = Encoding.UTF8.GetString(Convert.FromBase64String(parametroEmailUsuario.ValorCriptografado));

            var password = "";

            var parametroEmailSenha = _parametrosReadRepository
                .GetParametrosAsync(-1, Parametros.TipoDoParametro.EmailSenha, Parametros.TipoDoValor.Criptografia)?
                .Result;

            if (parametroEmailSenha != null)
                password = Encoding.UTF8.GetString(Convert.FromBase64String(parametroEmailSenha.ValorCriptografado));

            var enableSsl = false;

            var parametroEmailSsl = _parametrosReadRepository
                .GetParametrosAsync(-1, Parametros.TipoDoParametro.EmailSsl, Parametros.TipoDoValor.Number)?
                .Result;

            if (parametroEmailSsl != null)
                enableSsl = Convert.ToBoolean(parametroEmailSsl.Valor.ToInt());

            #endregion

            var smtpClient = new SmtpClient(host)
            {
                UseDefaultCredentials = false,
                Port = port,
                Credentials = new NetworkCredential(userName, password),
                EnableSsl = enableSsl
            };


            smtpClient.Send(mail);

            return Task.CompletedTask;
        }
    }
}