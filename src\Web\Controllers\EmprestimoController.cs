using System;
using System.IO;
using DinkToPdf.Contracts;
using Microsoft.AspNetCore.Mvc;
using SistemaInfo.BBC.Application.Interface.Emprestimo;
using SistemaInfo.BBC.Application.Objects.Api.Emprestimo;
using SistemaInfo.BBC.Application.Objects.Api.Portador;
using SistemaInfo.BBC.Application.Objects.Base;
using SistemaInfo.BBC.Application.Objects.Web;
using SistemaInfo.BBC.Domain.Enum;
using SistemaInfo.BBC.Web.Attributes;
using SistemaInfo.BBC.Web.Controllers.Base;
using SistemaInfo.Framework.DomainDrivenDesign.Domain.Engine;

namespace SistemaInfo.BBC.Web.Controllers
{
    /// <summary>
    /// 
    /// </summary>
    [Route("Emprestimo")]
    public class EmprestimoController : WebControllerBase<IEmprestimoAppService, IConverter>
    {
        /// <summary>
        /// 
        /// </summary>
        /// <param name="engine"></param>
        /// <param name="appService"></param>
        /// <param name="converter"></param>
        public EmprestimoController(IAppEngine engine, IEmprestimoAppService appService, IConverter converter) : base(
            engine, appService, converter)
        {
        }

        /// <summary>
        /// 
        /// </summary>
        /// <param name="request"></param>
        /// <returns></returns>
        [HttpPost("Consultar")]
        [Menu(new[] { EMenus.Emprestimo })]
        public JsonResult ConsultarEmprestimos([FromBody] BaseGridRequest request)
        {
            try
            {
                var consultarEmprestimos = AppService.ConsultarGridEmprestimos(request.Take, request.Page, request.Order, request.Filters);

                return ResponseBase.ResponderSucesso(consultarEmprestimos);
            }
            catch (Exception e)
            {
                return ResponseBase.ResponderErro(e);
            }
        }

        /// <summary>
        /// </summary>
        /// <param name="request"></param>
        /// <returns></returns>
        [HttpPost("Cadastrar")]
        [Menu(new[] { EMenus.Emprestimo })]
        public JsonResult Cadastrar([FromBody] EmprestimoCadastrarRequest request)
        {
            try
            {
                var response = AppService.Cadastrar(request);
                return ResponseBase.ResponderSucesso(response);
            }
            catch (Exception e)
            {
                return ResponseBase.ResponderErro(e.Message);
            }
        }

        /// <summary>
        /// 
        /// </summary>
        /// <param name="request"></param>
        /// <returns></returns>
        [HttpPost("SinalizaClienteEmprestimo")]
        [Menu(new[] { EMenus.Emprestimo, EMenus.Portador })]
        public JsonResult SinalizaClienteEmprestimo([FromBody] SinalizarEmprestimoRequest request)
        {
            try
            {
                var response = AppService.SinalizaClienteEmprestimo(request).Result;
                return !response.sucesso
                    ? ResponseBase.ResponderErro(response.mensagem)
                    : ResponseBase.ResponderSucesso(response);
            }
            catch (Exception e)
            {
                return ResponseBase.ResponderErro(e.Message);
            }
        }

        /// <summary>
        /// </summary>
        /// <param name="id"></param>
        /// <returns></returns>
        [HttpGet("ConsultarParaEdicao")]
        [Menu(new[] { EMenus.Emprestimo })]
        public JsonResult ConsultarParaEdicao(int id)
        {
            try
            {
                var response = AppService.ConsultarParaEdicao(id);
                return ResponseBase.ResponderSucesso(response);
            }
            catch (Exception e)
            {
                return ResponseBase.ResponderErro(e.Message);
            }
        }

        /// <summary>
        /// </summary>
        /// <param name="request"></param>
        /// <returns></returns>
        [HttpPost("GerarRelatorio")]
        [Menu(new[] { EMenus.Emprestimo })]
        public JsonResult GerarRelatorio([FromBody]Relatorio request)
        {
            try
            {
                var document = AppService.GerarRelatorioEmprestimo(request.json);
                var customAssemblyLoadContext = new CustomAssemblyLoadContext();

                customAssemblyLoadContext.LoadUnmanagedLibrary(
                    Path.Combine(AppContext.BaseDirectory + "libwkhtmltox.dll"));

                var fileContents = AppInterface.Convert(document);

                return ResponseBase.ResponderSucesso(Convert.ToBase64String(fileContents));
            }
            catch (Exception e)
            {
                return ResponseBase.ResponderErro(e.Message);
            }
        }
        
        /// <summary>
        /// 
        /// </summary>
        /// <param name="request"></param>
        /// <returns></returns>
        [HttpPost("DadosRelatorioGridPagamentos")]
        [Menu(new[] { EMenus.CentralNotificacoes, EMenus.CentralPendencias, EMenus.Emprestimo, EMenus.PainelPagamento })]
        public JsonResult DadosRelatorioGridPagamentos([FromBody] BaseGridRequest request)
        {
            try
            {
                return ResponseBase.ResponderSucesso(
                    AppService.DadosRelatorioGridPagamentos(request.Order, request.Filters));
            }
            catch (Exception e)
            {
                return ResponseBase.ResponderErro(e.Message);
            }
        }
        /// <summary>
        /// 
        /// </summary>
        /// <returns></returns>
        public class Relatorio
        {
            /// <summary>
            /// 
            /// </summary>
            /// <returns></returns>
            public int json { get; set; }
        }
    }
}