using System;
using System.Collections.Generic;
using System.Linq;
using System.Linq.Dynamic.Core;
using System.Text;
using System.Threading.Tasks;
using System.Xml;
using AutoMapper.QueryableExtensions;
using Microsoft.EntityFrameworkCore;
using Newtonsoft.Json;
using NLog;
using SistemaInfo.BBC.Application.Helpers.Xml;
using SistemaInfo.BBC.Application.Interface.PagamentoAbastecimento;
using SistemaInfo.BBC.Application.Interface.ProtocoloAbastecimento;
using SistemaInfo.BBC.Application.Objects.Base;
using SistemaInfo.BBC.Application.Objects.Web.PagamentoAbastecimento;
using SistemaInfo.BBC.Application.Objects.Web.ProtocoloAbastecimento;
using SistemaInfo.BBC.Domain.Enum;
using SistemaInfo.BBC.Domain.External.SAP.DTO.SAP;
using SistemaInfo.BBC.Domain.External.SAP.Interface;
using SistemaInfo.BBC.Domain.Grid;
using SistemaInfo.BBC.Domain.Helper;
using SistemaInfo.BBC.Domain.Models.Abastecimento.Commands;
using SistemaInfo.BBC.Domain.Models.Abastecimento.Repository;
using SistemaInfo.BBC.Domain.Models.Combustivel.Repository;
using SistemaInfo.BBC.Domain.Models.Empresa.Repository;
using SistemaInfo.BBC.Domain.Models.EmpresaCfop.Repository;
using SistemaInfo.BBC.Domain.Models.Parametros.Repository;
using SistemaInfo.BBC.Domain.Models.Posto.Repository;
using SistemaInfo.BBC.Domain.Models.PostoCombustivelProduto.Repository;
using SistemaInfo.BBC.Domain.Models.ProtocoloAbastecimento.Commands;
using SistemaInfo.BBC.Domain.Models.ProtocoloAbastecimento.Repository;
using SistemaInfo.BBC.Infra.Reports.Objects;
using SistemaInfo.Framework.DomainDrivenDesign.Application.Service;
using SistemaInfo.Framework.DomainDrivenDesign.Domain.Engine;
using SistemaInfo.Framework.Utils;

namespace SistemaInfo.BBC.Application.Services.ProtocoloAbastecimento
{
    public class ProtocoloAbastecimentoAppService : AppService<
            Domain.Models.ProtocoloAbastecimento.ProtocoloAbastecimento,
            IProtocoloAbastecimentoReadRepository, IProtocoloAbastecimentoWriteRepository>,
        IProtocoloAbastecimentoAppService
    {
        private readonly IAbastecimentoReadRepository _abastecimentoRepository;
        private readonly IAbastecimentoWriteRepository _abastecimentoWriteRepository;
        private readonly IPostoReadRepository _postoRepository;
        private readonly ICombustivelReadRepository _combustivelRepository;
        private readonly IEmpresaCfopReadRepository _empresaCfopReadRepository;
        private readonly IEmpresaReadRepository _empresaReadRepository;
        private readonly IPagamentoAbastecimentoAppService _pagamentoAbastecimentoAppService;
        private readonly IPedidoSAPRepository _pedidoSAPRepository;
        private readonly IParametrosReadRepository _parametrosReadRepository;
        private readonly IPostoCombustivelProdutoReadRepository _postoCombustivelProdutoReadRepository;
        
        public ProtocoloAbastecimentoAppService(
            IAppEngine engine,
            IProtocoloAbastecimentoReadRepository readRepository,
            IProtocoloAbastecimentoWriteRepository writeRepository,
            IAbastecimentoReadRepository abastecimentoRepository,
            IAbastecimentoWriteRepository abastecimentoWriteRepository,
            IPostoReadRepository postoRepository,
            IEmpresaCfopReadRepository empresaCfopReadRepository,
            ICombustivelReadRepository combustivelRepository,
            IPagamentoAbastecimentoAppService pagamentoAbastecimentoAppService,
            IEmpresaReadRepository empresaReadRepository,
            IPostoCombustivelProdutoReadRepository postoCombustivelProdutoReadRepository,
            IPedidoSAPRepository pedidoSAPRepository, 
            IParametrosReadRepository parametrosReadRepository) : base(
            engine, readRepository, writeRepository)
        {
            _abastecimentoRepository = abastecimentoRepository;
            _postoRepository = postoRepository;
            _combustivelRepository = combustivelRepository;
            _empresaCfopReadRepository = empresaCfopReadRepository;
            _empresaReadRepository = empresaReadRepository;
            _pagamentoAbastecimentoAppService = pagamentoAbastecimentoAppService;
            _pedidoSAPRepository = pedidoSAPRepository;
            _parametrosReadRepository = parametrosReadRepository;
            _abastecimentoWriteRepository = abastecimentoWriteRepository;
            _postoCombustivelProdutoReadRepository = postoCombustivelProdutoReadRepository;
        }

        public ConsultarGridProtocoloAbastecimentoResponse ConsultarGridProtocoloAbastecimento(ConsultarGridProtocoloAbastecimentoRequest request)
        {
            try
            {
                var DtInicial = request.dataInicial.ToDateTime();
                var DtFinal = request.dataFinal.ToDateTime().AddDays(1).AddSeconds(-1);
                
                IQueryable<Domain.Models.Abastecimento.Abastecimento> lAbastecimentoQuery;

                lAbastecimentoQuery = _abastecimentoRepository.AsNoTracking()
                    .Where(x => x.DataCadastro >= DtInicial && x.DataCadastro <= DtFinal &&
                                x.EmpresaId == request.EmpresaId &&
                                x.PostoId == User.AdministradoraId &&
                                (x.ProtocoloAbastecimentoId == null ||
                                 x.ProtocoloAbastecimento.Status == EStatusProtocolo.Reprovado) &&
                                x.Status == EStatusAbastecimento.Aprovado)
                    .Include(x => x.Posto)
                    .Include(x => x.Veiculo);

                if (request.CombustivelId != null)
                {
                    lAbastecimentoQuery = lAbastecimentoQuery.Where(x => x.CombustivelId == request.CombustivelId);
                }

                if (!string.IsNullOrWhiteSpace(request.Placa))
                {
                    lAbastecimentoQuery = lAbastecimentoQuery.Where(x => x.Veiculo.Placa.ToLower().Contains(request.Placa.ToLower()));
                }
                
                if (request.AbastecimentoId != null)
                {
                  lAbastecimentoQuery =  lAbastecimentoQuery.Where(x => x.Id == request.AbastecimentoId);
                }
                
                var lCount = lAbastecimentoQuery.Count();

                var retorno = lAbastecimentoQuery
                    .ProjectTo<ConsultarGridProtocoloAbastecimento>(Engine.Mapper.ConfigurationProvider).ToList();
            
                // Filtro 1: Abastecimento sem CNPJ de faturamento
                if (retorno.Any(x => x.CnpjAFaturar == null))
                    return new ConsultarGridProtocoloAbastecimentoResponse
                    {
                        Sucesso = false,
                        message = "Foram encontrados um ou mais abastecimentos sem CNPJ de faturamento"
                    };
                
                return new ConsultarGridProtocoloAbastecimentoResponse
                {   
                    Items = retorno,
                    totalItems = lCount
                };
            }
            catch (Exception e)
            {
                LogManager.GetCurrentClassLogger().Error(e);
                throw;
            }
        }

        public CarregarDadosXmlResponse CarregarDadosXml(XmlAbastecimentoRequest lXmlAbastecimentoRequestReq)
        {
            try
            {
                XmlDocument lXmlDados = new XmlDocument();

                var empresaAbastecimento = _abastecimentoRepository.Where(x =>
                    (x.Empresa.Cnpj == lXmlAbastecimentoRequestReq.CnpjAFaturar || 
                     x.Filial.Cnpj == lXmlAbastecimentoRequestReq.CnpjAFaturar || 
                     x.Empresa.Filial.Any(f => f.Cnpj == lXmlAbastecimentoRequestReq.CnpjAFaturar))
                    && x.PostoId == User.AdministradoraId.ToInt()).FirstOrDefault()?.EmpresaId;

                #region Carregamento de Arquivos
                
                try
                {
                    lXmlDados.LoadXml(
                        Encoding.UTF8.GetString(Convert.FromBase64String(lXmlAbastecimentoRequestReq.Xml)));
                }
                catch (Exception)
                {
                    throw new Exception("Xml inválido!");
                }
                
                // Filtro 1: Xml igual
                var numNota = lXmlDados.GetElementsByTagName("nNF")[0].InnerText;
                var cnpjPostoLogado = _postoRepository.FirstOrDefault(x => x.Id == User.AdministradoraId).Cnpj;
                
                var numNotaBanco = Repository.Query.FirstOrDefault(x => x.NotaXml == numNota 
                                                                        && x.Posto.Cnpj == cnpjPostoLogado 
                                                                        && (x.Status == EStatusProtocolo.Aprovado || 
                                                                            x.Status == EStatusProtocolo.Pendente));
                if (numNotaBanco != null)
                {
                    throw new Exception($"Nota já cadastrada para o protocolo de número {numNota}");
                }
                
                // Filtro 2: Quantidade de dados do xml
                if (!(XmlIsValid(lXmlDados)))
                {
                    throw new Exception($"Quantidade de dados dos xml é inválida");
                }

                string dataEmissaoXml;
                
                try
                {
                    dataEmissaoXml = lXmlDados.GetElementsByTagName("NFe")[0].InnerXml.Split('"')[1] == "2.00" || 
                                     lXmlDados.GetElementsByTagName("NFe")[0].InnerXml.Split('"')[3] == "2.00"
                        ? lXmlDados.GetElementsByTagName("dEmi")[0].InnerText
                        : lXmlDados.GetElementsByTagName("NFe")[0].InnerXml.Split('"')[1] == "4.00" ||
                          lXmlDados.GetElementsByTagName("NFe")[0].InnerXml.Split('"')[3] == "4.00"
                            ? lXmlDados.GetElementsByTagName("dhEmi")[0].InnerText
                            : throw new Exception($"Versão do XML não compativel com sistema!");
                }
                catch (Exception)
                {
                    throw new Exception($"Tag data de emissão não corresponde com a versão do xml!");
                }

                foreach (var abastecimento in lXmlAbastecimentoRequestReq.AbastecimentosSelecionados)
                {
                    if (Convert.ToDateTime(dataEmissaoXml) <= Convert.ToDateTime(abastecimento.DataCadastro))
                    {
                        throw new Exception("A data de emissão da NFe é anterior a data do abastecimento!");
                    }
                }

                decimal qtdLitrosXml = 0;
                decimal valorUnitarioXml = 0;

                for (var i = 0; i < lXmlDados.GetElementsByTagName("qCom").Count; i++)
                {
                    var cfopXml = lXmlDados.GetElementsByTagName("CFOP")[i].InnerText.ToInt();
                    
                    var validarCfopEmpresa = _empresaCfopReadRepository.Where(x =>
                        x.EmpresaId == empresaAbastecimento.ToInt() && x.CFOP.Cfop == cfopXml).Any();

                    if (!validarCfopEmpresa)
                    {
                        throw new Exception("CFOP não vinculado a empresa!");
                    }

                    qtdLitrosXml += Convert.ToDecimal(Math.Truncate(lXmlDados.GetElementsByTagName("qCom")[i].InnerText.Replace(".", ",").ToDecimal() * 1000) / 1000);
                    
                    // Filtro 3: Validar codigo do produto
                    var cProdXml = lXmlDados.GetElementsByTagName("cProd")[i].InnerText;
                    
                    var produto = _postoRepository
                        .Include(x => x.PostoCombustivelProduto)
                        .FirstOrDefault(x => x.Id == User.AdministradoraId)?.PostoCombustivelProduto?.FirstOrDefault(x =>
                            x.CodigoProduto == cProdXml);

                    if (produto == null)
                    {
                        throw new Exception("Código de produto "+ lXmlDados.GetElementsByTagName("xProd")[i].InnerText +" divergente ou não cadastrado!");
                    }
                }

                var valorXml = Convert.ToDecimal(Math.Truncate(lXmlDados.GetElementsByTagName("vNF")[0].InnerText.Replace(".",",").ToDecimal() * 1000) / 1000);

                #endregion
                
                return new CarregarDadosXmlResponse
                {
                    ValorXml = valorXml,
                    QtdLitrosXml = qtdLitrosXml,
                    ValorUnitario = valorUnitarioXml,
                    Sucesso = true,
                    Mensagem = ""
                };
            }
            catch (Exception e)
            {
                return new CarregarDadosXmlResponse()
                {
                    Sucesso = false,
                    Mensagem = e.Message
                };
            }
        }
        
        private bool ValidarMargem(decimal total, decimal margemTotal, decimal totalXml)
        {
            var valorMinimo = total - margemTotal;
            var valorMaximo = total + margemTotal;
            return totalXml >= valorMinimo && totalXml <= valorMaximo;
        }
        
        private async Task<Domain.Models.Posto.Posto> CarregarPosto(int pAdministradoraId)
        {
           return  await _postoRepository.Where(x => x.Id == pAdministradoraId)
                .Include(x => x.PostoCombustivelProduto)
                .ThenInclude(x => x.Combustivel)
                .FirstOrDefaultAsync();
        }
        
        private (decimal valorTotalItem, decimal totalLitros) CalcularTotais(IEnumerable<AbastecimentosValidaXml> abastecimentos)
        {
            var valorTotalItem = abastecimentos.Sum(x => x.Valor);
            var totalLitros = abastecimentos.Sum(x => x.Litros);
            return (valorTotalItem, totalLitros);
        } 
        
        private bool PertenceAoAbastecimento(decimal valor, decimal qtdTributo, decimal margemValorUnitario)
        {
            var valorMinimo = valor - margemValorUnitario;
            var valorMaximo = valor + margemValorUnitario;
            return qtdTributo >= valorMinimo && qtdTributo <= valorMaximo;
        }

        private bool MesmoCombustivelQtdUnitarioDiferente(List<ItensXmlSap> itensXML, string codigo)
        {
            var itensComMesmoCodigo = itensXML.Where(item => item.CodigoProduto == codigo).ToList();
            // Verifica se há valores unitários diferentes
            return itensComMesmoCodigo.Select(item => item.QtdTributo).Distinct().Count() > 1;

        }
        
        public static string LogErrorWithJson(string message, object data) {
            var jsonData = JsonConvert.SerializeObject(data);
            LogManager.GetCurrentClassLogger().Error(message + " : " + jsonData);
            return message;
        }
        
        public async Task<RespPadrao> ValidarXmlNota(ItensAbastecimentoValidarXml itensValidacao, int postoId)
        {
            try
            {
                var erroValorUnitario = false;
                var mensagemErro = new RetornoErroValoresProtocolo();

                #region getDadosXml e lista de produtos posto

                var lPosto = await CarregarPosto(User.AdministradoraId);
                var lCodigoProduto = lPosto.PostoCombustivelProduto.AsEnumerable();
                var eXml = itensValidacao.Xml.CarregarDocumento(); 

                #region Construcao de itensXML variavel de comparacao

                var itensXML = eXml.CarregarItensSap();
                
                #endregion

                #region Get total da nota e Abastecimentos

                var totalXml = eXml.CarregarTotalXml();
                var totalAbastecimentos = itensValidacao.Abastecimentos.Sum(x => x.Valor);
                var lMargemTotal = await _parametrosReadRepository.GetMargemTotalAbastecimentoXml();
                
                if (!ValidarMargem(totalAbastecimentos, lMargemTotal, totalXml))
                {
                    return new RespPadrao()
                    {
                        mensagem = $"O valor total não está de acordo com o(s) abastecimento(s) selecionado(s). Total Abastecimento:  {totalAbastecimentos} - TotalXml: {totalXml}",
                        sucesso = false
                    };
                }

                #endregion

                #region Get total Litros Nota e Abastecimento

                var totalLitrosXml = itensXML.Sum(x => (x.Litros));
                var totalLitrosProtocolo = itensValidacao.Abastecimentos.Sum(x => (x.Litros));

                var lMargemLitragem = await _parametrosReadRepository.GetMargemLitragemXml();
                lMargemLitragem = lMargemLitragem == 0 ?  0.002m : lMargemLitragem;
                
                if (!ValidarMargem(totalLitrosProtocolo, lMargemLitragem, totalLitrosXml)) {
                    return new RespPadrao()
                    {
                        mensagem =
                            "Quantidade total de litros não está de acordo com o(s) abastecimento(s) selecionado(s)!",
                        sucesso = false
                    };
                }

                #endregion

                #endregion
                
                var lMargem = await _parametrosReadRepository.GetMargemErroXml();
                    
                var valorPadraoMargem = 0.002m;

                lMargem = lMargem == 0 ? valorPadraoMargem : lMargem;
                
                if (itensXML.Count >= 1)
                {
                    foreach (var item in itensXML)
                    {
                        decimal valorLitro = 0;
                        var postoCombustivelProduto = lCodigoProduto
                            .First(c => c.CodigoProduto == item.CodigoProduto);
                        
                        var abastecimento = itensValidacao.Abastecimentos.Where(i => i.CodCombustivel == postoCombustivelProduto.Combustivel.Id);
                        
                        if (abastecimento.IsEmpty())
                        {
                            return new RespPadrao()
                            {
                                sucesso = false,
                                mensagem = LogErrorWithJson("Nenhum abastecimento encontrado com o código do combustível. Verifique se o código no XML ou o identificador nos abastecimentos está correto.", itensValidacao)
                            };
                        }
                        var rangeVuniTrib =  _parametrosReadRepository.GetMargemErroXml().Result;
                        
                        if (abastecimento.Count() > 1 && MesmoCombustivelQtdUnitarioDiferente(itensXML, item.CodigoProduto))
                            abastecimento = abastecimento.Where(i => PertenceAoAbastecimento(i.ValorUnit, item.ValorUnitarioTributo, rangeVuniTrib));
                        
                        var (valorTotalItem, totalLitros) = CalcularTotais(abastecimento);
                        
                        valorPadraoMargem = 0.002m;

                        rangeVuniTrib = rangeVuniTrib == 0 ? valorPadraoMargem : rangeVuniTrib;
                        
                        if (totalLitros > 0)
                        {
                            valorLitro = valorTotalItem / totalLitros;
                        }

                        var valorLitroXml = item.ValorDesconto != 0 ? item.ValorUnitarioTributo - (item.ValorDesconto / item.Litros) : item.ValorUnitarioTributo;
                        
                        if (Math.Abs(valorTotalItem - (item.QtdTributo ?? 0m - item.ValorDesconto)) <= lMargemTotal 
                            && (valorLitro <= valorLitroXml + rangeVuniTrib)
                            && (valorLitro >= valorLitroXml - rangeVuniTrib))
                        {
                            abastecimento.ForEach(abast =>
                            {
                                abast.nItem = item.nItem.ToInt();
                                abast.nitemobj = item;
                            });
                        }
                        else
                        {
                            valorLitro = Math.Round(valorLitro.ToDecimal(), 3);
                            item.ValorUnitarioTributo = Math.Round(item.ValorUnitarioTributo.ToDecimal(), 2);
                            
                            if (Math.Abs(valorLitro - item.ValorUnitarioTributo) > lMargem)
                            {
                                erroValorUnitario = true;
                                mensagemErro.nitemobj = item;
                                mensagemErro.nItem = item.nItem;
                                mensagemErro.Descricao = item.Descricao;
                                mensagemErro.CodigoProduto = item.CodigoProduto;
                                break;
                            }
                        }
                    }
                }
                else
                {
                    return new RespPadrao() {
                    mensagem = LogErrorWithJson("Nenhum item encontrado na nota fiscal!", itensValidacao)
                    };
                }
                
                if (erroValorUnitario) {
                    return new RespPadrao()
                    {
                        sucesso = false,
                        mensagem = LogErrorWithJson(
                            "O valor unitário do item " + mensagemErro.nItem + " - " + mensagemErro.Descricao
                            + " não está de acordo com o(s) abastecimento(s) selecionado(s)!", itensValidacao)
                    };
                }
                
                if (itensValidacao.Abastecimentos.Any(abatescimento => abatescimento.nItem == null))
                {
                    return new RespPadrao()
                    {
                        sucesso = false,
                        mensagem = LogErrorWithJson(
                            "Alguns abastecimentos não puderam ser relacionados automaticamente.", itensValidacao),
                        data = new ProtocoloAbastecimentoErro {itensXML = itensXML, Abastecimentos = itensValidacao.Abastecimentos}
                    };
                }

                return new RespPadrao()
                {
                    sucesso = true,
                    mensagem = "Nota validada com sucesso!",
                    data = itensValidacao.Abastecimentos
                };
            }
            catch (Exception e)
            {
                return new RespPadrao()
                {
                    sucesso = false,
                    mensagem = LogErrorWithJson(
                        "Erro ao validar relação de produto x nota, verifique as informações enviadas", itensValidacao)
                };
            }
        }
        
        [Obsolete("Este método está obsoleto. Use o ValidarXmlNota() em vez disso.")]
        public async Task<RespPadrao> ValidaXmlNota(ItensAbastecimentoValidaXml itensValidacao)
        {
            try
            {
                var erroValorTotal = false;
                var erroLitros = false;
                var erroValorUnitario = false;
                var mensagemErro = new RetornoErroValoresProtocolo();

                #region getDadosXml e lista de produtos posto

                var lPosto = await _postoRepository.Where(x => x.Id == User.AdministradoraId)
                    .Include(x => x.PostoCombustivelProduto)
                    .ThenInclude(x => x.Combustivel)
                    .FirstOrDefaultAsync();
                
                var lCodigoProduto = lPosto.PostoCombustivelProduto.AsEnumerable();

                var eXml = new XmlDocument();
                eXml.LoadXml(Encoding.UTF8.GetString(Convert.FromBase64String(itensValidacao.Xml)));

                #region Construcao de itensXML variavel de comparacao

                var itensXML = eXml.CarregarItensSap();
                
                #endregion

                #region Get total da nota e Abastecimentos

                var totalXml = eXml.CarregarTotalXml();
                var totalAbastecimentos = itensValidacao.Abastecimentos.Sum(x => x.Valor);
                var lMargemTotal = await _parametrosReadRepository.GetMargemTotalAbastecimentoXml();

                if (!ValidarMargem(totalAbastecimentos, lMargemTotal, totalXml))
                {
                    return new RespPadrao()
                    {
                        mensagem = "O valor total não está de acordo com o(s) abastecimento(s) selecionado(s)!",
                        sucesso = false
                    };
                }

                #endregion

                #region Get total Litros Nota e Abastecimento

                var totalLitrosXml = itensXML.Sum(x => (x.Litros));
                var totalLitrosProtocolo = itensValidacao.Abastecimentos.Sum(x => (x.Litros));

                var lMargemLitragem = await _parametrosReadRepository.GetMargemLitragemXml();
                lMargemLitragem = lMargemLitragem == 0 ?  0.002m : lMargemLitragem;
                
                if (!ValidarMargem(totalLitrosProtocolo, lMargemLitragem, totalLitrosXml)) {
                    return new RespPadrao()
                    {
                        mensagem =
                            "Quantidade total de litros não está de acordo com o(s) abastecimento(s) selecionado(s)!",
                        sucesso = false
                    };
                }

                #endregion

                #endregion
                
                var lMargem = await _parametrosReadRepository.GetMargemErroXml();
                    
                var valorPadraoMargem = 0.002m;

                lMargem = lMargem == 0 ? valorPadraoMargem : lMargem;
                
                if (itensValidacao.PrimeiraVerificacao)
                {
                    var itensRepetidos = itensXML.GroupBy(c => new {c.ValorUnitarioTributo, c.CodigoProduto})
                        .Where(g => g.Count() > 1).Select(c => c.First()).ToList();

                    if (itensRepetidos.Any())
                    {
                        return new RespPadrao()
                        {
                            sucesso = false,
                            mensagem = "Relacione o(s) abastecimento(s) com o(s) respectivo(s) item(s) da nota fiscal!" + "Itens Repedidos",
                            data = itensXML
                        };
                    }

                    
                    if (itensXML.Count == 1)
                    {
                        foreach (var item in itensXML)
                        {
                            decimal valorLitro = 0;
                            var combustivelNome = lCodigoProduto
                                .Where(c => c.CodigoProduto == item.CodigoProduto.ToString())
                                .Select(s => s.Combustivel.Nome).First();

                            var abastecimento =
                                itensValidacao.Abastecimentos.Where(i => i.Combustivel == combustivelNome);

                            var valorTotalItem = abastecimento.Sum(x => x.Valor);

                            if (itensValidacao.Abastecimentos[0].Combustivel != combustivelNome)
                            {
                                return new RespPadrao()
                                {
                                    sucesso = false,
                                    mensagem = "O combustivel " + itensValidacao.Abastecimentos[0].Combustivel +
                                               ", não está de acordo com o(s) abastecimento(s) selecionado(s)!"
                                };
                            }

                            var totalLitros = abastecimento.Sum(x => x.Litros);
                            decimal rangeVuniTrib =  _parametrosReadRepository.GetMargemErroXml().Result;
                    
                            valorPadraoMargem = 0.002m;

                            rangeVuniTrib = rangeVuniTrib == 0 ? valorPadraoMargem : rangeVuniTrib;
                            
                            if (totalLitros > 0)
                            {
                                valorLitro = valorTotalItem / totalLitros;
                            }

                            var valorLitroXml = item.ValorDesconto != 0 ? item.ValorUnitarioTributo - (item.ValorDesconto / item.Litros) : item.ValorUnitarioTributo;

                            if (valorTotalItem == (item.QtdTributo - item.ValorDesconto) 
                                && (valorLitro <= valorLitroXml+rangeVuniTrib)
                                && (valorLitro >= valorLitroXml-rangeVuniTrib))
                            {
                                foreach (var abast in itensValidacao.Abastecimentos)
                                {
                                    abast.nItem = item.nItem.ToInt();
                                }
                            }
                            else
                            {
                                if (valorTotalItem != item.QtdTributo)
                                {
                                    return new RespPadrao()
                                    {
                                        sucesso = false,
                                        mensagem = "O valor total referente ao item: " +
                                                   itensValidacao.Abastecimentos[0].Combustivel +
                                                   ", não está de acordo com o(s) abastecimento(s) selecionado(s)!"
                                    };
                                }

                                valorLitro = Math.Round(valorLitro.ToDecimal(), 3);
                                item.ValorUnitarioTributo = Math.Round(item.ValorUnitarioTributo.ToDecimal(), 2);
                                
                                if (Math.Abs(valorLitro - item.ValorUnitarioTributo) > lMargem)
                                {
                                    erroValorUnitario = true;
                                    mensagemErro.nItem = item.nItem;
                                    mensagemErro.Descricao = item.Descricao;
                                    mensagemErro.CodigoProduto = item.CodigoProduto;
                                    break;
                                }
                            }
                        }
                    }
                    else
                    {
                        if (itensXML.Count() != itensValidacao.Abastecimentos.Count())
                        {
                            return new RespPadrao()
                            {
                                sucesso = false,
                                mensagem =
                                    "Quantidade de itens presentes na nota fiscal e de abastecimento(s) selecionado(s) difere(m). " +
                                    "Relacione o(s) abastecimento(s) com o(s) respectivo(s) item(s) da nota fiscal!",
                                data = itensXML
                            };
                        }

                        foreach (var abastecimento in itensValidacao.Abastecimentos)
                        {
                            var codigoProduto = "";
                            decimal valorUnitarioItemXml = 0; 
                            
                            foreach (var itemCodProd in itensXML)
                            {
                                var postoCombustivelProduto =
                                    _postoCombustivelProdutoReadRepository.First(x => x.CodigoProduto == itemCodProd.CodigoProduto);
                                
                                if (postoCombustivelProduto?.CodigoProduto == itemCodProd.CodigoProduto)
                                {
                                    if(Math.Round(((abastecimento.Valor / abastecimento.Litros * 1000)/1000),2) == 
                                       Math.Round(((itemCodProd.ValorUnitarioTributo * 1000)/1000),2))
                                    {
                                        valorUnitarioItemXml = itemCodProd.ValorUnitarioTributo;
 
                                        codigoProduto = lCodigoProduto
                                            .Where(c => c.CodigoProduto == itemCodProd?.CodigoProduto)
                                            .Select(s => s.CodigoProduto).First(); 
                                    }
                                }
                            }
                            
                            decimal rangeVuniTrib =  _parametrosReadRepository.GetMargemErroXml().Result;
                    
                            valorPadraoMargem = 0.002m;

                            rangeVuniTrib = rangeVuniTrib == 0 ? valorPadraoMargem : rangeVuniTrib;

                            var ValorUnitario = Math.Round((abastecimento.Valor / abastecimento.Litros),2);

                            var itemXml = itensXML.Where(i =>
                                i.CodigoProduto == codigoProduto && i.ValorUnitarioTributo == ValorUnitario).ToList();
                            
                            if ((Math.Truncate(ValorUnitario * 1000) / 1000) !=
                                (Math.Truncate(valorUnitarioItemXml * 1000) / 1000)
                                && (Math.Truncate(ValorUnitario * 1000) / 1000 >
                                    (Math.Truncate(valorUnitarioItemXml * 1000) / 1000) + rangeVuniTrib)
                                || (Math.Truncate(ValorUnitario * 1000) / 1000 <
                                    (Math.Truncate(valorUnitarioItemXml * 1000) / 1000) - rangeVuniTrib))
                            {
                                return new RespPadrao()
                                {
                                    sucesso = false,
                                    mensagem =
                                        "Relacione o(s) abastecimento(s) com o(s) respectivo(s) item(s) da nota fiscal!" + 
                                        "Valor unitário do(s) abastecimento(s) diferente do XML anexado",
                                    data = itensXML
                                }; 
                            }

                            foreach (var item in itemXml)
                            {
                                if((Math.Round(((abastecimento.Valor / abastecimento.Litros * 1000)/1000),2) == 
                                    Math.Round(((item.ValorUnitarioTributo.ToDecimal() * 1000)/1000),2)))
                                {
                                    if (item.QtdTributo == abastecimento.Valor)
                                    {
                                        abastecimento.nItem = item.nItem.ToInt();
                                    }
                                    else
                                    {
                                        return new RespPadrao()
                                        {
                                            sucesso = false,
                                            mensagem =
                                                "Relacione o(s) abastecimento(s) com o(s) respectivo(s) item(s) da nota fiscal!" + 
                                                "Valor do(s) abastecimento(s) diferente do XML",
                                            data = itensXML
                                        };
                                    }
                                }
                            }
                        }
                    }
                }
                else
                {
                    foreach (var item in itensXML)
                    {
                        decimal valorLitro = 0;
                        var valorTotalItem = itensValidacao.Abastecimentos.Where(p => p.nItem == item.nItem)
                            .Sum(x => (x.Valor));

                        var totalLitros = itensValidacao.Abastecimentos.Where(p => p.nItem == item.nItem)
                            .Sum(x => (x.Litros));

                        if (totalLitros > 0)
                        {
                            valorLitro = valorTotalItem / totalLitros;
                        }
                        
                        if (valorTotalItem == (item.QtdTributo - item.ValorDesconto) 
                            && (valorLitro == valorTotalItem))
                            
                        //if (item.QtdTributo != valorTotalItem)
                        {
                            if (valorTotalItem == 0)
                            {
                                return new RespPadrao()
                                {
                                    sucesso = false,
                                    mensagem = "Item " + item.nItem + " de código " + item.CodigoProduto + " - " +
                                               item.Descricao + " não foi relacionado com nenhum abastecimento.",
                                    data = itensXML
                                };
                            }

                            erroValorTotal = true;
                            mensagemErro.nItem = item.nItem;
                            mensagemErro.Descricao = item.Descricao;
                            mensagemErro.CodigoProduto = item.CodigoProduto;
                            break;
                        }

                        if (item.Litros != totalLitros)
                        {
                            erroLitros = true;
                            mensagemErro.nItem = item.nItem;
                            mensagemErro.Descricao = item.Descricao;
                            mensagemErro.CodigoProduto = item.CodigoProduto;
                            break;
                        }

                        valorLitro = Math.Round(valorLitro.ToDecimal(), 3);
                        item.ValorUnitarioTributo = Math.Round(item.ValorUnitarioTributo.ToDecimal(), 3);

                        if (Math.Abs(valorLitro - item.ValorUnitarioTributo) > lMargem)
                        {
                            erroValorUnitario = true;
                            mensagemErro.nItem = item.nItem;
                            mensagemErro.Descricao = item.Descricao;
                            mensagemErro.CodigoProduto = item.CodigoProduto;
                            break;
                        }
                    }
                }

                if (erroValorTotal || erroLitros)
                {
                    var msgRetorno = erroLitros ? "Quantidade de litros" : "Valor do item";

                    return new RespPadrao()
                    {
                        sucesso = false,
                        mensagem = "Item " + mensagemErro.nItem + " de código " + mensagemErro.CodigoProduto + " - " +
                                   mensagemErro.Descricao + ". " + msgRetorno +
                                   " não corresponde com o(s) abastecimento(s) selecionado(s)!",
                    };
                }

                if (erroValorUnitario)
                {
                    return new RespPadrao()
                    {
                        sucesso = false,
                        mensagem = "O valor unitário do item " + mensagemErro.nItem + " - " + mensagemErro.Descricao
                                   + " não está de acordo com o(s) abastecimento(s) selecionado(s)!"
                    };
                }
                
                foreach (var abatescimento in itensValidacao.Abastecimentos)
                {
                    if (abatescimento.nItem == null)
                    {
                        return new RespPadrao()
                        {
                            sucesso = false,
                            mensagem = "O abastecimento " + abatescimento.CodOperacao +
                                       " não foi relacionado com nenhum item da nota fiscal.",
                            data = itensXML
                        };
                    }
                }

                return new RespPadrao()
                {
                    sucesso = true,
                    mensagem = "Nota validada com sucesso!",
                    data = itensValidacao.Abastecimentos
                };
            }
            catch (Exception e)
            {
                LogManager.GetCurrentClassLogger().Error(e);
                return new RespPadrao()
                {
                    sucesso = false,
                    mensagem = "Erro ao validar relação de produto x nota, verifique as informações"
                };
            }
        }

        public async Task<RespPadrao> SalvarRelacaoProtocoloAbastecimento(int abastecimentoId, int itemXmlNota, int protocoloId)
        {
            try
            {
                var lAbastecimento = _abastecimentoRepository.GetById(abastecimentoId);

                var command = Mapper.Map<AbastecimentoSalvarCommand>(lAbastecimento);

                _abastecimentoRepository.Detach(lAbastecimento);

                command.ProtocoloAbastecimentoId = protocoloId;
                command.NumeroItemXmlNota = itemXmlNota;


                await Engine.CommandBus
                    .SendCommandAsync<Domain.Models.Abastecimento.Abastecimento>(command);

                return new RespPadrao()
                {
                    sucesso = true,
                    mensagem = "Relação salva com sucesso."
                };

            }
            catch (Exception e)
            {
                LogManager.GetCurrentClassLogger().Error(e);
                throw;
            }
        }

        public RespPadrao ValidarRelacaoAbastecimentoNota(ValidacaoXmlRequest lValidacaoXmlRequest)
        {
            #region Construção de XML para validação de relação
            
            var eXml = new XmlDocument();
            eXml.LoadXml(Encoding.UTF8.GetString(Convert.FromBase64String(lValidacaoXmlRequest.XML)));

            var itensXML = eXml.CarregarItensSap();
            
            #endregion

            #region Validações

            var erroRelacional = 0;

            if (itensXML.Count == 1)
            {
                var valorTotalAbastecimentos = lValidacaoXmlRequest.AbastecimentosSelecionados.Sum(x => x.Data.ValorAbastecido);
                var litroTotalAbastecimentos = lValidacaoXmlRequest.AbastecimentosSelecionados.Sum(x => x.Data.QtdLitros);
                
                var valorAbastItemXml = 
                    Convert.ToDecimal(
                        (Math.Truncate(itensXML[0].QtdTributo.ToDecimal() * 1000) / 1000) 
                        - Math.Truncate(itensXML[0].ValorDesconto.ToDecimal() * 1000) / 1000);

                var rangeVuniTrib =  _parametrosReadRepository.GetMargemErroXml().Result;
                rangeVuniTrib = rangeVuniTrib == 0 ? 0.002m : rangeVuniTrib;

                if (!ValidarMargem((Math.Truncate(valorTotalAbastecimentos * 1000) / 1000), rangeVuniTrib,
                        (Math.Truncate(valorAbastItemXml * 1000) / 1000)))
                {
                    erroRelacional = erroRelacional + 1; 
                }

                // if ((Math.Truncate(valorTotalAbastecimentos * 1000) / 1000) 
                //     != (Math.Truncate(valorAbastItemXml * 1000) / 1000))
                // {
                //     erroRelacional = erroRelacional + 1;
                // }
                
                var rangeVLitros =  _parametrosReadRepository.GetMargemLitragemXml().Result;
                rangeVLitros = rangeVLitros == 0 ?  0.002m : rangeVLitros;
                
                if (!ValidarMargem((Math.Truncate(litroTotalAbastecimentos * 1000) / 1000), rangeVLitros, (Math.Truncate(itensXML[0].Litros * 1000) / 1000))) {
                    erroRelacional = erroRelacional + 1;
                }
            }
            else
            {
                var rangeVuniTrib =  _parametrosReadRepository.GetMargemErroXml().Result;
                var rangeVLitros =  _parametrosReadRepository.GetMargemLitragemXml().Result;
                    
                var valorPadraoMargem = 0.002m;

                rangeVuniTrib = rangeVuniTrib == 0 ? valorPadraoMargem : rangeVuniTrib;
                foreach (var abastecimentoValidar in lValidacaoXmlRequest.AbastecimentosSelecionados)
                {
                    var valorTotalAbastecimento = lValidacaoXmlRequest.AbastecimentosSelecionados
                        .Select(x => new
                        {
                            x.Data.ValorAbastecido,
                            x.Data.nItem
                        })
                        .Where(x => x.nItem == abastecimentoValidar.Data.nItem)
                        .Sum(x => x.ValorAbastecido);
                    
                    var litroTotalAbastecimentos = lValidacaoXmlRequest.AbastecimentosSelecionados
                        .Select(x => new
                        {
                            x.Data.QtdLitros,
                            x.Data.nItem
                        })
                        .Where(x => x.nItem == abastecimentoValidar.Data.nItem)
                        .Sum(x => x.QtdLitros);
                    
                    foreach (var xmlValidar in itensXML)
                    {
                        if (abastecimentoValidar.Data.nItem == xmlValidar.nItem)
                        {
                            var valorAbastItemXml = 
                                Convert.ToDecimal(
                                    (Math.Truncate(xmlValidar.QtdTributo.ToDecimal() * 1000) / 1000) 
                                    - Math.Truncate(xmlValidar.ValorDesconto.ToDecimal() * 1000) / 1000);
                            
                            if (!ValidarMargem((Math.Truncate(valorTotalAbastecimento * 1000) / 1000),rangeVuniTrib, (Math.Truncate(valorAbastItemXml * 1000) / 1000)))
                            {
                                erroRelacional = erroRelacional + 1;
                            }
                            // if ((Math.Truncate(valorTotalAbastecimento * 1000) / 1000)
                            //     != (Math.Truncate(valorAbastItemXml * 1000) / 1000))
                            // {
                            //     erroRelacional = erroRelacional + 1;
                            // }

                            var valorUnitarioCalculado = Convert.ToDecimal((xmlValidar.QtdTributo - xmlValidar.ValorDesconto) / xmlValidar.Litros);
                            
                            if ((Math.Truncate(abastecimentoValidar.Data.ValorUnitario * 1000) / 1000) != (Math.Truncate(valorUnitarioCalculado * 1000) / 1000)
                                && (Math.Truncate(abastecimentoValidar.Data.ValorUnitario * 1000) / 1000 > (Math.Truncate(valorUnitarioCalculado * 1000) / 1000) + rangeVuniTrib)
                                || (Math.Truncate(abastecimentoValidar.Data.ValorUnitario * 1000) / 1000 < (Math.Truncate(valorUnitarioCalculado * 1000) / 1000) - rangeVuniTrib))
                            {
                                return new RespPadrao()
                                {
                                    sucesso = false,
                                    mensagem = "O valor unitário do item " + xmlValidar.nItem + " - " + xmlValidar.Descricao
                                               + " não está de acordo com o(s) abastecimento(s) selecionado(s)!"
                                };
                            }

                            rangeVLitros = rangeVLitros == 0 ?  0.002m : rangeVLitros;
                
                            if (!ValidarMargem((Math.Truncate(litroTotalAbastecimentos * 1000) / 1000), rangeVLitros, (Math.Truncate(xmlValidar.Litros * 1000) / 1000))) {
                                erroRelacional = erroRelacional + 1;
                            }
                        }

                    }
                }
            }

            #endregion

            if (erroRelacional > 0)
            {
                return new RespPadrao()
                {
                    sucesso = false
                };
            }

            return new RespPadrao()
            {
                sucesso = true
            };

        }
        public ValidarXmlReponse ValidarXml(ValidacaoXmlRequest lValidacaoXmlRequest)
        {
            var validarRelacao = ValidarRelacaoAbastecimentoNota(lValidacaoXmlRequest);

            if (!validarRelacao.sucesso)
            {
                if (!validarRelacao.mensagem.IsNullOrWhiteSpace())
                {
                    return new ValidarXmlReponse()
                    {
                        Sucesso = false,
                        Mensagem = validarRelacao.mensagem
                    };
                }
                else
                {
                    return new ValidarXmlReponse()
                    {
                        Sucesso = false,
                        Mensagem = "Processo cancelado! Relação de produtos x nota inconsistente!"
                    };
                }
            }
                
            XmlDocument lXmlDados = new XmlDocument();
            List<ItemXmlNfe> lItensXml;
            var cnpjsSelecionados = new HashSet<string>();

            foreach (var cnpjs in lValidacaoXmlRequest.AbastecimentosSelecionados)
            {
                cnpjsSelecionados.Add(cnpjs.Data.CnpjAFaturar);
            }

            foreach (var cnpjFaturamento in cnpjsSelecionados)
            {
                var recusaXml = 0;
                var mensagem = "";
            
                var lXml = GetXml(lValidacaoXmlRequest);
                lXmlDados.LoadXml(Encoding.UTF8.GetString(Convert.FromBase64String(lXml)));
                lItensXml = lXmlDados.GetItensNFe().ToList();
                
                var valorTotalAbastecimentosSelecionados =
                    GetValorTotalAbastecimentosSelecionados(lValidacaoXmlRequest.AbastecimentosSelecionados,
                        cnpjFaturamento);
                var nomesCombustiveisSelecionados =
                    GetNomesCombustiveisSelecionados(lValidacaoXmlRequest.AbastecimentosSelecionados, cnpjFaturamento)
                        .ToList();
                var nomesCombustiveisXml = GetNomesCombustiveisNFe(lItensXml).ToList();
                var quantidadeLitrosSelecionados =
                    GetQtdLitrosSelecionados(lValidacaoXmlRequest.AbastecimentosSelecionados, cnpjFaturamento);
                var quantidadeLitrosXml = GetQtdLitrosNFe(lItensXml);
                var cnpjPostoLogado = _postoRepository.GetCnpj(lValidacaoXmlRequest.PostoId);
                var cnpjPostoXml = lXmlDados.GetElementsByTagName("CNPJ")[0].InnerText;
                var cnpjDestinatarioXml = lXmlDados.GetElementsByTagName("CNPJ")[1].InnerText;

                var cnpjAFaturar = cnpjFaturamento;
                
                var valorXml = Convert.ToDecimal(ConverterXmlParaDecimaRegrado(lXmlDados.GetElementsByTagName("vNF")[0].InnerText).data);
                
                lValidacaoXmlRequest.NotaFiscal = Convert.ToString(lXmlDados.GetElementsByTagName("nNF")[0].InnerText);

                var produtoSemNItem = false;
                
                var lMargemLitragem =  _parametrosReadRepository.GetMargemLitragemXml().Result;
                lMargemLitragem = lMargemLitragem == 0 ?  0.002m : lMargemLitragem;
                
                // Validações gerais por CNPJ
                // Filtro 1: Quantidade de abastecimentos
                foreach (var abastecimentoIndividual in lValidacaoXmlRequest.AbastecimentosSelecionados)
                {
                    if (abastecimentoIndividual.Data.nItem == 0)
                    {
                        produtoSemNItem = true;
                    }
                }

                var lMargemTotal = _parametrosReadRepository.GetMargemTotalAbastecimentoXml().Result;
                if ((lValidacaoXmlRequest.AbastecimentosSelecionados.Count != lItensXml.Count) && produtoSemNItem)
                {
                    recusaXml = 1;
                    mensagem = "A quantidade de abastecimentos do XML difere das selecionadas em tela";
                }
                // Filtro 2: Valor total dos abastecimentos
                else if (!ValidarMargem(valorTotalAbastecimentosSelecionados, lMargemTotal, valorXml) )
                {
                    recusaXml = 1;
                    mensagem = "O valor total dos abastecimentos do XML difere dos selecionados em tela";
                }
                // Filtro 3: Quantidade de litros total dos abastecimentos
                else if (!ValidarMargem(quantidadeLitrosSelecionados, lMargemLitragem, quantidadeLitrosXml)) // (quantidadeLitrosSelecionados != quantidadeLitrosXml)
                {
                    recusaXml = 1;
                    mensagem = "A quantidade de litros dos combustíveis do XML difere dos selecionados em tela";
                }
                // Filtro 5: CNPJ do emissor em relação ao CNPJ do posto
                else if ((cnpjPostoLogado != cnpjPostoXml))
                {
                    recusaXml = 1;
                    mensagem = "O CNPJ do emissor da nota difere do CNPJ do posto";
                }
                // Filtro 6: CNPJ do destinatário em relação ao CNPJ a faturar
                else if ((cnpjDestinatarioXml != cnpjAFaturar))
                {
                    recusaXml = 1;
                    mensagem = "O CNPJ do destinatário da NFe difere do CNPJ a faturar";
                }
            
                if (recusaXml == 1)
                {
                    return new ValidarXmlReponse()
                    {
                        Sucesso = false,
                        Mensagem = mensagem
                    };
                }
                
            }

            #region Salvamento de relacao de abastecimento para protocolo
            
            var retornoSaveProtocolo  = SalvarProtocoloAbastecimento(lValidacaoXmlRequest.AbastecimentosSelecionados, lValidacaoXmlRequest.NotaFiscal, lValidacaoXmlRequest.XML, lValidacaoXmlRequest.PDF).Result;

            if (!retornoSaveProtocolo.sucesso)
            {
                return new ValidarXmlReponse()
                {
                    Sucesso = false,
                    Mensagem = retornoSaveProtocolo.mensagem ?? "Protocolo(s) não registrado(S)!"
                };
            }

            #endregion

            return new ValidarXmlReponse()
            {
                Sucesso = true,
                Mensagem = "Protocolo(s) salvo(s) com sucesso!"
            };
        }

        private bool XmlIsValid(XmlDocument lXmlDados)
        {
            var lItensXml = lXmlDados.GetItensNFe().ToList();
            foreach (var abastecimento in lItensXml)
            {
                if (abastecimento.cProd.IsNullOrWhiteSpace())
                    return false;
                if (abastecimento.nQtd.IsNullOrWhiteSpace())
                    return false;
                if (abastecimento.qCom.IsNullOrWhiteSpace())
                    return false;
                if (abastecimento.xProd.IsNullOrWhiteSpace())
                    return false;
                if (abastecimento.vUnCom.IsNullOrWhiteSpace())
                    return false;
                if (abastecimento.vProd.IsNullOrWhiteSpace())
                    return false;
            }
            return true;
        }

        private string GetXml(ValidacaoXmlRequest validacaoXmlRequest)
        {
            if (validacaoXmlRequest.XML != null)
            {
                return validacaoXmlRequest.XML;
            }
            throw new Exception("Xml não encontrado");
        }

        private IEnumerable<string> GetNomesCombustiveisNFe(List<ItemXmlNfe> lItensXml)
        {
            var listaNomesCombustiveisNfe = new List<string>();

            foreach (var nomesCombustiveisNfe in lItensXml)
            {
                listaNomesCombustiveisNfe.Add(nomesCombustiveisNfe.xProd);
            }

            return listaNomesCombustiveisNfe;
        }
        
        private decimal GetQtdLitrosNFe(List<ItemXmlNfe> lItensXml)
        {
            decimal qtdLitrosNfe = 0;

            foreach (var litrosNfe in lItensXml)
            {
                qtdLitrosNfe += Convert.ToDecimal(ConverterXmlParaDecimaRegrado(litrosNfe.qCom).data);
            }

            return qtdLitrosNfe;
        }
        
        private decimal GetQtdLitrosSelecionados(List<VetorAbastecimentoRequest> abastecimentosSelecionados,
            string cnpjFaturamento)
        {
            decimal qtdLitrosSelecionados = 0;

            foreach (var litrosSelecionados in abastecimentosSelecionados)
            {   
                if (litrosSelecionados.Data.CnpjAFaturar == cnpjFaturamento)
                    qtdLitrosSelecionados += litrosSelecionados.Data.QtdLitros;
            }

            return qtdLitrosSelecionados;
        }

        private decimal GetValorTotalAbastecimentosSelecionados(
            List<VetorAbastecimentoRequest> abastecimentosSelecionados, string cnpjFaturamento)
        {
            decimal valorTotalAbastecimentosSelecionados = 0;
            
            foreach (var valorAbastecimento in abastecimentosSelecionados)
            {
                if (valorAbastecimento.Data.CnpjAFaturar == cnpjFaturamento) 
                    valorTotalAbastecimentosSelecionados += valorAbastecimento.Data.ValorAbastecido;
            }

            return valorTotalAbastecimentosSelecionados;
        }
        
        private IEnumerable<string> GetNomesCombustiveisSelecionados(
            List<VetorAbastecimentoRequest> abastecimentosSelecionados, string cnpjFaturamento)
        {
            var listaNomesCombustiveis = new List<string>();
            
            foreach (var nomesCombustiveis in abastecimentosSelecionados)
            {   
                if (nomesCombustiveis.Data.CnpjAFaturar == cnpjFaturamento)
                    listaNomesCombustiveis.Add(nomesCombustiveis.Data.Combustivel);
            }

            return listaNomesCombustiveis;
        }
        
        public async Task<RespPadrao> SalvarProtocoloAbastecimento(List<VetorAbastecimentoRequest> lProtocoloAbastecimentoRequest, string notaFiscal, string xml, string pdf)
        {
            try
            {
                var command = Mapper.Map<ProtocoloAbastecimentoSalvarComRetornoCommand>(lProtocoloAbastecimentoRequest.FirstOrDefault()?.Data);

                command.PostoId = Engine.User.AdministradoraId;
                command.DiasMDR = _postoRepository.Where(x => x.Id == Engine.User.AdministradoraId)
                    .Include(x=>x.MdrPrazos).FirstOrDefault()?.MdrPrazos?.Prazo ?? 1;
                command.PercentualMDR = _postoRepository.Where(x => x.Id == Engine.User.AdministradoraId)
                    .Include(x=>x.MdrPrazos).FirstOrDefault()?.MdrPrazos?.MDR ?? 0;

                command.EmpresaId = _abastecimentoRepository.GetById(lProtocoloAbastecimentoRequest.FirstOrDefault()?.Data.AbastecimentoId).EmpresaId.ToInt(); 

                command.NotaFiscal = notaFiscal;
                command.Status = EStatusProtocolo.Aprovado;

                command.Xml = xml;
                command.Pdf = pdf;
                command.NotaXml = xml.NotaXml();
                command.ValorXml = xml.ValorXml();
                
                var retorno = await Engine.CommandBus.SendCommandAsync<Domain.Models.ProtocoloAbastecimento.ProtocoloAbastecimento>(command);
                var mensagem = "";

                if (retorno.Id > 0 && retorno.Status == EStatusProtocolo.Aprovado)
                {
                    foreach (var abastecimentoIndividual in lProtocoloAbastecimentoRequest)
                    {
                        var lResult = SalvarRelacaoProtocoloAbastecimento(abastecimentoIndividual.Data.AbastecimentoId,
                            abastecimentoIndividual.Data.nItem, retorno.Id).Result;
                        if (!lResult.sucesso)
                        {
                            await AtualizarStatus(retorno.Id, EStatusProtocolo.Reprovado);
                            
                            return new RespPadrao
                            {
                                id = retorno.Id,
                                sucesso = false,
                                mensagem = $"Erro ao vincular protocolo ao(s) abastecimento(s), protocolo reprovado automaticamente.!"
                            };
                        }
                    }
                    
                    var protocoloId = new ProtocoloPendenteRequest {IdProtocolo = retorno.Id};
                    
                    var lProtocolosAprovar = new List<ProtocoloPendenteRequest>();
                    lProtocolosAprovar.Add(protocoloId);
                    
                    await AprovarProtocoloPendente(lProtocolosAprovar);
                }
                
                return new RespPadrao
                {
                    id = retorno.Id,
                    sucesso = true,
                    mensagem = $"Registro salvo com sucesso! <br>{mensagem}"
                };
            }
            catch (Exception e)
            {
                return new RespPadrao()
                {
                    sucesso = false,
                    mensagem = e.Message
                };
            }
        }
        
         public ConsultarGridProtocoloResponse ConsultarGridPainelProtocoloAbastecimento(int empresaId, int status, DateTime dtInicial, DateTime dtFinal, int take, int page,
            OrderFilters orderFilters, List<QueryFilters> filters)
        {
            try
            {
                var lProtocoloAbastecimento = Repository.Query.GetAll();
                
                if (empresaId > 0 && User.EmpresaId == 0)
                {
                    lProtocoloAbastecimento = lProtocoloAbastecimento.Where(x => x.EmpresaId == empresaId);
                }
                else if (User.EmpresaId != 0)
                {
                    lProtocoloAbastecimento = lProtocoloAbastecimento.Where(x => x.EmpresaId == User.EmpresaId);
                }

                lProtocoloAbastecimento = lProtocoloAbastecimento.Where(x => x.Status.GetHashCode() == status);
                

                if (dtInicial > ConvertUtils.ToDateTime(("01/01/0001 00:00:00")) || dtFinal > ConvertUtils.ToDateTime(("01/01/0001 00:00:00")))
                {
                    var dtIni = ConvertUtils.ToDateTime(dtInicial.ToShortDateString());
                    var dtFim = ConvertUtils.ToDateTime(dtFinal.ToShortDateString()).AddDays(1).AddSeconds(-1);

                    lProtocoloAbastecimento =
                        lProtocoloAbastecimento.Where(p => p.DataCadastro >= dtIni && p.DataCadastro <= dtFim);
                    
                }

                lProtocoloAbastecimento = lProtocoloAbastecimento
                    .AplicarFiltrosDinamicos<Domain.Models.ProtocoloAbastecimento.ProtocoloAbastecimento>(filters);
                lProtocoloAbastecimento = string.IsNullOrWhiteSpace(orderFilters?.Campo)
                    ? lProtocoloAbastecimento.OrderByDescending(o => o.Id)
                    : lProtocoloAbastecimento.OrderBy(
                        $"{orderFilters.Campo} {orderFilters.Operador.DescriptionAttr()}");

                return new ConsultarGridProtocoloResponse
                {
                    Items = lProtocoloAbastecimento.Skip((page - 1) * take).Take(take).Select(x => new ConsultarGridProtocolo
                    {
                        Id = x.Id,
                        RazaoSocial = x.Posto.RazaoSocial == "" ? x.Posto.NomeFantasia : x.Posto.RazaoSocial,
                        Cnpj = x.Posto.Cnpj.ToCNPJFormato(),
                        DataCadastro = x.DataCadastro.FormatDateBr(),
                        QtdLitrosXml = x.QtdLitrosXml,
                        ValorXml = x.ValorXml.ToString("C2"),
                        PedidoSAP = x.NumeroPedidoSap,
                        NotaFiscal = x.NotaFiscal,
                        Status = x.Status.GetDescription(),
                        Xml = x.Xml,
                        Pdf = x.Pdf,
                        LotePagamentoId = x.LotePagamentoId
                    }).ToList(),
                    totalItems = lProtocoloAbastecimento.Count()
                };
            }
            catch (Exception e)
            {
                LogManager.GetCurrentClassLogger().Error(e);
                throw;
            }
        }
         
        public ConsultarGridProtocoloAbastecimentoRelatorio ConsultarGridPainelProtocoloAbastecimentoRelatorio(int empresaId, int status, DateTime dtInicial, DateTime dtFinal, int take, int page, OrderFilters orderFilters, List<QueryFilters> filters)
        {
            try
            {
                var query = Repository.Query.GetAll()
                    .Include(c => c.Abastecimentos)
                    .Include(c => c.Posto)
                    //.Include(c => c.Combustivel)
                    .AsQueryable();
                
                if (empresaId > 0 && User.EmpresaId == 0) {
                    query = query.Where(x => x.EmpresaId == empresaId);
                } else if (User.EmpresaId != 0) {
                    query = query.Where(x => x.EmpresaId == User.EmpresaId);
                }

                
                query = query.Where(x => x.Status.GetHashCode() == status);
                
                if (dtInicial > ConvertUtils.ToDateTime(("01/01/0001 00:00:00")) || dtFinal > ConvertUtils.ToDateTime(("01/01/0001 00:00:00")))
                {
                    var dtIni = ConvertUtils.ToDateTime(dtInicial.ToShortDateString());
                    var dtFim = ConvertUtils.ToDateTime(dtFinal.ToShortDateString()).AddDays(1).AddSeconds(-1);
                    query = query.Where(p => p.DataCadastro >= dtIni && p.DataCadastro <= dtFim);
                }

                query = query.OrderByDescending(x => x.Id);
                
                query = query.AplicarFiltrosDinamicos(filters);
                
                query = string.IsNullOrWhiteSpace(orderFilters?.Campo)
                    ? query.OrderByDescending(o => o.Id)
                    : query.OrderBy(
                        $"{orderFilters.Campo} {orderFilters.Operador.DescriptionAttr()}");
                
                var count = 0;
                
                var retorno = query
                    .Skip((page - 1) * take)
                    .Take(take)
                    .ToList();
                
                var retornoItens = new List<ConsultarGridProtocoloAbastecimentoRelatorioItem>();
                foreach (var pagamentoAbastecimento in retorno) {
                    if (!pagamentoAbastecimento.Abastecimentos.IsEmpty()) {
                        foreach (var abastecimento in pagamentoAbastecimento.Abastecimentos.OrderByDescending(a => a.Id))
                        {
                            var item = Mapper.Map<ConsultarGridProtocoloAbastecimentoRelatorioItem>(pagamentoAbastecimento);
                            item.AbastecimentoId = abastecimento.Id;
                            retornoItens.Add(item);
                            count++;
                        }
                    } else {
                        var item = Mapper.Map<ConsultarGridProtocoloAbastecimentoRelatorioItem>(pagamentoAbastecimento);
                        item.AbastecimentoId = 0;
                        retornoItens.Add(item);
                        count++;
                    }
                }
                
                return new ConsultarGridProtocoloAbastecimentoRelatorio
                {
                    Items = retornoItens, 
                    totalItems = count
                };
            }
            catch (Exception e)
            {
                LogManager.GetCurrentClassLogger().Error(e);
                throw;
            }
        }
         
         public async Task<RespPadrao> ReprovarProtocoloPendente(List<ProtocoloPendenteRequest> protocoloId)
         {
             try
             {
                 var atualizarStatus = new RespPadrao();
                
                 foreach (var lReprovaProtocolos in protocoloId)
                 {
                     atualizarStatus = await AtualizarStatus(lReprovaProtocolos.IdProtocolo, EStatusProtocolo.Reprovado);
                    
                 }
                 
                 if (atualizarStatus.sucesso)
                 {
                     return new RespPadrao
                     {
                         sucesso = true,
                         mensagem = "Registro reprovado com sucesso!"
                     };
                 }
                 
                 return new RespPadrao
                 {
                     sucesso = false,
                     mensagem = "Registro não reprovado!"
                 };
             }
             catch (Exception e)
             {
                 return new RespPadrao()
                 {
                     sucesso = false,
                     mensagem = e.Message
                 };
             }
         }

        
         public async Task<RespPadrao> AprovarProtocoloPendente(List<ProtocoloPendenteRequest> protocoloId)
         {
             try
             {
                 var atualizarStatus = new RespPadrao();
                 var contadorFalha = 0;
                 var erroSap = 0;
                 var mensagem = "";
                
                 foreach (var lAprovaProtocolos in protocoloId)
                 {
                     atualizarStatus = await AtualizarStatus(lAprovaProtocolos.IdProtocolo, EStatusProtocolo.Aprovado);

                     mensagem = mensagem == "" ? atualizarStatus.mensagem : mensagem + atualizarStatus.mensagem;

                     var protocoloAtulizado = Mapper.Map<Domain.Models.ProtocoloAbastecimento.ProtocoloAbastecimento>(atualizarStatus.data);
                     
                     contadorFalha =  atualizarStatus.sucesso ? contadorFalha : contadorFalha+1;
                     erroSap =  protocoloAtulizado?.IntegracaoSap == 1 ? erroSap : erroSap+1;
                 }

                 if (contadorFalha > 0 && erroSap > 0)
                 {
                     return new RespPadrao
                     {
                         sucesso = false,
                         data = erroSap,
                         mensagem = "Operação realizada! " + (erroSap > 0 ? mensagem : "")
                     };
                 }
                 
                 if (contadorFalha > 0)
                 {
                     return new RespPadrao
                     {
                         sucesso = false,
                         mensagem = "Operação não realizada!"
                     };
                 }
                 
                 return new RespPadrao
                 {
                     sucesso = true,
                     data = erroSap,
                     mensagem = "Operação realizada! " + mensagem
                 };
                 
             }
             catch (Exception e)
             {
                 return new RespPadrao()
                 {
                     sucesso = false,
                     mensagem = e.Message
                 };
             }
         }

         public async Task<RespPadrao> AtualizarStatus(int idProtocoloAbastecimento, EStatusProtocolo status)
         {
             try
             {
                 //Consulta de registro
                 var lProtocoloAbastecimentoUpdate =
                     Repository.Query.Include(x => x.Empresa)
                         .FirstOrDefault(x => x.Id == idProtocoloAbastecimento);
                 //Ajustes de valores do objeto
                 lProtocoloAbastecimentoUpdate.Status = status;
                 
                 //Pedido SAP
                 IntegracaoSapResponse retornoIntegracaoSap = null;
                 
                 if (lProtocoloAbastecimentoUpdate.Status == EStatusProtocolo.Aprovado && !lProtocoloAbastecimentoUpdate.Empresa.LinkSAP.IsNullOrWhiteSpace())
                 {
                     retornoIntegracaoSap = await IntegracaoSap(lProtocoloAbastecimentoUpdate.Id);
                     
                     if (!retornoIntegracaoSap.Sucesso)
                     {
                         status = EStatusProtocolo.Pendente;
                         
                         return new RespPadrao()
                         {
                             sucesso = false,
                             data = null,
                             mensagem = "<br>Protocolo " + idProtocoloAbastecimento + " não aprovado!" +
                             "<br>" + retornoIntegracaoSap.Mensagem
                         };
                     }

                     Domain.Models.ProtocoloAbastecimento.ProtocoloAbastecimento pagamentoabastecimentoAtualizado = retornoIntegracaoSap.Data != null 
                         ? Mapper.Map<Domain.Models.ProtocoloAbastecimento.ProtocoloAbastecimento>(retornoIntegracaoSap.Data) 
                         : null;

                     if (pagamentoabastecimentoAtualizado?.NumeroPedidoSap != null)
                     {
                         lProtocoloAbastecimentoUpdate.IntegracaoSap = pagamentoabastecimentoAtualizado.IntegracaoSap;
                         lProtocoloAbastecimentoUpdate.NumeroPedidoSap = pagamentoabastecimentoAtualizado.NumeroPedidoSap;
                         lProtocoloAbastecimentoUpdate.JsonEnvioSap = pagamentoabastecimentoAtualizado.JsonEnvioSap;
                         lProtocoloAbastecimentoUpdate.JsonRetornoSap = pagamentoabastecimentoAtualizado.JsonRetornoSap;
                     }

                 }

                 var mensagemSap = retornoIntegracaoSap != null
                     ? "<br>" + retornoIntegracaoSap?.Mensagem
                     : "";

                 //Mapper
                 var protocoloAbastecimentoSalvar =
                     Mapper.Map<ProtocoloAbastecimentoAlterarStatusCommand>(lProtocoloAbastecimentoUpdate);
                 //Fechamento de consulta
                 Repository.Query.Detach(lProtocoloAbastecimentoUpdate);

                 //Save command
                 var retorno = await Engine.CommandBus
                     .SendCommandAsync<Domain.Models.ProtocoloAbastecimento.ProtocoloAbastecimento>(
                         protocoloAbastecimentoSalvar);

                 //sucesso
                 if (retorno.Status == status)
                 {
                     #region Atualizar abastecimentos protocolo

                     var mensagemRetencao = "";

                     var lAbastecimentosProtocoloPrazo =
                         _abastecimentoRepository.GetAbastecimentosPrazoProtocolo(retorno.Id);

                     if (lAbastecimentosProtocoloPrazo.Count() > 0)
                     {
                         foreach (var abastecimentoProt in lAbastecimentosProtocoloPrazo)
                         {
                             var retornoAtualização =
                                 await _abastecimentoWriteRepository
                                     .UpdateDataPrazoPagamento(abastecimentoProt.Id);

                             if (!retornoAtualização)
                             {
                                 mensagemRetencao = mensagemRetencao == ""
                                     ? $"<br>Falha au atualizar data de prazo para abastecimento {abastecimentoProt.Id}"
                                     : $"{mensagemRetencao}. <br>Falha au atualizar data de prazo para abastecimento {abastecimentoProt.Id}";
                             }
                         }

                         var retornoRetencaoPrazo =
                             await _pagamentoAbastecimentoAppService.IntegrarRetencaoProtocolo(retorno.Id, true);

                         if (!retornoRetencaoPrazo.sucesso)
                         {
                             mensagemRetencao = mensagemRetencao == ""
                                 ? $"<br>Retenção gerada para o(s) abastecimento(s): {retornoRetencaoPrazo.id} " +
                                   $"com status pendente, motivo {retornoRetencaoPrazo.mensagem}"
                                 : $"{mensagemRetencao}. <br>Retenção gerada para o(s) abastecimento(s): " +
                                   $"{retornoRetencaoPrazo.id} com status pendente, motivo {retornoRetencaoPrazo.mensagem}";
                         }
                     }

                     var retornoRetencaoProtocolo =
                         await _pagamentoAbastecimentoAppService.IntegrarRetencaoProtocolo(retorno.Id);

                     if (!retornoRetencaoProtocolo.sucesso)
                     {
                         mensagemRetencao = mensagemRetencao == ""
                             ? $"<br>Retenção gerada para o(s) abastecimento(s): {retornoRetencaoProtocolo.id} " +
                               $"com status pendente, motivo {retornoRetencaoProtocolo.mensagem}"
                             : $"{mensagemRetencao}. <br>Retenção gerada para o(s) abastecimento(s): " +
                               $"{retornoRetencaoProtocolo.id} com status pendente, motivo {retornoRetencaoProtocolo.mensagem}";
                     }

                     #endregion

                     return new RespPadrao()
                     {
                         sucesso = true,
                         data = retorno,
                         mensagem =
                             $"<br>Protocolo {idProtocoloAbastecimento} atualizado para {status} com sucesso!<br>{mensagemSap}<br>{mensagemRetencao}"
                     };
                 }

                 return new RespPadrao()
                 {
                     sucesso = false,
                     data = retorno,
                     mensagem = $"<br>Protocolo {idProtocoloAbastecimento} não {status}!<br>{mensagemSap}"
                 };
                 
             }
             catch (Exception e)
             {
                 var lLog = LogManager.GetCurrentClassLogger();
                 lLog.Error(e, "Erro ao executar o processo AtualizarStatus de ProtocoloAbastecimento: ");
                 //Insucesso
                 return new RespPadrao()
                 {
                     sucesso = false,
                     data = null,
                     mensagem = "<br>Protocolo " + idProtocoloAbastecimento + " não " + status + "!"
                 };
             }
         }

         public async Task<IntegracaoSapResponse> IntegracaoSap(int protocoloId)
         {
             var protocoloAbastecimento = Repository.Query.Where(x => x.Id == protocoloId)
                 .Include(x => x.Empresa)
                 .FirstOrDefault();

             var pedidoSap = MontarPedidoSap(protocoloAbastecimento.Id, protocoloAbastecimento.Xml);
             var mensagemSap = "";
             bool sucessoSap = false;
             if (!pedidoSap.sucesso)
             {
                 protocoloAbastecimento.Status = EStatusProtocolo.Pendente;
                 
                 var protocoloAbastecimentoSalvarIntegracao =
                     Mapper.Map<ProtocoloAbastecimentoAlterarStatusCommand>(protocoloAbastecimento);

                 await Engine.CommandBus
                     .SendCommandAsync(protocoloAbastecimentoSalvarIntegracao);
                 
                 return new IntegracaoSapResponse
                 {
                     Mensagem = pedidoSap.mensagem,
                     Data = null,
                     Sucesso = pedidoSap.sucesso
                 };
             }

             SAPPedidoResp retornoGeracaoPedido = null;

             var empresa = _empresaReadRepository.FirstOrDefault(x => x.Id == protocoloAbastecimento.EmpresaId);

             if (empresa == null)
             {
                 return new IntegracaoSapResponse
                 {
                     Mensagem = "Empresa do protocolo abastecimento não encontrado.",
                     Data = null,
                     Sucesso = false
                 };
             }
             
             if (empresa.UsuarioSAP == null || empresa.SenhaSAP == null || empresa.LinkSAP == null)
             {
                 return new IntegracaoSapResponse
                 {
                     Mensagem = "Parametrização para integração com SAP inválido: configure corretamente os dados na empresa.",
                     Data = null,
                     Sucesso = false
                 };
             }

             retornoGeracaoPedido = await GerarPedidoSap(pedidoSap.data, empresa.LinkSAP, empresa.SenhaSAP,empresa.UsuarioSAP);

             if (retornoGeracaoPedido.PedidoResponse != null)
             {
                 protocoloAbastecimento.NumeroPedidoSap = retornoGeracaoPedido.PedidoResponse.Numero_pedido;
                 protocoloAbastecimento.JsonEnvioSap = JsonConvert.SerializeObject(pedidoSap.data);
                 protocoloAbastecimento.JsonRetornoSap =
                     JsonConvert.SerializeObject(retornoGeracaoPedido.PedidoResponse);
                 protocoloAbastecimento.IntegracaoSap =
                     !retornoGeracaoPedido.PedidoResponse.Numero_pedido.IsNullOrWhiteSpace() ? 1 : 0;

                 protocoloAbastecimento.Status = retornoGeracaoPedido?.PedidoResponse?.Numero_pedido == null 
                     ? EStatusProtocolo.Pendente : EStatusProtocolo.Aprovado;
                 
                 var protocoloAbastecimentoSalvarIntegracao =
                     Mapper.Map<ProtocoloAbastecimentoAlterarStatusCommand>(protocoloAbastecimento);
                 
                 await Engine.CommandBus
                     .SendCommandAsync(protocoloAbastecimentoSalvarIntegracao);

                 if (!retornoGeracaoPedido.PedidoResponse.Numero_pedido.IsNullOrWhiteSpace())
                 {
                     mensagemSap = "Pedido SAP para protocolo "
                                   + protocoloAbastecimento.Id
                                   + " gerado com sucesso!<br>Número do pedido: "
                                   + retornoGeracaoPedido.PedidoResponse.Numero_pedido;

                     sucessoSap = true;
                 }
                 else
                 {
                     mensagemSap = "Pedido SAP para protocolo "
                                   + protocoloAbastecimento.Id
                                   + " não gerado!<br><br>"
                                   + "Retorno SAP: "
                                   + retornoGeracaoPedido.PedidoResponse.Mensagem;
                 }
             }
             else
             {
                 protocoloAbastecimento.Status = EStatusProtocolo.Pendente;
                 protocoloAbastecimento.JsonEnvioSap = JsonConvert.SerializeObject(pedidoSap.data);
                 protocoloAbastecimento.JsonRetornoSap = JsonConvert.SerializeObject(retornoGeracaoPedido.PedidoResponse);

                 var protocoloAbastecimentoSalvarIntegracao =
                     Mapper.Map<ProtocoloAbastecimentoAlterarStatusCommand>(protocoloAbastecimento);

                 await Engine.CommandBus
                     .SendCommandAsync(protocoloAbastecimentoSalvarIntegracao);

                 mensagemSap = "Pedido SAP para protocolo "
                               + protocoloAbastecimento.Id
                               + " não gerado!"
                               + "<br>Motivo: " + retornoGeracaoPedido?.PedidoResponse?.Mensagem;
             }

             return new IntegracaoSapResponse
             {
                 Mensagem = mensagemSap,
                 Data = protocoloAbastecimento,
                 Sucesso = sucessoSap
             };
         }

         /// <summary>
         /// 
         /// </summary>
         /// <param name="lValidarAbastecimentoRequest"></param>
         /// <returns></returns>
         public RespPadrao ValidarValorAbastecimento(ValidarAbastecimentoRequest lValidarAbastecimentoRequest)
         {
             var lAbastecimento = _abastecimentoRepository.FirstOrDefault(x => x.Id == lValidarAbastecimentoRequest.AbastecimentoId);
             var lEmpresa = _empresaReadRepository.GetById(lValidarAbastecimentoRequest.EmpresaId);

             if (Math.Round(lAbastecimento.ValorAbastecimento,3) > (lValidarAbastecimentoRequest.ValorAbastecimento + lEmpresa.ValorTolerancia))
             {
                 return new RespPadrao()
                 {
                     mensagem = "Valor informado inferior ao permitido!",
                     sucesso = false,
                     data = Math.Round(lAbastecimento.ValorAbastecimento,3)
                 };
             }
             
             if (Math.Round(lAbastecimento.ValorAbastecimento,3) < (lValidarAbastecimentoRequest.ValorAbastecimento - lEmpresa.ValorTolerancia))
             {
                 return new RespPadrao()
                 {
                     mensagem = "Valor informado superior ao permitido!",
                     sucesso = false,
                     data = Math.Round(lAbastecimento.ValorAbastecimento,3)
                 };
             }
             
             return new RespPadrao()
             {
                 mensagem = "",
                 sucesso = true,
                 data = 0
             };
         }

         private RespPadrao ConverterXmlParaDecimaRegrado(string xmlValor)
         {
             return new RespPadrao()
             {
                 data = Convert.ToDecimal(Math.Truncate(xmlValor.Replace(".", ",").ToDecimal() * 1000) / 1000)
             };
         }


         public class MontarClasseResult<T>
         {
             public bool sucesso { get; set; }
             public T data { get; set; }
             public string mensagem { get; set; }
         }

         private MontarClasseResult<SAPPedidoReq> MontarPedidoSap(int protocoloId, string xml)
         {
             var integracaoSap = new SAPPedidoReq();
             integracaoSap.Pedido = new List<Pedido>();
             integracaoSap.PedidoXML = new List<PedidoXML>();

             var lItensXml = GerarListItensXml(xml);
             
             var eXml = new XmlDocument();
             eXml.LoadXml(Encoding.UTF8.GetString(Convert.FromBase64String(xml)));

             var codigoNfe = eXml.GetElementsByTagName("NFe")[0].InnerXml.Split('"')[3];

             var lAbastecimento = _abastecimentoRepository.Where(x => x.ProtocoloAbastecimentoId == protocoloId)
                 .Include(x => x.Combustivel)
                 .Include(x => x.Empresa)
                 .Include(x => x.Posto)
                 .ThenInclude(p => p.PostoCombustivelProduto)
                 .Include(x => x.Veiculo.Filial)
                 .Include(x => x.Veiculo.CentroCusto)
                 .ToList();

             foreach (var abastecimento in lAbastecimento)
             {
                 var pedido = Mapper.Map<Pedido>(abastecimento);
                 
                 pedido.nfe = codigoNfe;
                 
                 integracaoSap.Pedido.Add(pedido);
             }

             foreach (var itenXml in lItensXml)
             {
                 var pedidoXml = Mapper.Map<PedidoXML>(itenXml);
                 
                 var consultaAbastecimento = lAbastecimento
                     .FirstOrDefault(x => x.NumeroItemXmlNota == itenXml.nItem);
                 
                 var codigoExternoCombustivel = consultaAbastecimento?.Combustivel?.CodigoExterno;
                 
                 
                 if (codigoExternoCombustivel == null)
                 {
                     return new MontarClasseResult<SAPPedidoReq>()
                     {
                         sucesso = false,
                         mensagem = "Código externo do combustível " + consultaAbastecimento?.Combustivel?.Nome + " não configurado",
                         data = null
                     };
                 }
                 
                 pedidoXml.cod_material = lAbastecimento
                     .FirstOrDefault(x=>x.NumeroItemXmlNota == itenXml.nItem)?
                     .Combustivel?.CodigoExterno.PadLeft(18,'0');

                 integracaoSap.PedidoXML.Add(pedidoXml);
             }

             return new MontarClasseResult<SAPPedidoReq>()
             {
                 sucesso = true,
                 data = integracaoSap
             };
         }

         private List<ItensXmlSap> GerarListItensXml(string xml)
         { 
            var eXml = new XmlDocument();
            eXml.LoadXml(Encoding.UTF8.GetString(Convert.FromBase64String(xml)));

            var itensXml = new List<ItensXmlSap>();

                XmlNodeList elemListItens = eXml.GetElementsByTagName("prod");
                var lImpostos = eXml.GetElementsByTagName("imposto");

                for (int i = 0; i < elemListItens.Count; i++)
                {
                    var item = new ItensXmlSap();
                    XmlNodeList elemListinside = elemListItens[i].ChildNodes;
                    for (int j = 0; j < elemListinside.Count; j++)
                    {
                        if (elemListinside[j].Name == "cProd")
                        {
                            item.CodigoProduto = elemListinside[j].InnerXml.ToString();
                        }

                        if (elemListinside[j].Name == "qCom")
                        {
                            item.Litros = ConverterXmlParaDecimaRegrado(elemListinside[j].InnerText).data
                                .ToDecimal();
                        }

                        if (elemListinside[j].Name == "xProd")
                        {
                            item.Descricao = elemListinside[j].InnerXml;
                        }

                        if (elemListinside[j].Name == "vProd")
                        {
                            item.QtdTributo = ConverterXmlParaDecimaRegrado(elemListinside[j].InnerText).data
                                .ToDecimal();
                            ;
                        }

                        if (elemListinside[j].Name == "vUnTrib")
                        {
                            item.ValorUnitarioTributo = ConverterXmlParaDecimaRegrado(elemListinside[j].InnerText)
                                .data.ToDecimal();
                        }

                        if (elemListinside[j].Name == "vDesc")
                        {
                            item.ValorDesconto = ConverterXmlParaDecimaRegrado(elemListinside[j].InnerText).data
                                .ToDecimal();
                        }
                    }

                    var lImposto = lImpostos[i].ChildNodes;
                    for (var k = 0; k < lImposto.Count; k++)
                    {
                        if (lImposto[k].Name == "ICMS")
                        {
                            var lIcms00 = lImposto[k].ChildNodes;
                            for (var l = 0; l < lIcms00.Count; l++)
                            {
                                if (lIcms00[l].Name == "ICMS00")
                                {
                                    var lIcms = lIcms00[l].ChildNodes;
                                    for (var m = 0; m < lIcms.Count; m++)
                                    {
                                        if (lIcms[m].Name == "vICMS")
                                        {
                                            item.ValorIcms = lIcms[m].InnerText;
                                        }
                                    }
                                }
                            }
                        }
                    }

                    item.nItem = (i + 1);
                    itensXml.Add(item);
                }

                return itensXml;
         }
         
         private async Task<SAPPedidoResp> GerarPedidoSap(SAPPedidoReq pedido, string linkSap, string senhaSap,string usuarioSap)
         {
             var retornoIntegração = await _pedidoSAPRepository.IntegrarPedidoSAP(pedido, linkSap, senhaSap,usuarioSap);
             
             return retornoIntegração;
         }
         
         public ConsultarGridProtocoloReenvioSapResponse ConsultarGridProtocoloReenvioSap(string dataInicial,
             string dataFinal, int empresaId)
         {
             try
             {
                 var dtInicial = dataInicial.ToDateTime();
                 var dtFinal = dataFinal.ToDateTime().AddDays(1).AddSeconds(-1);
                 var lProtocolos = Repository.Query
                     .Where(x => x.DataCadastro >= dtInicial && x.DataCadastro <= dtFinal &&
                                 x.EmpresaId == empresaId &&
                                 x.PostoId == User.AdministradoraId &&
                                 x.NumeroPedidoSap.IsNullOrWhiteSpace() &&
                                 (!x.JsonEnvioSap.IsNullOrWhiteSpace() ||
                                 !x.JsonRetornoSap.IsNullOrWhiteSpace()));

                 var lCount = lProtocolos.Count();

                 var retorno = lProtocolos
                     .ProjectTo<ConsultarGridProtocoloReenvioSap>().ToList();

                 return new ConsultarGridProtocoloReenvioSapResponse
                 {   
                     Items = retorno,
                     totalItems = lCount
                 };
             }
             catch (Exception e)
             {
                 LogManager.GetCurrentClassLogger().Error(e);
                 throw;
             }
         }
         
         public List<ExportObject<ProtocoloAbastecimentoPaiExport, ProtocoloAbastecimentoItemExport>> ExportarRelatorio(DtoExportarRelatorioProtocoloAbastecimento request)
         {
             var gridItems = ConsultarGridProtocoloAbastecimento(request.GridRequest).Items;

             var exportList = new List<ExportObject<ProtocoloAbastecimentoPaiExport, ProtocoloAbastecimentoItemExport>>();

             var gruposPorCnpj = gridItems.GroupBy(c => c.CnpjAFaturar);

             foreach (var grupo in gruposPorCnpj)
             {
                 var pai = new ProtocoloAbastecimentoPaiExport {CnpjAFaturar = grupo.Key};
                 var filhos = new List<ProtocoloAbastecimentoItemExport>();
                 foreach (var grupoFilho in grupo)
                 {
                     filhos.Add(Mapper.Map<ProtocoloAbastecimentoItemExport>(grupoFilho));
                 }
                 exportList.Add(new ExportObject<ProtocoloAbastecimentoPaiExport, ProtocoloAbastecimentoItemExport>
                 {
                     Agrupador = pai,
                     Itens = filhos
                 });
             }
             
             return exportList;
         }

         public List<ExportObject<ProtocoloAbastecimentoControlePaiExport, ProtocoloAbastecimentoControleItemExport>>
             ExportarRelatorioControle(DtoExportarRelatorioProtocoloAbastecimentoControle request)
         {
             var dtIni = request.GridRequest.DtInicial.ToShortDateString().ToDateTime();
             var dtFim = request.GridRequest.DtFinal.ToShortDateString().ToDateTime().AddDays(1).AddSeconds(-1);

             if ((dtFim - dtIni).TotalDays > 31)
                 throw new InvalidOperationException("Escolha um período de no máximo 30 dias para gerar o relatório.");

             var protocolos = Repository.Query
                 .Include(c => c.Posto)
                 .Include(c => c.Abastecimentos)
                 .ThenInclude(c => c.Combustivel)
                 .Include(c => c.Abastecimentos)
                 .ThenInclude(c => c.PagamentoAbastecimentos)
                 .AsQueryable();

             if (request.GridRequest.EmpresaId > 0)
                 protocolos = protocolos.Where(x => x.EmpresaId.HasValue && x.EmpresaId.ToInt() == request.GridRequest.EmpresaId);

             protocolos = protocolos.Where(x => x.Status.GetHashCode() == request.GridRequest.Status);
             protocolos = protocolos.Where(p => p.DataCadastro >= dtIni && p.DataCadastro <= dtFim);
             protocolos = protocolos.OrderByDescending(x => x.Id);
             
             var exportList = new List<ExportObject<ProtocoloAbastecimentoControlePaiExport, ProtocoloAbastecimentoControleItemExport>>();

             foreach (var protocolo in protocolos) 
             {
                 var pai = Mapper.Map<ProtocoloAbastecimentoControlePaiExport>(protocolo);
                 
                 var filhos = new List<ProtocoloAbastecimentoControleItemExport>();

                 var abastecimentos = protocolo.Abastecimentos.AsEnumerable();
                 abastecimentos = abastecimentos.OrderByDescending(x => x.Id).AsEnumerable();
                 
                 foreach (var abastecimento in abastecimentos)
                 {
                     filhos.Add(Mapper.Map<ProtocoloAbastecimentoControleItemExport>(abastecimento));
                 }

                 exportList.Add(new ExportObject<ProtocoloAbastecimentoControlePaiExport, ProtocoloAbastecimentoControleItemExport>
                 {
                     Agrupador = pai,
                     Itens = filhos
                 });
             }

             return exportList;
         }

         public ConsultarGridPainelPedidosPendentesResponse ConsultarGridProtocoloPedidosPendentes(DtoConsultaGridPedidosPendentes request)
         {
             try
             {
                 var lProtocoloAbastecimento = Repository.Query
                     .Where(x => x.DataCadastro >= request.DtInicial && x.DataCadastro <= request.DtFinal 
                                                                     && (request.EmpresaId > 0
                                                                         ? x.EmpresaId == request.EmpresaId
                                                                         : x.EmpresaId != null) 
                                                                     && (request.Status == 1 
                                                                         ? x.IntegracaoSap == 1 
                                                                         : x.IntegracaoSap != 1));

                 lProtocoloAbastecimento = lProtocoloAbastecimento.AplicarFiltrosDinamicos(request.Filters);
                 lProtocoloAbastecimento = string.IsNullOrWhiteSpace(request.Order?.Campo)
                     ? lProtocoloAbastecimento.OrderByDescending(o => o.Id)
                     : lProtocoloAbastecimento.OrderBy($"{request.Order.Campo} {request.Order.Operador.DescriptionAttr()}");

                 var retorno = lProtocoloAbastecimento.Skip((request.Page - 1) * request.Take)
                     .Take(request.Take)
                     .ProjectTo<ConsultarGridPainelPedidoPendente>()
                     .ToList();
                 
                 return new ConsultarGridPainelPedidosPendentesResponse
                 {   
                     Items = retorno,
                     totalItems = retorno.Count
                 };
             }
             catch (Exception e)
             {
                 LogManager.GetCurrentClassLogger().Error(e);
                 throw;
             }
         }
    }
}