using System;
using System.Collections.Generic;
using System.Linq;
using System.Linq.Dynamic.Core;
using System.Threading.Tasks;
using AutoMapper.QueryableExtensions;
using SistemaInfo.BBC.Application.Interface.Retencao;
using SistemaInfo.BBC.Application.Objects.Api.Retencao;
using SistemaInfo.BBC.Application.Objects.Base;
using SistemaInfo.BBC.Application.Objects.Web.Retencao;
using SistemaInfo.BBC.Domain.Grid;
using SistemaInfo.BBC.Domain.Helper;
using SistemaInfo.BBC.Domain.Models.Retencao;
using SistemaInfo.BBC.Domain.Models.Retencao.Commands;
using SistemaInfo.BBC.Domain.Models.Retencao.Repository;
using SistemaInfo.Framework.DomainDrivenDesign.Application.Service;
using SistemaInfo.Framework.DomainDrivenDesign.Domain.Engine;

namespace SistemaInfo.BBC.Application.Services.Retencao
{
    public class RetencaoAppService :
        AppService<Domain.Models.Retencao.Retencao, IRetencaoReadRepository, IRetencaoWriteRepository>,
        IRetencaoAppService
    {

        public RetencaoAppService(IAppEngine engine, IRetencaoReadRepository readRepository,
            IRetencaoWriteRepository writeRepository) : base(engine,
            readRepository, writeRepository)
        {
        }

        public async Task<RespPadrao> Cadastrar(RetencaoCadastrarRequest retencaoCadastrarRequest)
        {
            var retencao = Mapper.Map<RetencaoSalvarCommand>(retencaoCadastrarRequest);

            await Engine.CommandBus.SendCommandAsync(retencao);

            return new RespPadrao(true, "Retenção cadastrada com sucesso!");
        }

        public ConsultarGridRetencaoResp ConsultarGridRetencao(DateTime dtInicial, DateTime dtFinal, int take, int page,
            OrderFilters orderFilters, List<QueryFilters> filters)
        {
            var lRetencao = Repository.Query.GetAll()
                .Where(r => r.DataCadastro >= dtInicial && r.DataCadastro <= dtFinal);

            var lCount = lRetencao.Count();

            lRetencao = lRetencao.AplicarFiltrosDinamicos(filters);
            lRetencao = string.IsNullOrWhiteSpace(orderFilters?.Campo)
                ? lRetencao.OrderByDescending(o => o.Id)
                : lRetencao.OrderBy($"{orderFilters.Campo} {orderFilters.Operador.DescriptionAttr()}");

            var retorno = lRetencao.Skip((page - 1) * take)
                .Take(take)
                .ProjectTo<ConsultarGridRetencao>(Engine.Mapper.ConfigurationProvider).ToList();

            return new ConsultarGridRetencaoResp
            {
                items = retorno,
                totalItems = lCount
            };
        }

        public List<ConsultarGridRetencao> DadosRelatorioGridRetencao(DateTime dtInicial, DateTime dtFinal, OrderFilters orderFilters, List<QueryFilters> filters)
        {
            var dados = DadosGridRelatorioRetencao(dtInicial, dtFinal, orderFilters, filters);
            return dados.ProjectTo<ConsultarGridRetencao>().ToList();
        }

        private IQueryable<Domain.Models.Retencao.Retencao> DadosGridRelatorioRetencao(DateTime dtInicial, DateTime dtFinal, OrderFilters orderFilters,
            List<QueryFilters> filters)
        {
            var lRetencao = Repository.Query.GetAll()
                .Where(r => r.DataCadastro >= dtInicial && r.DataCadastro <= dtFinal);

            lRetencao = lRetencao.AplicarFiltrosDinamicos(filters);

            lRetencao = string.IsNullOrWhiteSpace(orderFilters?.Campo)
                ? lRetencao.OrderByDescending(o => o.Id)
                : lRetencao.OrderBy($"{orderFilters.Campo} {orderFilters.Operador.DescriptionAttr()}");

            return lRetencao;
        }

        public async Task<RespPadrao> SetarRetencaoIntegrada(RetencaoCadastrarRequest  request)
        {
            var retencaoCommand = Mapper.Map<RetencaoSalvarCommand>(request);
            
            retencaoCommand.DataIntegracao = DateTime.Now;
            retencaoCommand.Status = StatusRetencao.Integrada;
            
            await Engine.CommandBus.SendCommandAsync(retencaoCommand);
            return new RespPadrao {sucesso = true};
        }
    }
}