﻿using System;
using System.Collections.Generic;
using System.Linq;
using System.Linq.Dynamic.Core;
using System.Threading.Tasks;
using AutoMapper.QueryableExtensions;
using Microsoft.EntityFrameworkCore;
using NLog;
using SistemaInfo.BBC.Application.Email.Posto;
using SistemaInfo.BBC.Application.Email.Usuario;
using SistemaInfo.BBC.Application.Interface.DocumentosProcessoVinculado;
using SistemaInfo.BBC.Application.Interface.Parametros;
using SistemaInfo.BBC.Application.Interface.Posto;
using SistemaInfo.BBC.Application.Interface.PostoCombustivel;
using SistemaInfo.BBC.Application.Objects.Base;
using SistemaInfo.BBC.Application.Objects.Mobile.Posto;
using SistemaInfo.BBC.Application.Objects.Web.Posto;
using SistemaInfo.BBC.Application.Objects.Web.Posto.Request;
using SistemaInfo.BBC.Domain.Components.Email;
using SistemaInfo.BBC.Domain.Enum;
using SistemaInfo.BBC.Domain.Grid;
using SistemaInfo.BBC.Domain.Helper;
using SistemaInfo.BBC.Domain.Models.Cidade.Repository;
using SistemaInfo.BBC.Domain.Models.Documento.Repository;
using SistemaInfo.BBC.Domain.Models.DocumentosProcessoVinculado.Repository;
using SistemaInfo.BBC.Domain.Models.Posto.Commands;
using SistemaInfo.BBC.Domain.Models.Posto.Repository;
using SistemaInfo.BBC.Domain.Models.Usuario.Commands;
using SistemaInfo.BBC.Domain.Models.Usuario.Repository;
using SistemaInfo.Framework.DomainDrivenDesign.Application.Service;
using SistemaInfo.Framework.DomainDrivenDesign.Domain.Engine;
using SistemaInfo.Framework.Utils;

namespace SistemaInfo.BBC.Application.Services.Posto
{
    public class PostoAppService : AppService<Domain.Models.Posto.Posto, IPostoReadRepository, IPostoWriteRepository>, 
        IPostoAppService
    {
        private readonly INotificationEmailExecutor _notificationEmailExecutor;
        private readonly ICidadeReadRepository _cidadeReadRepository;
        private readonly IPostoCombustivelAppService _postoCombustivelAppService;
        private readonly IDocumentoReadRepository _documentoReadRepository;
        private readonly IPostoReadRepository _readPostoRepository;
        private readonly IParametrosAppService _parametrosAppService;
        private readonly IUsuarioReadRepository _usuarioReadRepository;
        private readonly IDocumentosProcessoVinculadoReadRepository _documentosProcessoVinculadoReadRepository;

        public PostoAppService(IAppEngine engine, 
            IPostoReadRepository readRepository, 
            IPostoWriteRepository writeRepository, 
            INotificationEmailExecutor notificationEmailExecutor,
            IDocumentoReadRepository documentoReadRepository,
            IUsuarioReadRepository usuarioReadRepository,
            IPostoCombustivelAppService postoCombustivelAppService,
            IParametrosAppService parametrosAppService,
            IDocumentosProcessoVinculadoReadRepository documentosProcessoVinculadoReadRepository,
            ICidadeReadRepository cidadeReadRepository) : base(engine, readRepository, writeRepository)
        {
            _notificationEmailExecutor = notificationEmailExecutor;
            _postoCombustivelAppService = postoCombustivelAppService;
            _documentoReadRepository = documentoReadRepository;
            _cidadeReadRepository = cidadeReadRepository;
            _readPostoRepository = readRepository;
            _parametrosAppService = parametrosAppService;
            _usuarioReadRepository = usuarioReadRepository;
            _documentosProcessoVinculadoReadRepository = documentosProcessoVinculadoReadRepository;
        }

        public ConsultarGridPostoResponse ConsultarGridPosto(int take, int page, OrderFilters orderFilters, List<QueryFilters> filters)
        {
            var lPosto = Repository.Query.GetAll();
            
            lPosto = lPosto.AplicarFiltrosDinamicos(filters);
            lPosto = string.IsNullOrWhiteSpace(orderFilters?.Campo)
                ? lPosto.OrderByDescending(o => o.Id)
                : lPosto.OrderBy($"{orderFilters.Campo} {orderFilters.Operador.DescriptionAttr()}");

            var retorno = lPosto.Skip((page - 1) * take)
                .Take(take)
                .ProjectTo<ConsultarPostoGrid>(Engine.Mapper.ConfigurationProvider).ToList();
            
            return new ConsultarGridPostoResponse
            {
                items = retorno,
                totalItems = lPosto.Count()
            };
        }

        public async Task<PostoResponse> ConsultarPorId(int idPosto)
        {

            #region Valida se é ambiente posto para puxar as informações corretas

            var postoId = User.Sistema == "Posto" ? User.AdministradoraId : idPosto;

            #endregion
            
            var consultaPosto = await _readPostoRepository.GetByIdIncludeAll(postoId);
            
            return Mapper.Map<PostoResponse>(consultaPosto);
        }
        
        public async Task AlterarStatus(PostoStatusRequest lPostoStatus)
        {
            await Engine.CommandBus.SendCommandAsync(Mapper.Map<PostoAlterarStatusCommand>(lPostoStatus));
        }
        
        public  async Task<RespPadrao> Save(PostoRequest lPostoReq)
        {
            try
            {

                #region Valida se usuário tem permissão de editar o posto
                
                if (User.Sistema == "Posto")
                {
                    var idPostoLogado = User.AdministradoraId;
                    if (lPostoReq.Id != idPostoLogado)
                    {
                        return new RespPadrao()
                        {
                            sucesso = false,
                            mensagem = "Não foi possível salvar o posto informado! Usuário não possui permissão."
                        };
                    }
                }

                #endregion
                
                var lPosto = Mapper.Map<PostoSaveComRetornoCommand>(lPostoReq);
                var posto = Mapper.Map<Domain.Models.Posto.Posto>(lPosto);
                posto.ValidarCriacao();
                
                if (lPosto.Bloqueado == 1)
                {
                    lPosto.DataBloqueio = DateTime.Now;
                    lPosto.UsuarioBloqueioId = User.Id;
                    lPosto.StatusCadastro = StatusCadastroPosto.Bloqueado;
                }

                if (lPostoReq.Id > 0)
                {
                    var oldPostoGravado =
                        Repository.Query.GetByIdIncludeAll(lPosto.Id).Result;
                    
                    if (oldPostoGravado.Bloqueado == 1 && lPosto.Bloqueado == 0)
                    {
                        lPosto.StatusCadastro = StatusCadastroPosto.AguardandoAprovacao;
                    }
                    
                    Repository.Query.Detach(oldPostoGravado);
                }

                if (Engine.User.Id != 0)
                {
                    if (lPosto.StatusCadastro == StatusCadastroPosto.Aprovado || lPosto.StatusCadastro == StatusCadastroPosto.Reprovado)
                    {
                        lPosto.UsuarioCredenciamentoSlaId = Engine.User.Id;
                    }
                }
                
                if (lPosto.Id == 0)
                {
                    lPosto.FarolSla = TipoFarol.FarolVerde;
                }

                if (lPosto.Senha != null)
                {
                    lPosto.Senha = CriptografiaUtils.GetHashSha1(lPosto.Senha);
                }
                
                var result = await Engine.CommandBus.SendCommandAsync<Domain.Models.Posto.Posto>(lPosto);
                
                return new RespPadrao()
                {
                    id = result.Id,
                    sucesso = true,
                    mensagem = ""
                };
            } 
            catch (Exception e)
            {
                return new RespPadrao()
                {
                    sucesso = false,
                    mensagem = e.Message
                };
            }
        }

        public  async Task<RespPadrao> SaveNewAccreditation(PostoRequest lPostoReq)
        {
            try
            {
                var lPosto = Mapper.Map<PostoSaveComRetornoCommand>(lPostoReq);
                
                var posto = Mapper.Map<Domain.Models.Posto.Posto>(lPosto);
                posto.ValidarCriacao();

                lPosto.FarolSla = TipoFarol.FarolVerde;

                var documentosVinculadosObrigatorios =
                        _documentosProcessoVinculadoReadRepository.Where(x => x.Obrigatorio == 1 && x.Ativo == 1 && x.ProcessoVinculadoId == (EProcessoVinculado)2).ToList();

                if (documentosVinculadosObrigatorios.Any())
                {
                    foreach (var postoDocumentos in documentosVinculadosObrigatorios)
                    {
                        var existeDocumentoObrigatorio =
                            lPostoReq.Documentos.FirstOrDefault(x => x.DocumentosProcessoVinculadoId == postoDocumentos.Id);

                        if (existeDocumentoObrigatorio == null)
                        {
                            return new RespPadrao()
                            {
                                sucesso = false,
                                mensagem = "Preencha os documentos obrigatórios."
                            };
                        }

                    }
                }      
                    
                var result = await Engine.CommandBus.SendCommandAsync<Domain.Models.Posto.Posto>(lPosto);

                EmailPostoNovoCredenciamento.EnviarEmail(_notificationEmailExecutor, result.NomeFantasia, result.Cnpj, lPostoReq.Senha, lPosto.EmailPosto);
                
                var lParametroSla = await _parametrosAppService.ConsultarParametroConfiguracaoSla();

                if (lParametroSla != null)
                {
                    var farolMensagem = $"{result.RazaoSocial} - CNPJ: {result.Cnpj}.";

                    var farolEmail = lParametroSla.EmailsFarolVerde;

                    var farolName =
                        $"Verde, restam: {(lParametroSla.FarolVerdeHoras - (DateTime.Now - result.DataCadastro).TotalHours).ToInt()} horas para o próximo farol";

                    EmailPostoNotificacaoSla.EnviarEmail(_notificationEmailExecutor, farolMensagem,
                        farolName, farolEmail.Split(';').ToList());
                }

                return new RespPadrao()
                {
                    id = result.Id,
                    sucesso = true,
                    mensagem = ""
                };
            } 
            catch (Exception e)
            {
                return new RespPadrao()
                {
                    sucesso = false,
                    mensagem = e.Message
                };
            }
        }
        
        public PostoAutenticacaoViewModel ValidarPosto(string cnpj, string senha)
        {
            const int maximoTentativas = 4;
                   
            
            string lMensagem = "Usuário/senha inválidos. Caso não se lembre de sua" +
                               "senha, solicite uma nova clicando na opção esqueci minha senha ou contate a central de" +
                               "atendimento através do 0800-771-4080"; 
            
            var tempPosto = Repository.Query.Where(u => u.Cnpj == cnpj).AsQueryable();
            var postoLogado = tempPosto.FirstOrDefault(u => u.Senha == CriptografiaUtils.GetHashSha1(senha) && u.Cnpj == cnpj);
            var posto = tempPosto.FirstOrDefault();

            if (posto != null)
            {
                if (posto.QtdeErroSenha >= maximoTentativas)
                {
                    return new PostoAutenticacaoViewModel
                    {
                        posto = Mapper.Map<PostoLogadoViewModel>(posto),
                        mensagem = lMensagem,
                        senhaErrada = true,
                        token = null
                    };
                }
                if (postoLogado == null)
                {
                    posto.QtdeErroSenha += 1;
                    Repository.Command.SaveChanges();
                    return new PostoAutenticacaoViewModel
                    {
                        posto = Mapper.Map<PostoLogadoViewModel>(posto),
                        mensagem = lMensagem,
                        senhaErrada = true,
                        token = null
                    };
                }
            }
            else
            {
                return new PostoAutenticacaoViewModel
                {
                    posto = null,
                    mensagem = lMensagem,
                    senhaErrada = true,
                    token = null
                };
            }
            
            var postoAuth = Mapper.Map<PostoAutenticacaoViewModel>(postoLogado);
            
            postoAuth.senhaErrada = false;
            postoAuth.posto.UsuarioId = null;
            postoLogado.QtdeErroSenha = 0;
            Repository.Command.SaveChanges();
            
            return postoAuth;
        }
        
        public PostoAutenticacaoViewModel ValidarUsuarioPosto(string aUsuario, string aSenha)
        {
            try
            {
                var hashSenha = CriptografiaUtils.GetHashSha1(aSenha);
                
                const int maximoTentativas = 4;

                string lMensagem = "Usuário/senha inválidos. Caso não se lembre de sua " +
                                   "senha, solicite uma nova clicando na opção esqueci minha senha ou contate a central de " +
                                   "atendimento através do 0800-771-4080";
                
                var tempUsuario = _usuarioReadRepository.Where(u => u.Login == aUsuario && u.PostoId > 0).AsQueryable();
                var usuario = tempUsuario.FirstOrDefault(u => u.Senha == hashSenha && u.Login == aUsuario);
                var userErro = tempUsuario.FirstOrDefault();

                if (userErro != null)
                {
                    if (userErro.QtdeErroSenha >= maximoTentativas)
                    {
                        return new PostoAutenticacaoViewModel
                        {
                            posto = Mapper.Map<PostoLogadoViewModel>(userErro),
                            mensagem = lMensagem,
                            senhaErrada = true,
                            token = null
                        };
                    }
                    if (usuario == null)
                    {
                       
                        userErro.QtdeErroSenha += 1;
                        
                        _usuarioReadRepository.SaveChanges();
                        
                        var tentativasRestantes = maximoTentativas - userErro.QtdeErroSenha;
                        return new PostoAutenticacaoViewModel
                        {
                            posto = Mapper.Map<PostoLogadoViewModel>(userErro),
                            mensagem = lMensagem,
                            senhaErrada = true,
                            token = null
                        };
                    }
                }
                else
                {
                    return new PostoAutenticacaoViewModel
                    {
                        posto = null,
                        mensagem = lMensagem,
                        senhaErrada = true,
                        token = null
                    };
                }

                var postoUsuario = Repository.Query.FirstOrDefault(x => x.Id == usuario.PostoId);
                
                var postoAuth = new PostoAutenticacaoViewModel();
                postoAuth.posto = Mapper.Map<PostoLogadoViewModel>(usuario);
                postoAuth.posto.UsuarioId = usuario.Id; 
                postoAuth.posto.Nome = usuario.Nome;
                postoAuth.posto.NomePosto = postoUsuario.RazaoSocial;
                postoAuth.senhaErrada = false;

                usuario.QtdeErroSenha = 0;

                _usuarioReadRepository.SaveChanges();
                
                return postoAuth;

            }
            catch (Exception e)
            {
                LogManager.GetCurrentClassLogger().Error(e);
                throw;
            }

        }
        
        public async Task<RespPadrao> RecuperarSenha(RecuperarSenhaPostoRequest request)
        {
            var posto = Repository.Query.Where(o => o.Cnpj == request.CpfCnpj && o.EmailPosto == request.Email)
                .ProjectTo<PostoSaveCommand>().FirstOrDefault();
            
            if (posto == null)
                throw new Exception($"Nenhum posto encontrado na base para o CNPJ {request.CpfCnpj.ToCpfOrCnpj()} e E-mail {request.Email}");

            var senhaNova = GerarSenhaAleatoria();
            posto.Senha = CriptografiaUtils.GetHashSha1(senhaNova);
            posto.QtdeErroSenha = 0;
            EmailPostoRecuperarSenha.EnviarEmail(_notificationEmailExecutor, posto.NomeFantasia, senhaNova, posto.EmailPosto);
            
            var command = Mapper.Map<PostoSavePasswordCommand>(posto);
            await Engine.CommandBus.SendCommandAsync<Domain.Models.Posto.Posto>(command);
            
            return new RespPadrao()
            {
                sucesso = true,
                mensagem = "E-mail enviado com sucesso!"
            };
        }
        
        
        public async Task<RespPadrao> RecuperarSenhaUsuarioPosto(RecuperarSenhaPostoRequest request)
        {
            try
            {
                var lUsuario = _usuarioReadRepository
                    .FirstOrDefault(x => x.Cpf == request.CpfCnpj && x.Email == request.Email && x.PostoId != null && x.Ativo == 1);

                if (lUsuario == null) return new RespPadrao(false, "Não foi possível recuperar a senha do usuário.");

                var lSenhaAntiga = lUsuario.Senha;

                var lSenhaNovaAleatoria = GerarSenhaAleatoria();

                var lAlterarSenhaCommand = new UsuarioAlterarSenhaCommand
                {
                    Id = lUsuario.Id,
                    Senha = lSenhaNovaAleatoria,
                    DataCriacaoSenha = DateTime.Now,
                    SenhaProvisoria = 1,
                    QtdeErroSenha = 0,
                    HasharSenha = true
                };

                await Engine.CommandBus.SendCommandAsync(lAlterarSenhaCommand);

                var lRetornoEmail = await EmailUsuarioRecuperarSenha
                    .EnviarEmail(_notificationEmailExecutor, lUsuario.Nome, lSenhaNovaAleatoria, lUsuario.Email, null);

                //Caso dê tudo certo retorna sucesso
                if (lRetornoEmail.sucesso)
                {
                    return new RespPadrao
                    {
                        sucesso = true,
                        mensagem = ""
                    };
                }

                //Caso não dê pra enviar o email, volta a senha antiga do usuário
                var lRetornarSenhaCommand = new UsuarioAlterarSenhaCommand
                {
                    Id = lUsuario.Id,
                    Senha = lSenhaAntiga,
                    DataCriacaoSenha = DateTime.Now,
                    SenhaProvisoria = 0,
                    HasharSenha = false
                };

                await Engine.CommandBus.SendCommandAsync(lRetornarSenhaCommand);

                return new RespPadrao
                {
                    sucesso = false,
                    mensagem = lRetornoEmail.mensagem
                };
            }
            catch (Exception e)
            {
                LogManager.GetCurrentClassLogger().Error(e);
                return new RespPadrao
                {
                    sucesso = false,
                    mensagem = "Não foi possível recuperar a senha do usuário."
                };
            }
        }
        

        public async Task<RespPadrao> ChangePassword(AlterarSenhaPostoRequest request)
        {

            var posto = await _readPostoRepository.GetByIdAsync(User.AdministradoraId);

            var lPostoSaveCommand = Mapper.Map<PostoSaveCommand>(posto);

            Repository.Query.Detach(posto);
            
            if (request.senhaAntiga.GetHashSha1() != lPostoSaveCommand?.Senha)
            {
                return new RespPadrao()
                {
                    sucesso = false,
                    mensagem = "Senha atual inválida!"
                };
            }

            #region Validar argumentos
            
            if (string.IsNullOrEmpty(request.novaSenha))
            {
                return new RespPadrao()
                {
                    sucesso = false,
                    mensagem = "Informe a nova senha!"
                };
            }

            if (string.IsNullOrEmpty(request.confirmacaoNovaSenha))
            {
                return new RespPadrao()
                {
                    sucesso = false,
                    mensagem = "Necessário confirmar a nova senha"
                };
            }

            if (request.novaSenha != request.confirmacaoNovaSenha)
            {
                return new RespPadrao()
                {
                    sucesso = false,
                    mensagem = "Confirmação da senha não confere com a nova senha."
                };
            }

            var validaSenha = ValidarSenha(request.novaSenha);
            if (!string.IsNullOrEmpty(validaSenha))
            {
                return new RespPadrao()
                {
                    sucesso = false,
                    mensagem = validaSenha
                };
            }

            #endregion

            lPostoSaveCommand.Senha = request.novaSenha.GetHashSha1();

            try
            {
                await Engine.CommandBus.SendCommandAsync(lPostoSaveCommand);

            }
            catch (Exception e)
            {
                LogManager.GetCurrentClassLogger().Error(e);
                throw;
            }


            return new RespPadrao()
            {
                sucesso = true,
                mensagem = "Senha alterada com sucesso"
            };
        }

        private static string GerarSenhaAleatoria()
        {
            const string chars = "ABCDEFGHIJKLMNOPQRSTUVWXYZ0123456789";
            
            var random = new Random();
            var result = new string(Enumerable.Repeat(chars, 10).Select(s => s[random.Next(s.Length)]).ToArray());
            
            return result;
        }
        
        public string ValidarSenha(string senha)
        {
            if (senha.IsNullOrWhiteSpace())
            {
                return "Senha inválida!";
            }

            if (senha.Length < 6)
            {
                return "A senha informada deve ter no mínimo 6 caracteres!";
            }

            var lTextosParaBloquear = new string[]
                {"JSL", "SCHIO", "TGABC", "ABC", "DEF", "GHI", "123", "456", "789, BBC"};

            foreach (var lTexto in lTextosParaBloquear)
            {
                if (senha.ToUpper().IndexOf(lTexto) > -1)
                {
                    return
                        "A senha informada não atende os requisitos de segurança, forneça uma nova senha que não possua sequências e/ou nome da empresa!";
                }
            }

            var lLetras = "abcdefghijklmnopqrstuvwxyzABCDEFGHIJKLMNOPQRSTUVWXY";
            var lNumeros = "0123456789";
            var lEspeciais = "!@#$%¨&*()|\\?/}{[]§ªº.,:;";
            var lTemLetra = false;
            var lTemNumero = false;
            var lTemEspecial = false;

            for (int i = 0; i < senha.Length; i++)
            {
                var lCaracterer = senha.Substring(i, 1);

                if (lLetras.IndexOf(lCaracterer) > -1) lTemLetra = true;
                if (lNumeros.IndexOf(lCaracterer) > -1) lTemNumero = true;
                if (lEspeciais.IndexOf(lCaracterer) > -1) lTemEspecial = true;
            }

            if (!lTemLetra || !lTemNumero || !lTemEspecial)
            {
                return "É necessário ao menos uma letra, um número e um caracter especial na senha!";
            }

            return null;
        }
        
        public ConsultaListaPostosResponse ConsultarListaPostos(string latitude, string longitude, int? raio)
        {
            var lPosto = Repository.Query.GetAll().ToList();
            var lPostoConsulta = new ConsultaListaPostosResponse()
            {
                items = new List<ConsultaListaPostos>()
            };
            var lCount = 0;

            var longitudeOrigen = longitude.IsNullOrWhiteSpace() ? 0 : Convert.ToDouble(longitude.Replace('.',','));
            var latirudeOrigen = latitude.IsNullOrWhiteSpace() ? 0 : Convert.ToDouble(latitude.Replace('.',','));

            if (longitudeOrigen == 0 || latirudeOrigen == 0)
            {
                return null;
            }

            if (raio > 0)
            {
                foreach (var postoRange in lPosto)
                {
                    if (postoRange.Longitude.IsNullOrWhiteSpace() || postoRange.Latitude.IsNullOrWhiteSpace())
                    {
                        continue;
                    }
                    
                    var longitudePosto = Convert.ToDouble(postoRange.Longitude.Replace('.',','));
                    var latitudePosto = Convert.ToDouble(postoRange.Latitude.Replace('.',','));
                    var raioMetro = Convert.ToDouble(raio);

                    double theta = longitudePosto - longitudeOrigen;
                    double dist = Math.Sin(deg2rad(latirudeOrigen)) * Math.Sin(deg2rad(latitudePosto)) + Math.Cos(deg2rad(latirudeOrigen)) * Math.Cos(deg2rad(latitudePosto)) * Math.Cos(deg2rad(theta));
                    dist = Math.Acos(dist);
                    dist = rad2deg(dist);
                    dist = dist * 60 * 1.1515;
                    dist = dist * 1.609344;
    
                    // verificador de distancia de raio se inferior ou igual o posto é adicionado a lista
                    if (dist <= raioMetro)
                    {
                        var postoInsert = new ConsultaListaPostos()
                        {
                            Id = postoRange.Id,
                            NomeFantasia = postoRange.NomeFantasia,
                            RazaoSocial = postoRange.RazaoSocial,
                            Bandeira = postoRange.Bandeira,
                            Longitude = postoRange.Longitude,
                            Latitude = postoRange.Latitude,
                            Cnpj = postoRange.Cnpj
                        };

                        var lCombustivelPostoConsulta =
                            _postoCombustivelAppService.Repository.Query.Where(x => x.PostoId == postoRange.Id)
                                .Include(x=>x.Combustivel);

                        postoInsert.PostoConbustiveis = new List<postoCombustivelPrecoList>();
                            
                        foreach (var combustivelPosto in lCombustivelPostoConsulta)
                        {
                            postoInsert.PostoConbustiveis.Add(new postoCombustivelPrecoList()
                            {
                                Combustivel = combustivelPosto?.Combustivel?.Nome,
                                PrecoBomba = combustivelPosto?.ValorCombustivelBomba,
                                PrecoBBC = combustivelPosto?.ValorCombustivelBBC
                            });
                        }

                        lPostoConsulta.items.Add(postoInsert); 
                        
                        lCount += 1 ;

                        lPostoConsulta.totalItems = lCount;
                    }
                }
                
                return lPostoConsulta;
            }

            return Mapper.Map<ConsultaListaPostosResponse>(lPosto);
        }

        public ConsultaPostosIdApiResponse ConsultarPostoIdApi(int idPosto)
        {
            var consultaPosto = ConsultarPorId(idPosto).Result;

            var retornoConsultaApi = Mapper.Map<ConsultaPostosIdApiResponse>(consultaPosto);

            retornoConsultaApi.ContatoNome =
                consultaPosto.PostoContatos.FirstOrDefault(x => x.PostoId == consultaPosto.Id)?.Nome;
            retornoConsultaApi.Telefone =
                consultaPosto.PostoContatos.FirstOrDefault(x => x.PostoId == consultaPosto.Id)?.Telefone;
            
            retornoConsultaApi.CidadeNome = _cidadeReadRepository.FirstOrDefault(x=>x.Id == consultaPosto.CidadeId).Nome;
            retornoConsultaApi.CidadeUf = _cidadeReadRepository.FirstOrDefault(x=>x.Id == consultaPosto.CidadeId).Estado.Uf;
            
            return retornoConsultaApi;
        }
        
        public RespPadrao ConsultaPostoAbastecimento(ConsultaPostoAbastecimentoMobileQrRequest qrCode, string portadorCpfCnpj)
        {
            #region quebra e leitura do QRCODE

            //devese implementar metodod para QR code

            #endregion

            var lPosto = Repository.Query.Where(x => x.Cnpj == qrCode.qrCode).FirstOrDefault();

            if (lPosto != null)
            {
                var retorno = new ConsultaPostoAbastecimentoMobileResponse()
                {
                    postoId = lPosto.Id,
                    postoNome = lPosto.NomeFantasia,
                };

                return new RespPadrao()
                {
                    sucesso = true,
                    mensagem = "Recurso aguardando implementações futuras!"
                };
            }

            return new RespPadrao()
            {
                sucesso = false,
                mensagem = "Recurso aguardando implementações futuras!"
            };
        }
        
        

        public async Task<RespPadrao> AprovarCredencimento(PostoStatusRequest request)
        {
            var lPosto = await Repository.Query.GetByIdCredenciamento(request.id);

            if (lPosto == null)
            {
                return new RespPadrao()
                {
                    sucesso = false,
                    mensagem = "Posto não encontrado!"
                };
            }

            if (lPosto.StatusCadastro != StatusCadastroPosto.AguardandoAprovacao)
            {
                return new RespPadrao()
                {
                    sucesso = false,
                    mensagem = lPosto.StatusCadastro == StatusCadastroPosto.Aprovado? "Posto já aprovado." : "Posto já reprovado.",
                    data = lPosto
                };
            }

            try
            {
                var command = Mapper.Map<PostoAprovarCredenciamentoCommand>(lPosto);
                await Engine.CommandBus.SendCommandAsync(command);
            }
            catch (Exception e)
            {
                return new RespPadrao()
                {
                    sucesso = false,
                    mensagem = "Não foi possivel realizar operação. " + e
                };
            }

            return new RespPadrao()
            {
                sucesso = true,
                data = lPosto
            };
        } 
        
        public async Task<RespPadrao> ReprovarCredencimento(PostoStatusRequest request)
        {
            var lPosto = await Repository.Query.GetByIdCredenciamento(request.id);

            if (lPosto == null)
            {
                return new RespPadrao()
                {
                    sucesso = false,
                    mensagem = "Posto não encontrado!"
                };
            }

            if (lPosto.StatusCadastro != StatusCadastroPosto.AguardandoAprovacao)
            {
                return new RespPadrao()
                {
                    sucesso = false,
                    mensagem = lPosto.StatusCadastro == StatusCadastroPosto.Aprovado? "Posto já aprovado." : "Posto já reprovado.",
                    data = lPosto
                };
            }

            try
            {
                var command = Mapper.Map<PostoReprovarCredenciamentoCommand>(lPosto);
                await Engine.CommandBus.SendCommandAsync(command);
            }
            catch (Exception e)
            {
                return new RespPadrao()
                {
                    sucesso = false,
                    mensagem = "Não foi possivel realizar operação. " + e
                };
            }
            
            return new RespPadrao()
            {
                sucesso = true
            };
        } 

        public async Task ServiceEnviarEmailNotificacaoFarolSla()
        {
            var lLog = LogManager.GetCurrentClassLogger();
            try
            {
                var lParametroSla = await _parametrosAppService.ConsultarParametroConfiguracaoSla();

                if (lParametroSla == null)
                {
                    lLog.Error("BAT_FSLA_01 ERRO: Sem parâmetro definido para Configuração SLA.");
                    return;
                }

                var lPostoAguardandoAprovacao = await Repository.Query
                    .Where(p => p.StatusCadastro == StatusCadastroPosto.AguardandoAprovacao).ToListAsync();

                if (lPostoAguardandoAprovacao == null) return;

                foreach (var lPosto in lPostoAguardandoAprovacao)
                {
                    if (lPosto.DataCadastro.Hour != DateTime.Now.Hour) continue;
                    
                    var lEmailFarolDestinatario = lPosto.FarolSla switch
                    {
                        TipoFarol.FarolVerde => lParametroSla.EmailsFarolVerde,
                        TipoFarol.FarolAmarelo => lParametroSla.EmailsFarolAmarelo,
                        _ => lParametroSla.EmailsFarolVermelho
                    };

                    var lMensagemFarol = lPosto.FarolSla switch
                    {
                        TipoFarol.FarolVerde => $"Verde, aguardando aprovação e restando {(lParametroSla.FarolVerdeHoras - (DateTime.Now - lPosto.DataCadastro).TotalHours).ToInt()} horas para o próximo farol",
                        TipoFarol.FarolAmarelo => $"Amarelo, aguardando aprovação e restando {(lParametroSla.FarolAmareloHoras + lParametroSla.FarolVerdeHoras - (DateTime.Now - lPosto.DataCadastro).TotalHours).ToInt()} horas para o próximo farol",
                        _ => "Vermelho, estando fora do prazo de aprovação"
                    };

                    EmailPostoNotificacaoSla.EnviarEmail(_notificationEmailExecutor, $"{lPosto.RazaoSocial} - CNPJ: {lPosto.Cnpj}.", lMensagemFarol, 
                        lEmailFarolDestinatario.Split(';').ToList());
                }
            }
            catch (Exception e)
            {
                lLog.Error(e, "BAT_FSLA_01 ERRO");
            }
        }

        public async Task ServiceEnviarEmailControleCredenciamentoFarolSla()
        {
            var lLog = LogManager.GetCurrentClassLogger();
            try
            {
                var lParametroSla = await _parametrosAppService.ConsultarParametroConfiguracaoSla();

                if (lParametroSla == null)
                {
                    lLog.Error("BAT_FSLA_02 ERRO: Sem parâmetro definido para Configuração SLA.");
                    return;
                }

                var lPostoAguardandoAprovacao = await Repository.Query
                    .Where(p => p.StatusCadastro == StatusCadastroPosto.AguardandoAprovacao)
                    .ToListAsync();

                if (lPostoAguardandoAprovacao == null) return;
                
                foreach (var lPosto in lPostoAguardandoAprovacao)
                {
                    var lValidacaoFarol = await _parametrosAppService.FarolSlaValidar(lPosto.DataCadastro);

                    if (lPosto.FarolSla != lValidacaoFarol)
                    {
                        lPosto.FarolSla = lValidacaoFarol;
                        await Repository.Command.SaveChangesAsync();
                    }
                    
                    var lMensagemFarol = lValidacaoFarol switch
                    {
                        TipoFarol.FarolVerde => $"Verde, aguardando aprovação e restando {(lParametroSla.FarolVerdeHoras - (DateTime.Now - lPosto.DataCadastro).TotalHours).ToInt()} horas para o próximo farol",
                        TipoFarol.FarolAmarelo => $"Amarelo, aguardando aprovação e restando {(lParametroSla.FarolAmareloHoras + lParametroSla.FarolVerdeHoras - (DateTime.Now - lPosto.DataCadastro).TotalHours).ToInt()} horas para o próximo farol",
                        _ => "Vermelho, estando fora do prazo de aprovação"
                    };
                    
                    var lEmailFarolDestinatario = lValidacaoFarol switch
                    {
                        TipoFarol.FarolVerde => lParametroSla.EmailsFarolVerde,
                        TipoFarol.FarolAmarelo => lParametroSla.EmailsFarolAmarelo,
                        _ => lParametroSla.EmailsFarolVermelho
                    };
                    
                    EmailPostoNotificacaoSla.EnviarEmail(_notificationEmailExecutor, $"{lPosto.RazaoSocial} - CNPJ: {lPosto.Cnpj}.",
                        lMensagemFarol, lEmailFarolDestinatario.Split(';').ToList());
                }
            }
            catch (Exception e)
            {
                lLog.Error(e, "BAT_FSLA_02 ERRO");
            }
        }

        public async Task<RespPadrao> ValidarStatusCredenciamentoPosto(int idPosto)
        {
            try
            {
                if (idPosto == 0)
                    throw new ArgumentException("Código do Posto inválido.");
                
                var posto = await Repository.Query.GetByIdIncludeAll(idPosto);

                if (posto is null)
                    throw new InvalidOperationException("Posto não encontrado.");
                
                if (posto.Bloqueado == 1)
                    throw new InvalidOperationException($"Posto {posto.RazaoSocial} bloqueado! {posto.MotivoBloqueio}.");
                
                if (posto.StatusCadastro == StatusCadastroPosto.AguardandoAprovacao)
                    throw new InvalidOperationException($"Posto {posto.RazaoSocial} com credenciamento pendente de aprovação!");

                if (posto.StatusCadastro == StatusCadastroPosto.Reprovado)
                    throw new InvalidOperationException( $"Posto {posto.RazaoSocial} com credenciamento reprovado!");

                return new RespPadrao(true);
            }
            catch (Exception e)
            {
                return new RespPadrao(false, e.Message);
            }
        }

        // Conversor de decimal graus para radial
        private double deg2rad(double deg)
        {
            return (deg * Math.PI / 180.0);
        }

        // Conversor radial para decimal graus
        private double rad2deg(double rad)
        {
            return (rad / Math.PI * 180.0);
        }
        
        public async Task<RespPadrao> ConsultaCadastroCnpj(string cnpj)
        {
            if (cnpj.Length <= 11)
            {
                return new RespPadrao()
                {
                    sucesso = false,
                    mensagem = "Cnpj inválido!"
                };
            }
            
            var consultaPosto = await _readPostoRepository.GetByCnpjAsync(cnpj);
            
            if (consultaPosto != null)
            {
                return new RespPadrao()
                {
                    sucesso = true,
                    mensagem = "Cnpj já cadastrado no sistema!\n Status do credenciamento: \"" + consultaPosto.StatusCadastro + "\""
                };
            }

            return new RespPadrao()
            {
                sucesso = false,
                mensagem = ""
            };
        }

    }
}