using System;
using SistemaInfo.Framework.DomainDrivenDesign.Domain.Models;
using SistemaInfo.Framework.DomainDrivenDesign.Domain.Models.Validator;

namespace SistemaInfo.BBC.Domain.Models.AuthSessionApi
{
    public class AuthSessionApi : Entity<AuthSessionApi, int, NotImplementedEntityValidator<AuthSessionApi>>
    {
        public string Token { get; set; }
        public DateTime DataCadastro { get; set; }
        public DateTime DataUltimaReq { get; set; }
        public int? EmpresaId { get; set; }
        public int? GrupoEmpresaId { get; set; }
       
        public string Login { get; set; }

        #region Propriedades de Navegacao
        public virtual GrupoEmpresa.GrupoEmpresa GrupoEmpresa { get; set; }
        public virtual Empresa.Empresa Empresa { get; set; }

        #endregion
    }
}