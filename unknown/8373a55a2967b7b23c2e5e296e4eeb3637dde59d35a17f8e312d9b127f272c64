using System;
using System.IdentityModel.Tokens.Jwt;
using System.Linq;
using System.Reflection;
using System.Security.Authentication;
using System.Text;
using System.Threading.Tasks;
using AutoMapper;
using Microsoft.AspNetCore.Authorization;
using Microsoft.AspNetCore.Mvc.Controllers;
using Microsoft.AspNetCore.Mvc.Filters;
using Microsoft.EntityFrameworkCore;
using Microsoft.Extensions.DependencyInjection;
using Microsoft.IdentityModel.Tokens;
using NLog;
using SistemaInfo.BBC.Application.Interface.AuthSession;
using SistemaInfo.BBC.Application.Interface.Portador;
using SistemaInfo.BBC.Domain.Models.AuthSession;
using SistemaInfo.BBC.Mobile.Security;
using SistemaInfo.Framework.DomainDrivenDesign.Domain.Engine;
using SistemaInfo.Framework.DomainDrivenDesign.Infra.DataTransferObjects;
using SistemaInfo.Framework.DomainDrivenDesign.Web.Secutiry.UserSession;
using SistemaInfo.Framework.Utils;
using SistemaInfo.Framework.Web.Security;

namespace SistemaInfo.BBC.Mobile.Filters
{
    namespace SistemaInfo.Auth.Bus.Common.Infra.Security
{
    /// <summary>
    /// Validação de sessão utilizada por front end
    /// </summary>
    public class JwtAuthorizationHandler : AuthorizationHandler<AppTokenAuthorizationRequirement>       
    {
        private readonly IAppEngine _engine;
        private readonly IServiceProvider _serviceProvider;
        private readonly IPortadorAppService _portadorAppService;
        
        private readonly IAuthSessionAppService _authSessionAppService;
        private Microsoft.Extensions.Configuration.IConfiguration _configuration;

        
        /// <summary>
        /// Injeção de dependencias utilizadas na controller
        /// </summary>
        /// <param name="configuration"></param>
        /// <param name="engine"></param>
        /// <param name="serviceProvider"></param>
        /// <param name="authSessionAppService"></param>
        /// <param name="portadorAppService"></param>
        public JwtAuthorizationHandler(
            Microsoft.Extensions.Configuration.IConfiguration configuration,
            IAppEngine engine,
            IServiceProvider serviceProvider,
            IAuthSessionAppService authSessionAppService, 
            IPortadorAppService portadorAppService)
        {
            _engine = engine;
            _serviceProvider = serviceProvider;
            _portadorAppService = portadorAppService;
            _configuration = configuration;
            _authSessionAppService = authSessionAppService;
        }

        
        
        /// <summary>
        /// 
        /// </summary>
        /// <param name="token"></param>
        /// <param name="tokenConfigurations"></param>
        /// <param name="signingConfigurations"></param>
        /// <exception cref="SecurityTokenExpiredException"></exception>
        /// <exception cref="AuthenticationException"></exception>
        public void ValidarToken(string token, TokenConfigurations tokenConfigurations,
            SigningConfigurations signingConfigurations)
        {
            try
            {
                SecurityToken validatedToken;
                var validationToken = new TokenValidationParameters
                {
                    IssuerSigningKey = signingConfigurations.Key,
                    ValidAudience = tokenConfigurations.Audience,
                    ValidIssuer = tokenConfigurations.Issuer,
                    ValidateLifetime = true,
                    RequireExpirationTime = true,
                    RequireSignedTokens = true
                };

                var handler = new JwtSecurityTokenHandler();
                var validToken = handler.ValidateToken(token, validationToken, out validatedToken);

                if (validatedToken.ValidTo < DateTime.UtcNow)
                {
                    throw new SecurityTokenExpiredException();
                }
                
                var lAuthSession = Mapper.Map<AuthSession>(_authSessionAppService.GetByToken(token).data);
                
                if(lAuthSession == null)
                    throw new AuthenticationException();
            }
            catch (Exception)
            {
                throw new AuthenticationException();
            }
        }
        /// <summary>
        /// Solicitação de serviço em formato Async
        /// </summary>
        /// <param name="context"></param>
        /// <param name="requirement"></param>
        /// <returns></returns>
        /// <exception cref="AuthenticationException"></exception>
        protected override async Task HandleRequirementAsync(AuthorizationHandlerContext context,
            AppTokenAuthorizationRequirement requirement)
        {
            try
            {
                if (context.Resource is AuthorizationFilterContext mvcContext)
                {
                    // Caso o método não necessite de autenticação
                    var controllerActionDesc = mvcContext.ActionDescriptor as ControllerActionDescriptor;
                    if (controllerActionDesc != null)
                    {
                        var methodInfo = controllerActionDesc.MethodInfo;
                        if (methodInfo.GetCustomAttributes<IgnoreAuthAttribute>().Any())
                        {
                            context.Succeed(requirement);
                            return;
                        }
                    }

                    // Autenticar pelo barramento
                    var sessionToken = mvcContext.HttpContext.Request?.Headers["x-web-auth-token"].FirstOrDefault();

                    if (sessionToken.IsNullOrWhiteSpace())
                        throw new AuthenticationException("Token não identificado!");

                    // DESSERIALIZAR JWT

                    var handler = new JwtSecurityTokenHandler();
                    var tokenS = handler.ReadToken(sessionToken) as JwtSecurityToken;

                    var expDate = tokenS.ValidTo;

                    if (expDate < DateTime.UtcNow)
                        context.Fail(); 

                    var log = LogManager.GetCurrentClassLogger();
                    log.Info("JwtFilter: ");

                    var portadorId = tokenS.Claims.First(claim => claim.Type == "PortadorId").Value.ToIntSafe();

                    log.Info("JwtFilter: " + portadorId);

                    if (portadorId == 0)
                        throw new AuthenticationException($"Portador não informada!");

                    var portador = await _portadorAppService.Repository.Query
                        .Where(u => u.Id == portadorId)
                        .Select(u => new
                        {
                            u.Id,
                            u.Nome,
                            u.EmpresaIdFrota
                        })
                        .FirstOrDefaultAsync();

                    if (portador == null)
                    {
                        throw new AuthenticationException($"Usuário não localizado!");
                    }


                    if (portador.EmpresaIdFrota == null)
                    {
                        throw new AuthenticationException($"Usuário não vinculado a uma empresa! Verificar cadastro.");
                    }

                    var secretKey = Encoding.UTF8.GetString(Convert.FromBase64String(_configuration["Authentication:SecretKey"]));
                    
                    if (mvcContext.HttpContext.Request.Path != "/AuthSession/GerarToken" &&
                        mvcContext.HttpContext.Request.Path != "/Usuario/Cadastro" &&
                        mvcContext.HttpContext.Request.Path != "/Usuario/RecuperarSenha" &&
                        mvcContext.HttpContext.Request.Path != "/Portador/RecuperarSenha")
                    {
                        ValidarToken(mvcContext.HttpContext.Request.Headers["x-web-auth-token"], //alterar para session-key
                            new TokenConfigurations
                            {
                                Audience = "EveryApplication",
                                Issuer = "MyServer",
                                Seconds = 600
                            }, new SigningConfigurations(secretKey)
                            );
              
                    }

                    var sessionUser = _serviceProvider.GetRequiredService<ISessionUser>() as SessionUser;
                    sessionUser.Sistema = "Portador";
                    sessionUser.Nome = portador.Nome;
                    sessionUser.Id = 1;

                    // Validar no barramento

                    context.Succeed(requirement);
                }
            }
            catch (Exception)
            {
                context.Fail();
                context.Succeed(requirement);
            }
        } 
    }
}
}