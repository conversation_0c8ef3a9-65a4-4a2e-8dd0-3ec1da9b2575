﻿using System.Collections.Generic;
using System.Linq;
using System.Linq.Dynamic.Core;
using System.Threading.Tasks;
using AutoMapper.QueryableExtensions;
using Microsoft.EntityFrameworkCore;
using SistemaInfo.BBC.Application.Interface.PercentualTransferencia;
using SistemaInfo.BBC.Application.Objects.Base;
using SistemaInfo.BBC.Application.Objects.Web.PercentualTransferencia;
using SistemaInfo.BBC.Application.Objects.Web.Portador;
using SistemaInfo.BBC.Domain.Enum;
using SistemaInfo.BBC.Domain.Grid;
using SistemaInfo.BBC.Domain.Helper;
using SistemaInfo.BBC.Domain.Models.PercentualTransferencia.Commands;
using SistemaInfo.BBC.Domain.Models.PercentualTransferencia.Repository;
using SistemaInfo.BBC.Domain.Models.PercentualTransferenciaHistorico.Repository;
using SistemaInfo.BBC.Domain.Models.PercentualTransferenciaPortador.Commands;
using SistemaInfo.BBC.Domain.Models.PercentualTransferenciaPortador.Repository;
using SistemaInfo.BBC.Domain.Models.PercentualTransferenciaPortadorHistorico.Repository;
using SistemaInfo.BBC.Domain.Models.Portador.Repository;
using SistemaInfo.BBC.Domain.Models.PortadorEmpresa.Repository;
using SistemaInfo.BBC.Domain.Models.Usuario.Repository;
using SistemaInfo.Framework.DomainDrivenDesign.Application.Service;
using SistemaInfo.Framework.DomainDrivenDesign.Domain.Engine;
using SistemaInfo.Framework.Utils;

namespace SistemaInfo.BBC.Application.Services.PercentualTransferencia
{
    public class PercentualTransferenciaAppService
        : AppService<Domain.Models.PercentualTransferencia.PercentualTransferencia,
                IPercentualTransferenciaReadRepository, IPercentualTransferenciaWriteRepository>,
            IPercentualTransferenciaAppService
    {
        private readonly IPortadorReadRepository _portadorReadRepository;
        private readonly IPercentualTransferenciaPortadorReadRepository _motoristaReadRepository;
        private readonly IPercentualTransferenciaPortadorHistoricoReadRepository _motoristaHistoricoReadRepository;
        private readonly IPercentualTransferenciaHistoricoReadRepository _percentualHistoricoReadRepository;
        private readonly IUsuarioReadRepository _usuarioReadRepository;
        private readonly IPortadorEmpresaReadRepository _portadorEmpresaReadRepository;

        public PercentualTransferenciaAppService(IAppEngine engine,
            IPercentualTransferenciaReadRepository readRepository,
            IPercentualTransferenciaWriteRepository writeRepository,
            IPortadorReadRepository portadorReadRepository,
            IPercentualTransferenciaHistoricoReadRepository percentualHistoricoReadRepository,
            IPercentualTransferenciaPortadorReadRepository motoristaReadRepository,
            IPercentualTransferenciaPortadorHistoricoReadRepository motoristaHistoricoReadRepository,
            IUsuarioReadRepository usuarioReadRepository,
            IPortadorEmpresaReadRepository portadorEmpresaReadRepository)
            : base(engine, readRepository, writeRepository)
        {
            _portadorReadRepository = portadorReadRepository;
            _motoristaHistoricoReadRepository = motoristaHistoricoReadRepository;
            _motoristaReadRepository = motoristaReadRepository;
            _percentualHistoricoReadRepository = percentualHistoricoReadRepository;
            _usuarioReadRepository = usuarioReadRepository;
            _portadorEmpresaReadRepository = portadorEmpresaReadRepository;
        }

        public async Task<ConsultarGridPercentualTransferenciaResponse> ConsultarGridPercentualTransferencia(int take,
            int page, OrderFilters orderFilters, List<QueryFilters> filters)
        {
            var lQuery = _portadorReadRepository
                .Where(p => p.Ativo != 0)
                .AsQueryable();

            if (Engine.User.EmpresaId != 0)
            {
                var lPortadoresDaEmpresaAtualIds = await _portadorEmpresaReadRepository
                    .Where(p => p.EmpresaId == Engine.User.EmpresaId)
                    .Select(p => p.PortadorId)
                    .ToListAsync();

                lQuery = lQuery.Where(p => lPortadoresDaEmpresaAtualIds.Contains(p.Id));
            }

            var lCount = await lQuery.CountAsync();

            lQuery = lQuery.AplicarFiltrosDinamicos(filters);

            lQuery = string.IsNullOrWhiteSpace(orderFilters?.Campo)
                ? lQuery.OrderByDescending(o => o.Id)
                : lQuery.OrderBy($"{orderFilters.Campo} {orderFilters.Operador.DescriptionAttr()}");

            var lGridItemsResponse = await lQuery.Skip((page - 1) * take)
                .Take(take)
                .ProjectTo<ConsultarGridPercentualTransferencia>()
                .ToListAsync();

            var lGridResponse = new ConsultarGridPercentualTransferenciaResponse
            {
                items = lGridItemsResponse,
                totalItems = lCount
            };

            return lGridResponse;
        }

        public async Task<ConsultarGridPercentualTransferenciaPortadorResponse> ConsultarGridMotoristasProprietario(
            int proprietarioId, int take, int page,
            OrderFilters orderFilters, List<QueryFilters> filters)
        {
            var lQuery = _motoristaReadRepository
                .Where(m =>
                    m.PercentualTransferencia.ProprietarioId == proprietarioId
                    && m.Ativo != 0);

            var lCount = await lQuery.CountAsync();

            lQuery = lQuery.AplicarFiltrosDinamicos(filters);

            lQuery = string.IsNullOrWhiteSpace(orderFilters?.Campo)
                ? lQuery.OrderByDescending(o => o.Id)
                : lQuery.OrderBy($"{orderFilters.Campo} {orderFilters.Operador.DescriptionAttr()}");

            var lGridItemsResponse = await lQuery.Skip((page - 1) * take)
                .Take(take)
                .ProjectTo<PercentualTransferenciaPortadorResponse>()
                .ToListAsync();

            var lGridResponse = new ConsultarGridPercentualTransferenciaPortadorResponse
            {
                totalItems = lCount,
                items = lGridItemsResponse
            };

            return lGridResponse;
        }

        public async Task<ConsultarGridPortadorReduzidoResponse> ConsultarGridMotoristasCombo(int proprietarioId,
            int take, int page,
            OrderFilters orderFilters, List<QueryFilters> filters)
        {
            var lMotoristasJaAdicionadosIds = await _motoristaReadRepository
                .Where(m =>
                    m.PercentualTransferencia.ProprietarioId == proprietarioId
                    && m.Ativo != 0)
                .Select(m => m.PortadorId)
                .ToListAsync();

            var lQuery = _portadorReadRepository
                .Where(m =>
                    m.Id != proprietarioId &&
                    m.TipoPessoa == ETipoPessoa.Fisica &&
                    m.Ativo != 0 &&
                    !lMotoristasJaAdicionadosIds.Contains(m.Id))
                .AsQueryable();
            
            if (Engine.User.EmpresaId != 0)
            {
                var lMotoristasDaEmpresaAtualIds = await _portadorEmpresaReadRepository
                    .Where(p => p.EmpresaId == Engine.User.EmpresaId)
                    .Select(p => p.PortadorId)
                    .ToListAsync();

                lQuery = lQuery.Where(p => lMotoristasDaEmpresaAtualIds.Contains(p.Id));
            }

            var lCount = await lQuery.CountAsync();

            //se nao o proprietarioId vem como filter e quebra a filtragem toda
            filters.RemoveAt(0);
            lQuery = lQuery.AplicarFiltrosDinamicos(filters);

            lQuery = string.IsNullOrWhiteSpace(orderFilters?.Campo)
                ? lQuery.OrderByDescending(o => o.Id)
                : lQuery.OrderBy($"{orderFilters.Campo} {orderFilters.Operador.DescriptionAttr()}");

            var lGridItemsResponse = await lQuery.Skip((page - 1) * take)
                .Take(take)
                .ProjectTo<ConsultarGridPortadorReduzido>()
                .ToListAsync();

            var lGridResponse = new ConsultarGridPortadorReduzidoResponse
            {
                totalItems = lCount,
                items = lGridItemsResponse
            };

            return lGridResponse;
        }

        public async Task<ConsultarGridPercentualTransferenciaPortadorResponse> ConsultarGridMotoristasComHistoricos(
            int proprietarioId, int take,
            int page, OrderFilters orderFilters, List<QueryFilters> filters)
        {
            var lQuery = _motoristaReadRepository
                .Include(m => m.UsuarioCadastro)
                .Where(m => m.PercentualTransferencia.ProprietarioId == proprietarioId);

            var lCount = await lQuery.CountAsync();

            lQuery = lQuery.AplicarFiltrosDinamicos(filters);

            lQuery = string.IsNullOrWhiteSpace(orderFilters?.Campo)
                ? lQuery.OrderByDescending(o => o.DataAlteracao)
                : lQuery.OrderBy($"{orderFilters.Campo} {orderFilters.Operador.DescriptionAttr()}");

            var lGridItemsResponse = await lQuery.Skip((page - 1) * take)
                .Take(take)
                .ProjectTo<PercentualTransferenciaPortadorResponse>()
                .ToListAsync();

            var lGridResponse = new ConsultarGridPercentualTransferenciaPortadorResponse
            {
                totalItems = lCount,
                items = lGridItemsResponse
            };

            return lGridResponse;
        }

        public async Task<ConsultarGridPercentualTransferenciaPortadorHistoricoResponse>
            ConsultarGridHistoricoPorMotorista(int percentualTransferenciaPortadorId, int take, int page,
                OrderFilters orderFilters, List<QueryFilters> filters)
        {
            var lQuery = _motoristaHistoricoReadRepository
                .Where(m => m.PercentualTransferenciaPortadorId == percentualTransferenciaPortadorId);

            var lCount = await lQuery.CountAsync();

            lQuery = lQuery.AplicarFiltrosDinamicos(filters);

            lQuery = string.IsNullOrWhiteSpace(orderFilters?.Campo)
                ? lQuery.OrderByDescending(o => o.DataAlteracao)
                : lQuery.OrderBy($"{orderFilters.Campo} {orderFilters.Operador.DescriptionAttr()}");

            var lHistoricos = await lQuery.Skip((page - 1) * take)
                .Take(take)
                .ToListAsync();

            var lGridItemsResponse = new List<PercentualTransferenciaPortadorHistoricoResponse>();

            foreach (var lHistorico in lHistoricos)
            {
                var lUsuarioAlteracaoHistorico = await _usuarioReadRepository
                    .Where(u => u.Id == lHistorico.UsuarioAlteracaoId)
                    .FirstOrDefaultAsync();

                var lPortadorHistorico = await _portadorReadRepository
                    .Where(u => u.Id == lHistorico.PortadorId)
                    .FirstOrDefaultAsync();

                var lHistoricoResponse = new PercentualTransferenciaPortadorHistoricoResponse
                {
                    Id = lHistorico.Id,
                    Adiantamento = lHistorico.Adiantamento,
                    Saldo = lHistorico.Saldo,
                    PortadorNome = lPortadorHistorico.Nome,
                    PortadorCPF = lPortadorHistorico.CpfCnpj,
                    DataAlteracao = lHistorico.DataAlteracao.ToStringBr(FormatDateTimeMethod.ShortDateTime),
                    UsuarioAlteracaoNome = lUsuarioAlteracaoHistorico.Nome,
                    UsuarioAlteracaoId = lHistorico.UsuarioAlteracaoId.ToIntSafe(),
                    UsuarioAlteracaoCpf = lUsuarioAlteracaoHistorico.Cpf,
                    Ativo = lHistorico.Ativo != 0 ? "Ativo" : "Excluído"
                };

                lGridItemsResponse.Add(lHistoricoResponse);
            }

            var lGridResponse = new ConsultarGridPercentualTransferenciaPortadorHistoricoResponse
            {
                totalItems = lCount,
                items = lGridItemsResponse
            };

            return lGridResponse;
        }

        public async Task<ConsultarGridPercentualTransferenciaHistoricoResponse> ConsultarGridHistoricosDoPercentual(
            int proprietarioId, int take, int page, OrderFilters orderFilters, List<QueryFilters> filters)
        {
            var lQuery = _percentualHistoricoReadRepository
                .Include(m => m.UsuarioAlteracao)
                .Where(m => m.ProprietarioId == proprietarioId);

            var lCount = await lQuery.CountAsync();

            lQuery = lQuery.AplicarFiltrosDinamicos(filters);

            lQuery = string.IsNullOrWhiteSpace(orderFilters?.Campo)
                ? lQuery.OrderByDescending(o => o.DataAlteracao)
                : lQuery.OrderBy($"{orderFilters.Campo} {orderFilters.Operador.DescriptionAttr()}");

            var lGridItemsResponse = await lQuery.Skip((page - 1) * take)
                .Take(take)
                .ProjectTo<PercentualTransferenciaHistoricoResponse>()
                .ToListAsync();

            var lGridResponse = new ConsultarGridPercentualTransferenciaHistoricoResponse
            {
                totalItems = lCount,
                items = lGridItemsResponse
            };

            return lGridResponse;
        }

        public async Task<PercentualTransferenciaResponse> ConsultarPercentualTransferenciaPorId(int proprietarioId)
        {
            var lPercentual = await Repository.Query
                .Where(p => p.ProprietarioId == proprietarioId)
                .Include(p => p.Proprietario)
                .Include(p => p.UsuarioCadastro)
                .FirstOrDefaultAsync();
            if (lPercentual != null)
            {
                var lPercentualResponse = Mapper.Map<PercentualTransferenciaResponse>(lPercentual);
                return lPercentualResponse;
            }

            var lRequest = new PercentualTransferenciaRequest
            {
                Id = 0,
                ProprietarioId = proprietarioId,
            };
            var lCommand = Mapper.Map<PercentualTransferenciaSalvarComRetornoCommand>(lRequest);
            var lPercentualResult =
                await Engine.CommandBus
                    .SendCommandAsync<Domain.Models.PercentualTransferencia.PercentualTransferencia>(lCommand);
            lPercentual = await Repository.Query
                .Where(p => p.ProprietarioId == lPercentualResult.ProprietarioId)
                .Include(p => p.Proprietario)
                .Include(p => p.UsuarioCadastro)
                .FirstOrDefaultAsync();
            var lPercentualNewResponse = Mapper.Map<PercentualTransferenciaResponse>(lPercentual);
            return lPercentualNewResponse;
        }

        public async Task<RespPadrao> AdicionarMotorista(AdicionarMotoristaPercentualTransferenciaRequest request)
        {
            var lPortadorAnteriormenteAdicionado = await _motoristaReadRepository
                .Where(m =>
                    m.PortadorId == request.MotoristaId &&
                    m.PercentualTransferencia.ProprietarioId == request.ProprietarioId)
                .FirstOrDefaultAsync();

            if (lPortadorAnteriormenteAdicionado != null)
            {
                if (lPortadorAnteriormenteAdicionado.Ativo != 0)
                {
                    return new RespPadrao
                    {
                        sucesso = false,
                        mensagem = "Motorista já adicionado para transferência automática."
                    };
                }
                
                var lPercentualTransferenciaPortadorEditar = new PercentualTransferenciaPortadorRequest
                {
                    Id = lPortadorAnteriormenteAdicionado.Id,
                    PortadorId = lPortadorAnteriormenteAdicionado.PortadorId,
                    PercentualTransferenciaId = lPortadorAnteriormenteAdicionado.PercentualTransferenciaId,
                    Saldo = lPortadorAnteriormenteAdicionado.Saldo != request.Saldo 
                        ? request.Saldo 
                        : lPortadorAnteriormenteAdicionado.Saldo,
                    Adiantamento = lPortadorAnteriormenteAdicionado.Adiantamento != request.Adiantamento 
                        ? request.Adiantamento 
                        : lPortadorAnteriormenteAdicionado.Adiantamento,
                    Ativo = 1
                };

                var lCommand = 
                    Mapper.Map<PercentualTransferenciaPortadorSalvarCommand>(lPercentualTransferenciaPortadorEditar);
                await Engine.CommandBus.SendCommandAsync(lCommand);
                
                return new RespPadrao
                {
                    sucesso = true,
                    mensagem = "Motorista adicionado com sucesso!"
                };
            }

            var lMotoristaNovo = await _portadorReadRepository
                .Where(m => m.Id == request.MotoristaId)
                .FirstOrDefaultAsync();

            if (lMotoristaNovo == null)
            {
                return new RespPadrao
                {
                    sucesso = false,
                    mensagem = "Não foi possível consultar esse motorista no banco"
                };
            }

            var lPercentualTransferencia = await Repository.Query
                .Where(m => m.ProprietarioId == request.ProprietarioId)
                .FirstOrDefaultAsync();

            var lPercentualTransferenciaPortadorNovo = new PercentualTransferenciaPortadorRequest
            {
                PortadorId = request.MotoristaId,
                PercentualTransferenciaId = lPercentualTransferencia.Id,
                Saldo = (lPercentualTransferencia.Saldo != request.Saldo 
                    ? request.Saldo 
                    : lPercentualTransferencia.Saldo).ToDecimalSafe(),
                Adiantamento = (lPercentualTransferencia.Adiantamento != request.Adiantamento 
                    ? request.Adiantamento 
                    : lPercentualTransferencia.Adiantamento).ToDecimalSafe(),
                Ativo = 1
            };

            var lAdicionarCommand =
                Mapper.Map<PercentualTransferenciaPortadorSalvarCommand>(lPercentualTransferenciaPortadorNovo);
            await Engine.CommandBus.SendCommandAsync(lAdicionarCommand);

            return new RespPadrao
            {
                sucesso = true,
                mensagem = "Motorista adicionado com sucesso!"
            };
        }

        public async Task<RespPadrao> Save(PercentualTransferenciaRequest percentualTransferenciaRequest)
        {
            var lCommand = Mapper.Map<PercentualTransferenciaSalvarCommand>(percentualTransferenciaRequest);
            await Engine.CommandBus.SendCommandAsync(lCommand);

            return new RespPadrao
            {
                sucesso = true,
                mensagem = "Registro salvo com sucesso!"
            };
        }

        public async Task<RespPadrao> SavePercentualTransferenciaPortador(
            PercentualTransferenciaPortadorRequest request)
        {
            var lPercentualExistente = await _motoristaReadRepository.Where(x =>
                    x.PortadorId == request.PortadorId &&
                    x.PercentualTransferenciaId == request.PercentualTransferenciaId)
                .FirstOrDefaultAsync();

            if (lPercentualExistente != null)
            {
                request.Ativo = 1;
                request.Id = lPercentualExistente.Id;
            }

            var lCommand = Mapper.Map<PercentualTransferenciaPortadorSalvarCommand>(request);
            await Engine.CommandBus.SendCommandAsync(lCommand);

            return new RespPadrao
            {
                sucesso = true,
                mensagem = "Registro salvo com sucesso!"
            };
        }

        public async Task<RespPadrao> AlterarStatusPercentualTransferenciaPortador(
            PercentualTransferenciaPortadorAlterarStatusRequest request)
        {
            var lCommand = Mapper.Map<PercentualTransferenciaPortadorAlterarStatusCommand>(request);
            await Engine.CommandBus.SendCommandAsync(lCommand);

            return new RespPadrao
            {
                sucesso = true,
                mensagem =
                    "Transferência automática para o motorista desabilitada com sucesso. Se desejar, é possível adicioná-lo novamente."
            };
        }
    }
}