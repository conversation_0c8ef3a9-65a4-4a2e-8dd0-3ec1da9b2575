﻿using System.Globalization;
using System.Linq;
using SistemaInfo.BBC.Application.Objects.Api.Viagem;
using SistemaInfo.BBC.Application.Objects.Mobile.Viagem.Response;
using SistemaInfo.BBC.Application.Objects.Web.Viagem;
using SistemaInfo.BBC.Domain.Enum;
using SistemaInfo.BBC.Domain.Helper;
using SistemaInfo.BBC.Domain.Models.PagamentoEvento.Commands;
using SistemaInfo.BBC.Domain.Models.Viagem;
using SistemaInfo.BBC.Domain.Models.Viagem.Commands;
using SistemaInfo.Framework.Utils;

namespace SistemaInfo.BBC.Application.AutoMapper.ViagemMapper
{
    public class ViagemMappingProfile : SistemaInfoMappingProfile
    {
        public ViagemMappingProfile()
        {
            CreateMap<PagamentoViagemIntegrarRequest, ViagemSalvarCommand>()
                .ForMember(a => a.FilialId, opts => opts.MapFrom(d => d.FilialId))
                .ForMember(a => a.TipoConta, opts => opts.MapFrom(d => d.TipoConta.GetHashCode()));

            CreateMap<PagamentoViagemIntegrarRequest, ViagemSalvarComRetornoCommand>()
                .ForMember(a => a.FilialId, opts => opts.MapFrom(d => d.FilialId))
                .ForMember(a => a.Status, opts => opts.MapFrom(d => StatusViagem.Aberto))
                .ForMember(a => a.NomeProprietario, opts => opts.MapFrom(d => d.NomeContratado))
                .ForMember(a => a.TipoConta, opts => opts.MapFrom(d => d.TipoConta.GetHashCode()));

            CreateMap<PagamentoViagemIntegrarRequest, PagamentoEventoSalvarComRetornoCommand>()
                .ForMember(a => a.TipoConta, opts => opts.MapFrom(d => d.TipoConta.GetHashCode()))
                .ForMember(a => a.Status, opts => opts.MapFrom(d => StatusPagamento.Aberto));

            CreateMap<ViagemSalvarCommand, Viagem>()
                .ForMember(a => a.FilialId, opts => opts.MapFrom(d => d.FilialId));

            CreateMap<ViagemSalvarComRetornoCommand, Viagem>()
                .ForMember(a => a.FilialId, opts => opts.MapFrom(d => d.FilialId));;

            CreateMap<Viagem, ViagemConsultarGridItem>()
                .ForMember(d => d.DataBaixa, opts => opts.MapFrom( src => src.DataBaixa != null ? src.DataBaixa.ToDateTime().ToString("G") : ""))
                .ForMember(d => d.DataCadastro, opts => opts.MapFrom( src => src.DataCadastro.ToString("G")))
                .ForMember(d => d.DataAlteracao, opts => opts.MapFrom( src => src.DataAlteracao != null ? src.DataAlteracao.ToDateTime().ToString("G") : ""))
                .ForMember(d => d.Status, opts => opts.MapFrom( src => src.Status.ToString()))
                .ForMember(d => d.FilialId, opts => opts.MapFrom( src => src.FilialId))
                .ForMember(d => d.CpfCnpjMotorista, opts => opts.MapFrom( src => src.PortadorMotorista.CpfCnpj.ToCpfOrCnpj()))
                .ForMember(d => d.CpfCnpjProprietario, opts => opts.MapFrom( src => src.PortadorProprietario.CpfCnpj.ToCpfOrCnpj()))
                .ForMember(d => d.CnpjEmpresa, opts => opts.MapFrom( src => src.Empresa.Cnpj.ToCpfOrCnpj()))
                .ForMember(d => d.RazaoSocialEmpresa, opts => opts.MapFrom( src => src.Empresa.RazaoSocial))
                .ForMember(d => d.Ciot, opts => opts.MapFrom( src => src.Ciot.IsNullOrWhiteSpace() ? null : src.Ciot + "/" + (src.VerificadorCiot.IsNullOrWhiteSpace() ? "XXXX" : src.VerificadorCiot)))
                .ForMember(d => d.ViagemId, opts => opts.MapFrom( src => src.Id))
                .ForMember(d => d.ViagemExternoId, opts => opts.MapFrom( src => src.ViagemExternoId))
                .ForMember(d => d.NaturezaCarga, opts => opts.MapFrom( src => src.CodigoNaturezaCarga))
                .ForMember(d => d.PesoCarga, opts => opts.MapFrom( src => src.PesoCarga.ToString()))
                ;
            
            CreateMap<ViagemAlterarStatusRequest, ViagemAlterarStatusCommand>();
            
            
            CreateMap<Viagem, ConsultarViagemCiotComboResponse>()
                .ForMember(a => a.CodigoExternoId, opts => opts.MapFrom(d => d.ViagemExternoId))
                .ForMember(a => a.NomeDestino, opts => opts.MapFrom(d => d.CidadeDestino.Nome))
                .ForMember(a => a.NomeOrigem, opts => opts.MapFrom(d => d.CidadeOrigem.Nome))
                .ForMember(a => a.IdMunicipioOrigem, opts => opts.MapFrom(d => d.CidadeOrigem.Id.ToIntSafe(0)))
                .ForMember(a => a.IdMunicipioDestino, opts => opts.MapFrom(d => d.CidadeDestino.Id.ToIntSafe(0)))
                .ForMember(a => a.CodigoMunicipioOrigem, opts => opts.MapFrom(d => d.CidadeOrigem.Ibge.ToIntSafe(0)))
                .ForMember(a => a.CodigoMunicipioDestino, opts => opts.MapFrom(d => d.CidadeDestino.Ibge.ToIntSafe(0)))
                .ForMember(a => a.CodigoNaturezaCarga, opts => opts.MapFrom(d => d.CodigoNaturezaCarga))
                .ForMember(a => a.PesoCarga, opts => opts.MapFrom(d => d.PesoCarga))
                .ForMember(a => a.ValorFrete, opts => 
                    opts.MapFrom(d => d.PagamentoEvento.SelectMany(x => x.Transacao)
                    .Where(x => x.Tipo != Tipo.Tarifas && x.Status == StatusPagamento.Fechado).Sum(x => x.Valor).FormatMonetario()));
            
            CreateMap<Viagem, ConsultarViagensCiotGridResponse>()
                .ForMember(a => a.NomeDestino, opts => opts.MapFrom(d => d.CidadeDestino.Nome))
                .ForMember(a => a.Id, opts => opts.MapFrom(d => d.Id))
                .ForMember(a => a.NomeOrigem, opts => opts.MapFrom(d => d.CidadeOrigem.Nome))
                .ForMember(a => a.CodigoMunicipioOrigem, opts => opts.MapFrom(d => d.CidadeOrigem.Ibge.ToIntSafe(0)))
                .ForMember(a => a.CodigoMunicipioDestino, opts => opts.MapFrom(d => d.CidadeDestino.Ibge.ToIntSafe(0)))
                .ForMember(a => a.CodigoNaturezaCarga, opts => opts.MapFrom(d => d.CodigoNaturezaCarga))
                .ForMember(a => a.Peso, opts => opts.MapFrom(d => d.PesoCarga))
                .ForMember(a => a.ValorFrete, opts => opts.MapFrom(d => d.PagamentoEvento.SelectMany(x => x.Transacao)
                    .Where(x => x.Tipo != Tipo.Tarifas).Sum(x => x.Valor).FormatMonetario()));
        
            CreateMap<Viagem, ViagemResponseItem>()
                .ForMember(a => a.ViagemId, opts => opts.MapFrom(d => d.Id))
                .ForMember(a => a.RazaoSocialEmpresa, opts => opts.MapFrom(d => d.Empresa.RazaoSocial))
                .ForMember(a => a.CnpjEmpresa, opts => opts.MapFrom(d => d.Empresa.Cnpj.FormatarCpfCnpj(false)))
                .ForMember(a => a.DataBaixa, opts => opts.MapFrom(d => d.DataBaixa))
                .ForMember(a => a.DataCadastro, opts => opts.MapFrom(d => d.DataCadastro))
                .ForMember(a => a.DataAlteracao, opts => opts.MapFrom(d => d.DataAlteracao))
                .ForMember(a => a.FilialId, opts => opts.MapFrom(d => d.FilialId))
                .ForMember(a => a.NomeProprietario, opts => opts.MapFrom(d => d.NomeProprietario))
                .ForMember(a => a.NomeMotorista, opts => opts.MapFrom(d => d.NomeMotorista))
                .ForMember(a => a.CpfCnpjProprietario, opts => opts.MapFrom(d => d.PortadorProprietario.CpfCnpj.FormatarCpfCnpj(false)))
                .ForMember(a => a.CpfCnpjMotorista, opts => opts.MapFrom(d => d.PortadorMotorista.CpfCnpj.FormatarCpfCnpj(false)))
                .ForMember(a => a.Status, opts => opts.MapFrom(d => d.Status.ToString()))
                .ForMember(a => a.CidadeOrigem, opts => opts.MapFrom(d => d.CidadeOrigem.Nome))
                .ForMember(a => a.CidadeDestino, opts => opts.MapFrom(d => d.CidadeDestino.Nome))
                .ForMember(a => a.CidadeIbgeOrigem, opts => opts.MapFrom(d => d.CidadeOrigem.Ibge))
                .ForMember(a => a.CidadeIbgeDestino, opts => opts.MapFrom(d => d.CidadeDestino.Ibge))
                .ForMember(a => a.Ciot, opts => opts.MapFrom(d => d.Ciot));
            
        
        }
    }
}