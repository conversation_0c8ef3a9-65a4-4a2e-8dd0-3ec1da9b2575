﻿using System;
using System.Collections.Generic;
using System.Linq;
using System.Threading.Tasks;
using AutoMapper.QueryableExtensions;
using Microsoft.EntityFrameworkCore;
using NLog;
using SistemaInfo.BBC.Application.External.Conductor.Interface;
using SistemaInfo.BBC.Application.Interface.Pagamentos;
using SistemaInfo.BBC.Application.Interface.Saldo;
using SistemaInfo.BBC.Application.Interface.Viagem;
using SistemaInfo.BBC.Application.Objects.Api.Viagem;
using SistemaInfo.BBC.Application.Objects.Base;
using SistemaInfo.BBC.Application.Objects.External.Conductor.Conta;
using SistemaInfo.BBC.Application.Objects.Web.ConfiguracaoTelaoSaldo;
using SistemaInfo.BBC.Application.Objects.Web.Saldo;
using SistemaInfo.BBC.Domain.External.Conductor.Interface;
using SistemaInfo.BBC.Domain.Helper;
using SistemaInfo.BBC.Domain.Models.Empresa.Repository;
using SistemaInfo.BBC.Domain.Models.Parametros.Repository;
using SistemaInfo.BBC.Domain.Models.Usuario.Repository;
using SistemaInfo.Framework.DomainDrivenDesign.Application.Service;
using SistemaInfo.Framework.DomainDrivenDesign.Domain.Engine;
using SistemaInfo.Framework.Utils;

namespace SistemaInfo.BBC.Application.Services.Saldo;

public class SaldoAppService : AppService, ISaldoAppService
{
    private readonly IViagemAppService _viagemAppService;
    private readonly IPagamentosAppService _pagamentosAppService;
    private readonly IEmpresaReadRepository _empresaReadRepository;
    private readonly IParametrosReadRepository _parametrosReadRepository;
    private readonly IUsuarioReadRepository _usuarioReadRepository;
    private readonly IContaAppService _contaAppService;
    private readonly ICartaoRepository _cartaoRepository;
    public SaldoAppService(IAppEngine engine, IViagemAppService viagemAppService, IUsuarioReadRepository usuarioReadRepository, IEmpresaReadRepository empresaReadRepository, IParametrosReadRepository parametrosReadRepository, IPagamentosAppService pagamentosAppService, IContaAppService contaAppService, ICartaoRepository cartaoRepository) : base(engine)
    {
        _viagemAppService = viagemAppService;
        _usuarioReadRepository = usuarioReadRepository;
        _empresaReadRepository = empresaReadRepository;
        _parametrosReadRepository = parametrosReadRepository;
        _pagamentosAppService = pagamentosAppService;
        _contaAppService = contaAppService;
        _cartaoRepository = cartaoRepository;
    }

    

    public async Task<PagamentoDiaGraficoAngular> GetPagamentosGrafico(int empresaId, DateTime? dataBase)
    {
        try
        {
            var lRetorno = new List<PagamentoDiaGrafico>();

            // Inicializando dias da semana de segunda a domingo
            var lDataIndex = DateTime.Now.Date.StartOfWeek(DayOfWeek.Monday);
            do
            {
                lRetorno.Add(new PagamentoDiaGrafico { Dia = lDataIndex.DiaSemana() });
                lDataIndex = lDataIndex.AddDays(1);
            } while (lDataIndex.DayOfWeek != DayOfWeek.Monday);

            #region Atual
            
            var (_, ultimoDia) = (dataBase ?? DateTime.Now).ObterInicioEFimDaSemana();
            var lPagamentos = await _viagemAppService.ConsultarPagamentosDia(ultimoDia.StartOfWeek(DayOfWeek.Monday), ultimoDia, empresaId, true);
            foreach (var lPgto in lPagamentos)
            {
                var lDia = lPgto.Dia.DiaSemana();
                var lItem = lRetorno.First(i => i.Dia == lDia);
                lItem.ValorSemanaAtual = lPgto.Valor;
            }

            #endregion

            #region Anterior

            var lFinalSemanaAnterior = DateTime.Now.Date.StartOfWeek(DayOfWeek.Monday).AddDays(-1).EndOfDay();
            lPagamentos = await _viagemAppService.ConsultarPagamentosDia(lFinalSemanaAnterior.Date.StartOfWeek(DayOfWeek.Monday), lFinalSemanaAnterior, empresaId,true);
            foreach (var lPgto in lPagamentos)
            {
                var lDia = lPgto.Dia.DiaSemana();
                var lItem = lRetorno.First(i => i.Dia == lDia);
                lItem.ValorSemanaAnterior = lPgto.Valor ?? 0;
            }

            #endregion

            #region Mesma hora da semana anterior

            lPagamentos = await _viagemAppService.ConsultarPagamentosDia(DateTime.Today.AddDays(-7), DateTime.Now.AddDays(-7), empresaId, true);

            var lDiaAtual = lRetorno.First(i => i.Dia == DateTime.Now.DiaSemana());
            lDiaAtual.ValorMesmaHoraSemanaAnterior = lPagamentos.FirstOrDefault()?.Valor??0;

            #endregion

            return new PagamentoDiaGraficoAngular(lRetorno);
        }
        catch (Exception e)
        {
            LogManager.GetCurrentClassLogger().Error(e);
            return new PagamentoDiaGraficoAngular();
        }
    }
    
    public async Task<List<TotalizadorItem>> GetPagamentosPorDia(int empresaId, DateTime? dataBase)
    {
        try
        {
            return await _viagemAppService.ConsultarPagamentosPorDiaGrid(empresaId, dataBase ?? DateTime.Now);
        }
        catch (Exception e)
        {
            LogManager.GetCurrentClassLogger().Error(e);
            return new List<TotalizadorItem>();
        }
    }
    
    public async Task<List<ResumoDiaLinha>> GetResumoDia(int empresaId, DateTime? dataBase)
    {
           try
           {
                var dataCorrespondente = dataBase ?? DateTime.Now;
                var (primeiroDia, ultimoDia) = dataCorrespondente.ObterInicioEFimDaSemana();
                
                var lCompensado = dataCorrespondente.Hour >= 16;
                var lDataInicioUtilizado = primeiroDia.AddDays(-1);
                var cnpjEmpresa = await _empresaReadRepository.GetCnpjAsync(empresaId);
                var contaFrete = await _empresaReadRepository.GetContaFreteByCnpjAsync(cnpjEmpresa);
            
            
                while (lDataInicioUtilizado.DayOfWeek == DayOfWeek.Saturday || lDataInicioUtilizado.DayOfWeek == DayOfWeek.Sunday)
                    lDataInicioUtilizado = lDataInicioUtilizado.AddDays(-1);
                SaldoResponse saldo;
            
                if (contaFrete != null)
                {
                     saldo = _contaAppService.ConsultarSaldo((int)contaFrete);
                }
                else
                {
                    var conta = await _cartaoRepository.ConsultarContas(null, null, cpfcnpj: cnpjEmpresa);
                    saldo = _contaAppService.ConsultarSaldo(conta.content?
                        .FirstOrDefault(x => StatusConta().Contains(x.idStatusConta))
                        ?.id ?? 0);  
                }

               
                var (inicioDoDia, fimDoDia) = dataCorrespondente.AddDays(-1).ObterInicioEFimDataHora();
                var lPagamentos = await _viagemAppService.ConsultarPagamentosDia(inicioDoDia, fimDoDia ,empresaId, true);
                var lPagamento = lPagamentos.Sum(x => x.Valor) ?? 0m;
                return
                [
                    new ResumoDiaLinha { Descricao = "Saldo", Valor = saldo.Saldo.MonetaryStringToDecimal() },
                    new ResumoDiaLinha
                    {
                        Descricao = "Compensação " + (lCompensado ? "paga" : "a pagar"),
                        Valor = lPagamento,
                        StyleCss = "{ 'font-weight': bold,  'font-size': '12px', 'color': " +
                                   (lCompensado ? "'green'" : "'red'") + "}"
                    }
                ];
           }
           catch (Exception e)
           {
               LogManager.GetCurrentClassLogger().Error(e);
               return new List<ResumoDiaLinha>();
           }
    }
    
    public async Task<List<EmpresaHabilitadosResponse>> GetEmpresaHabilitados()
    {
        try
        {
            var quantidadeLayout = await
                _parametrosReadRepository.GetByTipoDoParametroAsync(Domain.Models.Parametros.Parametros.TipoDoParametro
                    .QuantidadeLayoutTelao);
                
            var listaEmpresasIds = _usuarioReadRepository.GetEmpresasAcessoUsuario(Engine.User.Id).Result;
            var lEmpresaHabilitada = _empresaReadRepository.GetHabilitadosParaTelaoSaldo(listaEmpresasIds, quantidadeLayout?.Valor.ToIntSafe());
            
            return await  lEmpresaHabilitada.ProjectTo<EmpresaHabilitadosResponse>(Engine.Mapper.ConfigurationProvider).ToListAsync();
        }
        catch (Exception e)
        {
            LogManager.GetCurrentClassLogger().Error(e);
            return null;
        }
    }
    
    public async Task<RespPadrao> ConsultaParametrosConfiguracaoTelaoSaldo()
    {
        try
        {
            var parametroLayout = await
                _parametrosReadRepository.GetParametrosAsync(-3,
                    Domain.Models.Parametros.Parametros.TipoDoParametro.QuantidadeLayoutTelao,
                    Domain.Models.Parametros.Parametros.TipoDoValor.Number);
            var parametroTempoAtualizacao = await
                _parametrosReadRepository.GetParametrosAsync(-3,
                    Domain.Models.Parametros.Parametros.TipoDoParametro.TempoAtualizacaoTelao,
                    Domain.Models.Parametros.Parametros.TipoDoValor.Number);
            var parametroTempoPaginacao = await
                _parametrosReadRepository.GetParametrosAsync(-3,
                    Domain.Models.Parametros.Parametros.TipoDoParametro.TempoPaginacaoTelao,
                    Domain.Models.Parametros.Parametros.TipoDoValor.Number);

            var response = new ConfiguracaoTelaoSaldoResponse()
            {
                tempoAtualizacaoTelao = parametroTempoAtualizacao.Valor.ToIntSafe(),
                QuantidadeLayoutTelao = parametroLayout.Valor.ToIntSafe(),
                TempoPaginacaoTelao = parametroTempoPaginacao.Valor.ToIntSafe()
            };
            
            
            return new RespPadrao
            {
                data = response,
                sucesso = true,
                mensagem = "Parâmetros de Vale Pedágio consultados com sucesso."
            };
        }
        catch (Exception e)
        {
            LogManager.GetCurrentClassLogger().Error(e);
            return new RespPadrao(false, e.Message);
        }
    }
    private List<int> StatusConta()
    {
        return new List<int>()
        {
            0,
            200,
            10,
            109
        };
    }
}