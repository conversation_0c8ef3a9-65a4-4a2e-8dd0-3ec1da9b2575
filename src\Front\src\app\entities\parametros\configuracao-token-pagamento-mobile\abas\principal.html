<style>
.input-group-custom {
    position: relative;
    margin-bottom: 20px;
}

.input-group-custom input {
    width: 100%;
    padding: 10px 40px 10px 10px;
    border: 1px solid #ccc;
    border-radius: 8px;
    transition: border-color 0.3s;
    font-size: 14px;
    background-color: #fff;
}

.input-group-custom input:focus {
    outline: none;
    border-color: #4a90e2;
    box-shadow: 0 0 5px rgba(74, 144, 226, 0.5);
}

.input-group-custom label {
    position: absolute;
    top: -10px;
    left: 10px;
    background-color: white;
    padding: 0 5px;
    font-size: 13px;
    color: #4a4a4a;
    font-weight: 600;
}

.custom-toggle {
    position: absolute;
    top: 50%;
    right: 10px;
    transform: translateY(-50%);
    cursor: pointer;
    color: #999;
    z-index: 10;
}

.custom-toggle:hover {
    color: #4a90e2;
}

.input-group-text i {
    font-size: 16px;
}


</style>

<div class="col-xs-12 col-sm-12 col-md-12 col-lg-12">
    <div class="row">
        <div class="col-xs-12 col-md-6">
            <div class="input-group-custom">
            <label for="login">Login</label>
            <input type="text"
                    id="login"
                    autocomplete="off"
                    ng-model="vm.configuracaoTokenMobile.configuracaoTokenMobileLogin"
                    name="ConfiguracaoTokenMobileLogin"
                    required
                    class="form-control" />
            </div>
        </div>

        <div class="col-xs-12 col-md-6">
            <div class="input-group-custom">
            <label for="clientSecret">Client Secret</label>
            <input type="{{showClientSecret ? 'text' : 'password'}}"
                    ng-model="vm.configuracaoTokenMobile.configuracaoTokenMobileClientSecret"
                    id="clientSecret"
                    autocomplete="new-password"
                    name="ConfiguracaoTokenMobileClientSecret"
                    required
                    class="form-control" />
            <span class="custom-toggle" ng-click="showClientSecret = !showClientSecret">
                <i class="fa" ng-class="{'fa-eye': !showClientSecret, 'fa-eye-slash': showClientSecret}"></i>
            </span>
            </div>
        </div>

        <div class="col-xs-12 col-md-6">
            <div class="input-group-custom">
            <label for="senha">Senha</label>
            <input type="{{showSenha ? 'text' : 'password'}}"
                    ng-model="vm.configuracaoTokenMobile.configuracaoTokenMobileSenha"
                    id="senha"
                    autocomplete="new-password"
                    name="ConfiguracaoTokenMobileSenha"
                    required
                    class="form-control" />
            <span class="custom-toggle" ng-click="showSenha = !showSenha">
                <i class="fa" ng-class="{'fa-eye': !showSenha, 'fa-eye-slash': showSenha}"></i>
            </span>
            </div>
        </div>
    </div>
</div>