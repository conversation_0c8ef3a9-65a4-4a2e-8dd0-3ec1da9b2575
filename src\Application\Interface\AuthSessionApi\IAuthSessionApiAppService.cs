﻿using System.Threading.Tasks;
using SistemaInfo.BBC.Application.Objects.Api.Token;
using SistemaInfo.BBC.Application.Objects.Base;
using SistemaInfo.BBC.Domain.Models.AuthSessionApi.Repository;
using SistemaInfo.Framework.DomainDrivenDesign.Application.Service;

namespace SistemaInfo.BBC.Application.Interface.AuthSessionApi
{
    public interface IAuthSessionApiAppService : IAppService<Domain.Models.AuthSessionApi.AuthSessionApi, IAuthSessionApiReadRepository, IAuthSessionApiWriteRepository>
    {
        Task<RespPadrao> GerarToken(TokenRequestIntegracao tokenRequest);  
        Task<RespPadrao> Login(TokenRequestIntegracao tokenRequest);
        Task<RespPadrao> GerarToken(TokenRequestMobilePagamentos tokenRequest);
        
    }
}