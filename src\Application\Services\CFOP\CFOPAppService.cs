﻿using System.Collections.Generic;
using System.Linq;
using System.Linq.Dynamic.Core;
using AutoMapper.QueryableExtensions;
using SistemaInfo.BBC.Application.Interface.CFOP;
using SistemaInfo.BBC.Application.Objects.Web.CFOP;
using SistemaInfo.BBC.Domain.Grid;
using SistemaInfo.BBC.Domain.Helper;
using SistemaInfo.BBC.Domain.Models.CFOP.Repository;
using SistemaInfo.Framework.DomainDrivenDesign.Application.Service;
using SistemaInfo.Framework.DomainDrivenDesign.Domain.Engine;

namespace SistemaInfo.BBC.Application.Services.CFOP
{
    public class CFOPAppService : AppService<Domain.Models.CFOP.CFOP,
        ICFOPReadRepository, ICFOPWriteRepository>, ICFOPAppService
    {

        public CFOPAppService(
            IAppEngine engine,
            ICFOPReadRepository readRepository,
            ICFOPWriteRepository writeRepository) : base(
            engine, readRepository, writeRepository)
        {
        }

        public ConsultarGridCFOPResponse ConsultarGridCFOP(int take, int page, OrderFilters orderFilters,
            List<QueryFilters> filters)
        {
         
            IQueryable<Domain.Models.CFOP.CFOP> lCFOP;

            lCFOP = Repository.Query.GetAll();

            lCFOP = lCFOP.AplicarFiltrosDinamicos(filters);
            lCFOP = string.IsNullOrWhiteSpace(orderFilters?.Campo)
                ? lCFOP.OrderByDescending(o => o.Id)
                : lCFOP.OrderBy($"{orderFilters.Campo} {orderFilters.Operador.DescriptionAttr()}");

            var lCount = lCFOP.Count();
            var retorno = lCFOP.Skip((page - 1) * take)
                .Take(take).ProjectTo<ConsultarGridCFOP>(Engine.Mapper.ConfigurationProvider).ToList();

            return new ConsultarGridCFOPResponse
            {
                items = retorno,
                totalItems = lCount
            };
        }
    }
}
