﻿using System;
using System.Collections.Generic;
using System.Linq;
using SistemaInfo.BBC.Application.Objects.Api.Documento;
using SistemaInfo.BBC.Application.Objects.Api.Portador;
using SistemaInfo.BBC.Application.Objects.Mobile.Posto;
using SistemaInfo.BBC.Application.Objects.Web.Empresa;
using SistemaInfo.BBC.Application.Objects.Web.Portador;
using SistemaInfo.BBC.Application.Objects.Web.PortadorCentroCusto;
using SistemaInfo.BBC.Application.Objects.Web.Transportador;
using SistemaInfo.BBC.Domain.Enum;
using SistemaInfo.BBC.Domain.External.Conductor.DTO.Cartao;
using SistemaInfo.BBC.Domain.External.Conductor.DTO.Individuo;
using SistemaInfo.BBC.Domain.Helper;
using SistemaInfo.BBC.Domain.Models.Documento.Commands;
using SistemaInfo.BBC.Domain.Models.Empresa.Commands;
using SistemaInfo.BBC.Domain.Models.Portador;
using SistemaInfo.BBC.Domain.Models.Portador.Commands;
using SistemaInfo.BBC.Domain.Models.PortadorCentroCusto;
using SistemaInfo.BBC.Domain.Models.PortadorCentroCusto.Commands;
using SistemaInfo.BBC.Domain.Models.PortadorEmpresa;
using SistemaInfo.BBC.Domain.Models.PortadorEmpresa.Commands;
using SistemaInfo.BBC.Domain.Models.PortadorRepresentanteLegal;
using SistemaInfo.Framework.Utils;

namespace SistemaInfo.BBC.Application.AutoMapper.PortadorMapper
{
    public class PortadorMappingProfile : SistemaInfoMappingProfile
    {
        public PortadorMappingProfile()
        {
            CreateMap<Portador, ConsultarPorIdPortadorResponse>()
                .ForMember(dest => dest.UfEmissao, opt => opt.MapFrom(src => src.UfEmissao.ToIntSafe(0)))
                .ForMember(dest => dest.ControlaAbastecimentoCentroCusto, opt => opt.MapFrom(src => src.ControlaAbastecimentoCentroCusto == 1 ? true : false))
                .ForMember(dest => dest.TipoPessoa, opts => opts.MapFrom(s => s.TipoPessoa.GetHashCode()))
                .ForMember(dest => dest.Sexo, opts => opts.MapFrom(s => s.Sexo.GetHashCode()))
                .ForMember(dest => dest.Atividade, opts => opts.MapFrom(s => s.Atividade.GetHashCode()))
                .ForPath(dest => dest.RepLegaisList, opts => opts.MapFrom(s => new List<PortadorRepLegalResponse>()));

            CreateMap<Portador, PortadorConsultarCpfCnpjApiResponse>()
                .ForMember(dest => dest.UfEmissao, opt => opt.MapFrom(src => src.UfEmissao.ToIntSafe(0)))
                .ForMember(dest => dest.ControlaAbastecimentoCentroCusto, opt => opt.MapFrom(src => src.ControlaAbastecimentoCentroCusto == 1 ? true : false))
                .ForMember(dest => dest.TipoPessoa, opts => opts.MapFrom(s => s.TipoPessoa.GetHashCode()))
                .ForMember(dest => dest.Sexo, opts => opts.MapFrom(s => s.Sexo.GetHashCode()))
                .ForMember(dest => dest.Atividade, opts => opts.MapFrom(s => s.Atividade.GetHashCode()));

            CreateMap<Portador, ConsultarGridPortador>()
                .ForMember(dest => dest.rntrc, opt => opt.MapFrom(src => src.RNTRC))
                .ForMember(dest => dest.cpfCnpj, opt => opt.MapFrom(src => FormatUtils.CpfCnpj(src.CpfCnpj)))
                .ForMember(dest => dest.telefone, opt => opt.MapFrom(src => src.Telefone.ToTelefoneFormato()));
            
            CreateMap<Portador, ConsultarGridPortadorReduzido>()
                .ForMember(dest => dest.CpfCnpj, opt => opt.MapFrom(src => FormatUtils.CpfCnpj(src.CpfCnpj)));

            CreateMap<PortadorRepresentanteLegal, PortadorRepLegalResponse>()
                .ForMember(dest => dest.Id, opts => opts.MapFrom(s => s.PortadorRepresentanteId))
                .ForMember(dest => dest.Nome, opts => opts.MapFrom(s => s.PortadorRepresentante.Nome))
                .ForMember(dest => dest.CpfCnpj, opts => opts.MapFrom(s => s.PortadorRepresentante.CpfCnpj));

            CreateMap<ConsultarContaResp, PortadorContaResponse>()
                .ForMember(d => d.ContaId, opt => opt.MapFrom(s => s.content.FirstOrDefault().id));

            CreateMap<PortadorCentroCusto, PortadorCentroCustoResp>()
                .ForMember(dest => dest.Descricao, opts => opts.MapFrom(s => s.CentroCusto.Descricao));

            CreateMap<ItensUsuarioJuridicoConductor, ItensUsuarioConductor>()
                .ForMember(dest => dest.id, opts => opts.MapFrom(s => s.id));

            CreateMap<Portador, ConsultarGridTransportador>()
                .ForMember(dest => dest.CpfCnpj, opt => opt.MapFrom(src => FormatUtils.CpfCnpj(src.CpfCnpj)))
                .ForMember(dest => dest.TipoPessoa, opt => opt.MapFrom(src => src.TipoPessoa == ETipoPessoa.Fisica ? "Fisica" : "Jurídica"));

            CreateMap<Portador, TransportadorResponse>()
                .ForMember(dest => dest.TipoPessoa, opt => opt.MapFrom(src => src.TipoPessoa == ETipoPessoa.Fisica ? 1 : 2))
                .ForMember(dest => dest.CiotTacAgregado, opt => opt.MapFrom(src => src.CiotTacAgregado == 1));

            CreateMap<PortadorSalvarCommand, Portador>()
                .ForMember(p => p.SenhaApi, opts => opts.MapFrom(e => e.SenhaApi));
            
            CreateMap<PortadorSalvarComRetornoCommand, Portador>();
            
            CreateMap<PortadorRequest, PortadorSalvarComRetornoCommand>()
                .ForMember(p => p.CpfCnpj, opts => opts.MapFrom(e => e.CpfCnpj.OnlyNumbers()))
                .ForMember(p => p.Telefone, opts => opts.MapFrom(e => e.Telefone.OnlyNumbers()))
                .ForMember(p => p.ControlaAbastecimentoCentroCusto, opts => opts.MapFrom(e => e.ControlaAbastecimentoCentroCusto == true ? 1 : 0))
                .ForMember(p => p.Celular, opts => opts.MapFrom(e => e.Celular.OnlyNumbers()))
                .ForMember(p => p.EnderecoNumero, opts => opts.MapFrom(e => e.EnderecoNumero.OnlyNumbers()))
                .ForMember(p => p.Cep, opts => opts.MapFrom(e => e.Cep.OnlyNumbers()));

            CreateMap<PortadorRequest, PortadorSalvarCommand>()
                .ForMember(p => p.CpfCnpj, opts => opts.MapFrom(e => e.CpfCnpj.OnlyNumbers()))
                .ForMember(p => p.Telefone, opts => opts.MapFrom(e => e.Telefone.OnlyNumbers()))
                .ForMember(p => p.ControlaAbastecimentoCentroCusto, opts => opts.MapFrom(e => e.ControlaAbastecimentoCentroCusto == true ? 1 : 0))
                .ForMember(p => p.Celular, opts => opts.MapFrom(e => e.Celular.OnlyNumbers()))
                .ForMember(p => p.EnderecoNumero, opts => opts.MapFrom(e => e.EnderecoNumero.OnlyNumbers()))
                .ForMember(p => p.Cep, opts => opts.MapFrom(e => e.Cep.OnlyNumbers()));
            
            CreateMap<PortadorStatusRequest, PortadorAlterarStatusCommand>();

            CreateMap<EmpresaRequest, PortadorSalvarCommand>()
                .ForMember(p => p.Nome, opts => opts.MapFrom(e => e.NomeFantasia))
                .ForMember(p => p.CpfCnpj, opts => opts.MapFrom(e => e.Cnpj.OnlyNumbers()))
                .ForMember(p => p.Telefone, opts => opts.MapFrom(e => e.Telefone.OnlyNumbers()))
                .ForMember(p => p.Celular, opts => opts.MapFrom(e => e.Celular.OnlyNumbers()))
                .ForMember(p => p.EnderecoNumero, opts => opts.MapFrom(e => e.EnderecoNumero.OnlyNumbers()))
                .ForMember(p => p.Cep, opts => opts.MapFrom(e => e.Cep.OnlyNumbers()));
            
            CreateMap<PortadorRequest, ContaPessoaFisicaReq>()
                .ForMember(c => c.name, opts => opts.MapFrom(p => p.Nome))
                .ForMember(c => c.motherName, opts => opts.MapFrom(p => p.NomeMae))
                .ForMember(c => c.birthDate, opts => opts.MapFrom(p => Convert.ToDateTime(p.DataNascimento).ToString("yyyy-MM-dd")))
                .ForMember(c => c.document, opts => opts.MapFrom(p => p.CpfCnpj))
                .ForMember(c => c.idNumber, opts => opts.MapFrom(p => p.NumeroIdentidade))
                .ForMember(c => c.dueDate, opts => opts.MapFrom(p => 10))
                .ForMember(c => c.identityIssuingEntity, opts => opts.MapFrom(p => p.OrgaoEmissor))
                .ForMember(c => c.federativeUnit, opts => opts.MapFrom(p => p.UfEmissaoSigla))
                .ForMember(c => c.issuingDateIdentity, opts => opts.MapFrom(p => Convert.ToDateTime(p.EmissaoIdentidade).ToString("yyyy-MM-dd")))
                .ForMember(c => c.email, opts => opts.MapFrom(p => p.Email))
                .ForPath(c => c.address.zipCode, opts => opts.MapFrom(p => p.Cep))
                .ForPath(c => c.address.street, opts => opts.MapFrom(p => p.Endereco))
                .ForPath(c => c.address.number, opts => opts.MapFrom(p => p.EnderecoNumero.ToInt()))
                .ForPath(c => c.address.complement, opts => opts.MapFrom(p => p.Complemento))
                .ForPath(c => c.address.neighborhood, opts => opts.MapFrom(p => p.Bairro))
                .ForPath(c => c.address.city, opts => opts.MapFrom(p => p.NomeCidade))
                .ForPath(c => c.address.federativeUnit, opts => opts.MapFrom(p => p.UfEstado))
                .ForPath(c => c.phone.areaCode, opts => opts.MapFrom(p => "0" + p.Celular.Substring(1, 2)))
                .ForPath(c => c.phone.number, opts => opts.MapFrom(p => p.Celular.Substring(2)));
            
            CreateMap<Portador, PortadorSalvarCommand>();

            CreateMap<PortadorRequest, ContaPessoaJuridicaReq>();

            CreateMap<Portador, ContaPessoaJuridicaReq>();

            CreateMap<EmpresaSaveCommand, Portador>();
            
            CreateMap<IntegrarPortadorRequest, PortadorSalvarCommand>()
                .ForMember(p => p.CpfCnpj, opts => opts.MapFrom(e => e.CpfCnpj.OnlyNumbers()))
                .ForMember(p => p.Telefone, opts => opts.MapFrom(e => e.Telefone.OnlyNumbers()))
                .ForMember(p => p.Celular, opts => opts.MapFrom(e => e.Celular.OnlyNumbers()))
                .ForMember(p => p.EnderecoNumero, opts => opts.MapFrom(e => e.EnderecoNumero.OnlyNumbers()))
                .ForMember(p => p.Cep, opts => opts.MapFrom(e => e.Cep.OnlyNumbers()))
                .ForMember(p => p.CidadeId, opts => opts.MapFrom(e => e.CidadeIbgeId));

            CreateMap<IntegrarPortadorRequest, PortadorRequest>()
                .ForMember(p => p.CidadeId, opts => opts.MapFrom(e => e.CidadeIbgeId))
                .ForMember(p => p.UfEmissaoSigla, opts => opts.MapFrom(e => e.UfEmissao))
                .ForMember(p => p.UfEmissao, opts => opts.Ignore());

            CreateMap<PortadorEmpresaSalvarCommand, PortadorEmpresa>();
            CreateMap<PortadorEmpresaRequest, PortadorEmpresaSalvarCommand>();
            
            CreateMap<PortadorCentroCustoSalvarCommand, PortadorCentroCusto>();
            CreateMap<PortadorCentroCustoRequest, PortadorCentroCustoSalvarComRetornoCommand>()
                .ForMember(dest => dest.CentroCustoId, opts => opts.MapFrom(s => s.centroCustoId));

            CreateMap<DocumentoSalvarCommand, DocumentoRequest>();
            
            //mobile
            CreateMap<ConsultarPorIdPortadorResponse, PortadorSalvarCommand>();

            CreateMap<TransportadorRequest, TransportadorSalvarCommand>();
            
            CreateMap<TransportadorRequest, TransportadorSalvarComRetornoCommand>();

            CreateMap<TransportadorSalvarCommand, Portador>();

            CreateMap<TransportadorSalvarComRetornoCommand, Portador>();

        }
    }
}