using System;
using System.Collections.Generic;
using System.Globalization;
using System.Linq;
using System.Linq.Dynamic.Core;
using System.Threading.Tasks;
using AutoMapper.QueryableExtensions;
using Microsoft.EntityFrameworkCore;
using Newtonsoft.Json;
using SistemaInfo.BBC.Application.Interface.AutorizacaoAbastecimento;
using SistemaInfo.BBC.Application.Interface.AutorizacaoContingecia;
using SistemaInfo.BBC.Application.Interface.PagamentoAbastecimento;
using SistemaInfo.BBC.Application.Interface.Veiculo;
using SistemaInfo.BBC.Application.Objects.Base;
using SistemaInfo.BBC.Application.Objects.Web.AutorizacaoContingecia;
using SistemaInfo.BBC.Domain.Enum;
using SistemaInfo.BBC.Domain.External.Movida.DTO.Movida;
using SistemaInfo.BBC.Domain.External.Movida.Interface;
using SistemaInfo.BBC.Domain.Grid;
using SistemaInfo.BBC.Domain.Helper;
using SistemaInfo.BBC.Domain.Models.Abastecimento.Repository;
using SistemaInfo.BBC.Domain.Models.Abastecimento.Commands;
using SistemaInfo.BBC.Domain.Models.AutorizacaoAbastecimento.Commands;
using SistemaInfo.BBC.Domain.Models.Empresa.Repository;
using SistemaInfo.BBC.Domain.Models.Filial.Repository;
using SistemaInfo.BBC.Domain.Models.Portador.Repository;
using SistemaInfo.BBC.Domain.Models.PortadorCentroCusto.Repository;
using SistemaInfo.BBC.Domain.Models.PortadorEmpresa.Repository;
using SistemaInfo.BBC.Domain.Models.Posto.Repository;
using SistemaInfo.BBC.Domain.Models.PostoCombustivel.Repository;
using SistemaInfo.BBC.Domain.Models.Veiculo.Commands;
using SistemaInfo.BBC.Domain.Models.VeiculoCombustivel.Repository;
using SistemaInfo.BBC.Domain.Models.VeiculoEmpresa.Repository;
using SistemaInfo.Framework.DomainDrivenDesign.Application.Service;
using SistemaInfo.Framework.DomainDrivenDesign.Domain.Engine;
using SistemaInfo.Framework.Utils;

namespace SistemaInfo.BBC.Application.Services.AutorizacaoContingecia
{
    public class AutorizacaoContingeciaAppService : AppService<Domain.Models.Abastecimento.Abastecimento,
        IAbastecimentoReadRepository, IAbastecimentoWriteRepository>, IAutorizacaoContingeciaAppService
    {
        private readonly IPortadorReadRepository _portadorReadRepository;
        private readonly IEmpresaReadRepository _empresaReadRepository;
        private readonly IPortadorEmpresaReadRepository _portadorEmpresaReadRepository;
        private readonly IPostoCombustivelReadRepository _postoCombustivelReadRepository;
        private readonly IVeiculoAppService _veiculoAppService;
        private readonly IVeiculoCombustivelReadRepository _veiculoCombustivelReadRepository;
        private readonly IPortadorCentroCustoReadRepository _portadorCentroCustoReadRepository;
        private readonly IVeiculoEmpresaReadRepository _veiculoEmpresaReadRepository;
        private readonly IAbastecimentoMovidaRepository _movidaRepository;
        private readonly IFilialReadRepository _filialReadRepository;
        private readonly IPostoReadRepository _postoReadRepository;
        private readonly IAutorizacaoAbastecimentoAppService _autorizacaoAbastecimentoAppService;
        private readonly IPagamentoAbastecimentoAppService _pagamentoAbastecimentoAppService;

        public AutorizacaoContingeciaAppService(
            IAppEngine engine,
            IAbastecimentoReadRepository readRepository,
            IPortadorReadRepository portadorReadRepository,
            IEmpresaReadRepository empresaReadRepository,
            IPortadorEmpresaReadRepository portadorEmpresaReadRepository,
            IPostoCombustivelReadRepository postoCombustivelReadRepository,
            IVeiculoAppService veiculoAppService,
            IVeiculoCombustivelReadRepository veiculoCombustivelReadRepository,
            IPortadorCentroCustoReadRepository portadorCentroCustoReadRepository,
            IVeiculoEmpresaReadRepository veiculoEmpresaReadRepository,
            IFilialReadRepository filialReadRepository,
            IPostoReadRepository postoReadRepository,
            IAbastecimentoMovidaRepository movidaRepository,
            IAbastecimentoWriteRepository writeRepository,
            IAutorizacaoAbastecimentoAppService autorizacaoAbastecimentoAppService,
            IPagamentoAbastecimentoAppService pagamentoAbastecimentoAppService) : base(
            engine, readRepository, writeRepository)
        {
            _portadorReadRepository = portadorReadRepository;
            _empresaReadRepository = empresaReadRepository;
            _portadorEmpresaReadRepository = portadorEmpresaReadRepository;
            _postoCombustivelReadRepository = postoCombustivelReadRepository;
            _portadorCentroCustoReadRepository = portadorCentroCustoReadRepository;
            _veiculoAppService = veiculoAppService;
            _veiculoCombustivelReadRepository = veiculoCombustivelReadRepository;
            _veiculoEmpresaReadRepository = veiculoEmpresaReadRepository;
            _movidaRepository = movidaRepository;
            _filialReadRepository = filialReadRepository;
            _postoReadRepository = postoReadRepository;
            _autorizacaoAbastecimentoAppService = autorizacaoAbastecimentoAppService;
            _pagamentoAbastecimentoAppService = pagamentoAbastecimentoAppService;
        }

        public ConsultarGridHistoricoAutorizacaoContingeciaResponse ConsultarGridHistoricoSolicitacoesPendentes(int IdPosto, DateTime dtInicial, DateTime dtFinal, int status, int take, int page, OrderFilters orderFilters,
            List<QueryFilters> filters)
        {
            IQueryable<Domain.Models.Abastecimento.Abastecimento> lHistoricoSolicitacoesPendentes;

            lHistoricoSolicitacoesPendentes = Repository.Query.GetAll();

            if (IdPosto > 0)
            {
                lHistoricoSolicitacoesPendentes = lHistoricoSolicitacoesPendentes.Where(x => x.PostoId == IdPosto);
            }
            
            if(dtInicial > ("01/01/0001 00:00:00").ToDateTime() || dtFinal > ("01/01/0001 00:00:00").ToDateTime())
            {
                var dtIni = dtInicial.ToShortDateString().ToDateTime();
                var dtFim = dtFinal.ToShortDateString().ToDateTime().AddDays(1).AddSeconds(-1);

                lHistoricoSolicitacoesPendentes = lHistoricoSolicitacoesPendentes.Where(p => p.DataCadastro >= dtIni && p.DataCadastro <= dtFim);
            }
            
            lHistoricoSolicitacoesPendentes = lHistoricoSolicitacoesPendentes.Where(x => x.Status != EStatusAbastecimento.AguardandoAprovacao);

            lHistoricoSolicitacoesPendentes = lHistoricoSolicitacoesPendentes.AplicarFiltrosDinamicos<Domain.Models.Abastecimento.Abastecimento>(filters);
            lHistoricoSolicitacoesPendentes = string.IsNullOrWhiteSpace(orderFilters?.Campo)
                ? lHistoricoSolicitacoesPendentes.OrderByDescending(o => o.Id)
                : lHistoricoSolicitacoesPendentes.OrderBy($"{orderFilters.Campo} {orderFilters.Operador.DescriptionAttr()}");

            var lCount = lHistoricoSolicitacoesPendentes.Count();
            var retorno = lHistoricoSolicitacoesPendentes.Skip((page - 1) * take)
                .Take(take).ProjectTo<ConsultarGridHistoricoAutorizacaoContingecia>(Engine.Mapper.ConfigurationProvider).ToList();

            return new ConsultarGridHistoricoAutorizacaoContingeciaResponse
            {
                items = retorno,
                totalItems = lCount
            };
        }

        public List<AutorizacaoContingeciaResponse> SolicitacoesPendentes(int idPosto, DateTime dataInicio, DateTime dataFim)
        {
            var dtInicio = dataInicio.ToShortDateString().ToDateTime();
            var dtFim = dataFim.ToShortDateString().ToDateTime().AddDays(1).AddSeconds(-1);

            if (idPosto == 0)
            {
                return Repository.Query
                    .Include(c => c.Combustivel)
                    .Include(p => p.Posto)
                    .Include(v=>v.Veiculo)
                    .Include(u=>u.UsuarioAlteracao)
                    .Include(u=>u.UsuarioCadastro)
                    .Where(a => a.DataCadastro >= dtInicio && a.DataCadastro <= dtFim && a.Status == EStatusAbastecimento.AguardandoAprovacao)
                    .ProjectTo<AutorizacaoContingeciaResponse>().ToList();
            }
            
            return Repository.Query
                .Include(c => c.Combustivel)
                .Include(p => p.Posto)
                .Include(v=>v.Veiculo)
                .Include(u=>u.UsuarioAlteracao)
                .Include(u=>u.UsuarioCadastro)
                .Where(a => a.DataCadastro >= dtInicio && a.DataCadastro <= dtFim && a.PostoId == idPosto && a.Status == EStatusAbastecimento.AguardandoAprovacao)
                .ProjectTo<AutorizacaoContingeciaResponse>().ToList();
        }
        
        public async Task<RespPadrao> SaveAprovacao(AutorizacaoContingeciaRequest lAutorizacaoContingeciaReq, bool integracaoJaFeita = false)
        {
            try
            {
                var lAbastecimentoIntegrados = "";
                var lAbastecimentosNaoIntegrados = "";
                var aprovacao = false;
                var retornoMensagemMovida = "";

                foreach (var lista in lAutorizacaoContingeciaReq.AutorizacaoContingeciaRequestItems)
                {
                    var abastecimentoConsulta = Repository.Query.Where(x => x.Id == lista.Id).FirstOrDefault();

                    abastecimentoConsulta.Status = lista.Status;
                    abastecimentoConsulta.Motivo = lista.Motivo;
                    abastecimentoConsulta.UsuarioAlteracaoId = Engine.User.Id;

                    var abstecimentoUpd = Mapper.Map<Domain.Models.Abastecimento.Abastecimento>(abastecimentoConsulta);
                    
                    if (lista.Status == EStatusAbastecimento.Aprovado)
                    {
                        aprovacao = true;
                        
                        var lVeiculo = _veiculoAppService.Repository.Query.Where(x => x.Id == abstecimentoUpd.VeiculoId)
                            .FirstOrDefault();

                        var lEmpresa = _empresaReadRepository.Where(x => x.Id == abstecimentoUpd.EmpresaId)
                            .Include(x => x.TipoEmpresa)
                            .FirstOrDefault();

                        var lPortador = _portadorReadRepository.Where(x => x.Id == abstecimentoUpd.PortadorId)
                            .First();

                        var lPosto = _postoReadRepository.Where(x => x.Id == abstecimentoUpd.PostoId)
                            .First();

                        if (lEmpresa == null)
                        {
                            return new RespPadrao()
                            {
                                sucesso = false,
                                mensagem = "Empresa não encontrada!"
                            };
                        }
                        
                        var lTipoEmpresa = false;
                        var tipoEmpresa = lEmpresa.TipoEmpresa?.Nome;
                        var tipoValidador = "Movida";
                            
                        lTipoEmpresa = tipoEmpresa?.ToUpper() == tipoValidador.ToUpper();
                        
                        if (lEmpresa.TipoEmpresaId == null || !String.Equals(tipoEmpresa, tipoValidador, StringComparison.CurrentCultureIgnoreCase))
                        {
                            var lFilial = _filialReadRepository.Where(x => x.Id == lVeiculo.FilialId)
                                .FirstOrDefault();

                            if (lFilial == null)
                            {
                                return new RespPadrao()
                                {
                                    sucesso = false,
                                    mensagem = "Filial não encontrada!"
                                };
                            }

                            lEmpresa = _empresaReadRepository.Where(x => x.Id == lFilial.EmpresaId)
                                .Include(x => x.TipoEmpresa)
                                .FirstOrDefault();
                        }
                        
                        if (lTipoEmpresa && !integracaoJaFeita)
                        {
                            //constroi objeto para external
                            var abastecimentoEnvio = new MovidaReq()
                            {
                                Placa = abstecimentoUpd.Veiculo.Placa,
                                DataAbastecimento = abstecimentoUpd.DataCadastro.ToString(CultureInfo.CurrentCulture),
                                LitragemAbastecida = abstecimentoUpd.Litragem,
                                CnpjPosto = lPosto.Cnpj,
                                ValorAbastecimento = abstecimentoUpd.ValorAbastecimento.Round(),
                                IntegracaoID = (int) abstecimentoUpd.AutorizacaoAbastecimentoId,
                                AbastecimentoID = abstecimentoUpd.Id,
                                ValorTaxaBBC = lEmpresa.TaxaAbastecimento,
                                CpfFuncionario = lPortador.CpfCnpj,
                                OdometroInformado = abstecimentoUpd.Odometro
                            };

                            //Realiza o envio do processo
                            var retornoEnvioMovida = await _movidaRepository
                                .RealizaEnvioAbastecimentoMovida(abastecimentoEnvio, lEmpresa);

                            abstecimentoUpd.DataIntegracaoMovida = DateTime.Now;
                            abstecimentoUpd.JsonEnvioMovida = JsonConvert.SerializeObject(abastecimentoEnvio);
                            abstecimentoUpd.RetornoMovida = JsonConvert.SerializeObject(retornoEnvioMovida);
                            abstecimentoUpd.ContadorIntegracaoMovida = abstecimentoUpd.ContadorIntegracaoMovida != null ? 
                                abstecimentoUpd.ContadorIntegracaoMovida + 1 : 0;

                            if (retornoEnvioMovida != null && retornoEnvioMovida.Success)
                            {
                                abstecimentoUpd.IntegracaoMovida = 1;
                                
                                lAbastecimentoIntegrados += abstecimentoUpd.Id + ", ";
                            }
                            else
                            {
                                abstecimentoUpd.IntegracaoMovida = 0;
                                
                                lAbastecimentosNaoIntegrados += abstecimentoUpd.Id + ", ";
                                
                                retornoMensagemMovida = "<br><br>Mensagem retorno MOVIDA:<br>" + retornoEnvioMovida?.Msg;
                            }
                        }

                        #region Retencao
                        
                        if (abastecimentoConsulta.Veiculo?.Empresa?.ControlaContingencia == 1 
                            && abastecimentoConsulta.TipoDebito == ETipoDebitoAbastecimento.Abastecimento)
                        {
                        
                            var retencaoSolicitacao = _pagamentoAbastecimentoAppService.IntegrarRetencao(abstecimentoUpd.Id, false).Result;

                            if (!retencaoSolicitacao.sucesso)
                            {
                                var abastecimentoUpdate = Repository.Query.FirstOrDefault(x => x.Id == abstecimentoUpd.Id);
                        
                                var commandUpdate = Mapper.Map<AbastecimentoSalvarComRetornoCommand>(abastecimentoUpdate);
                        
                                Repository.Query.Detach(abastecimentoUpdate);
                                
                                commandUpdate.Status = EStatusAbastecimento.AguardandoAprovacao;

                                var retornoUpdate = await Engine.CommandBus
                                    .SendCommandAsync<Domain.Models.Abastecimento.Abastecimento>(commandUpdate);
                        
                                return new RespPadrao
                                {
                                    id = retornoUpdate.Id,
                                    sucesso = false,
                                    mensagem = "Abastecimento não realizado. " + retencaoSolicitacao.mensagem
                                };
                            }
                        }

                        #endregion
                    } 
                    else if (lista.Status == EStatusAbastecimento.Reprovado)
                    {
                        abstecimentoUpd.Status = EStatusAbastecimento.Reprovado;

                        var lAutorizacaoAbastecimento = _autorizacaoAbastecimentoAppService.Repository.Query
                            .FirstOrDefault(x => x.Id == abstecimentoUpd.AutorizacaoAbastecimentoId);

                        if (lAutorizacaoAbastecimento != null)
                        {
                            var autorizacaoList =
                                Mapper.Map<AutorizacaoAbastecimentoSalvarCommand>(
                                    lAutorizacaoAbastecimento);
                            
                            _autorizacaoAbastecimentoAppService.Repository.Query.Detach(lAutorizacaoAbastecimento);
                            
                            autorizacaoList.LitragemUtilizada = lAutorizacaoAbastecimento.LitragemUtilizada -
                                                                abstecimentoUpd.Litragem;
                            autorizacaoList.Status = StatusAutorizacaoAbastecimento.Aberto;

                            await _autorizacaoAbastecimentoAppService.Engine.CommandBus
                                .SendCommandAsync<Domain.Models.AutorizacaoAbastecimento.AutorizacaoAbastecimento>(
                                    autorizacaoList);

                            if (abstecimentoUpd.Odometro != abstecimentoUpd.OdometroAnterior)
                            {
                                var veiculoRetornoOdometro = _veiculoAppService.Repository.Query.FirstOrDefault(x => x.Id == abstecimentoUpd.VeiculoId);

                                var veiculoList =
                                    Mapper.Map<VeiculoSalvarCommand>(veiculoRetornoOdometro);
                            
                                _veiculoAppService.Repository.Query.Detach(veiculoRetornoOdometro);
                            
                                veiculoList.Odometro = abstecimentoUpd.OdometroAnterior;
                                
                                await _veiculoAppService.Engine.CommandBus
                                    .SendCommandAsync<Domain.Models.Veiculo.Veiculo>(
                                        veiculoList);
                            }
                            
                        }
                    }
                    
                    var abastecimentoSave =
                        Mapper.Map<AbastecimentoSalvarComRetornoCommand>(abstecimentoUpd);
                            
                    Repository.Query.Detach(abstecimentoUpd);
                                
                    var retornoAbastecimentoSave = await Engine.CommandBus
                        .SendCommandAsync<Domain.Models.Abastecimento.Abastecimento>(abastecimentoSave);
                    
                    if (retornoAbastecimentoSave.Id <= 0)
                    {
                        throw new Exception();
                    }
                }

                //Sucesso do save com erro no envio
                if (aprovacao && (lAbastecimentoIntegrados != "" || lAbastecimentosNaoIntegrados != ""))
                {
                    var lAbastIntegrados = lAbastecimentoIntegrados.IsNullOrWhiteSpace()
                        ? ""
                        : lAbastecimentoIntegrados.Substring(0,
                            lAbastecimentoIntegrados.Length == 0 ? '0' : lAbastecimentoIntegrados.Length - 2);

                    var lAbastNaoIntegrados = lAbastecimentosNaoIntegrados.IsNullOrWhiteSpace()
                        ? ""
                        : lAbastecimentosNaoIntegrados.Substring(0,
                            lAbastecimentosNaoIntegrados.Length == 0 ? '0' : lAbastecimentosNaoIntegrados.Length - 2);
                    
                    return new RespPadrao
                    {
                        id = null,
                        sucesso = true,
                        mensagem =
                            "Registro(s) salvo(s) com sucesso! <br><br> ID(s) Abastecimento(s) integrado(s): " 
                            + lAbastIntegrados
                            + "<br> ID(s) Abastecimento(s) não integrado(s): " 
                            + lAbastNaoIntegrados
                            + retornoMensagemMovida
                    };
                }

                return new RespPadrao
                {
                    id = null,
                    sucesso = true,
                    mensagem = "Registro(s) salvo(s) com sucesso!"
                };

            }
            catch (Exception e)
            {
                return new RespPadrao()
                {
                    sucesso = false,
                    mensagem = e.Message
                };
            }
        }
    }
}
