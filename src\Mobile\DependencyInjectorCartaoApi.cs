﻿using Microsoft.AspNetCore.Authorization;
using Microsoft.AspNetCore.Http;
using Microsoft.Extensions.Configuration;
using Microsoft.Extensions.DependencyInjection;
using Microsoft.Extensions.DependencyInjection.Extensions;
using SistemaInfo.BBC.Infra.Bus;
using SistemaInfo.BBC.Infra.Data.Context;
using SistemaInfo.BBC.Mobile.Filters.SistemaInfo.Auth.Bus.Common.Infra.Security;
using SistemaInfo.Framework.CQRS.Message;

namespace SistemaInfo.BBC.Mobile
{
    /// <summary>
    ///
    /// </summary>
    public class DependencyInjectorCartaoApi
    {
        /// <summary>
        /// Registrar serviços necessários para funcionamento da aplicação
        /// </summary>
        /// <param name="services"></param>
        public static void RegisterServices(IServiceCollection services)
        {
            AddAppInfrastructure(services);
            AddAppServices(services);
        }

        /// <summary>
        /// Recuros ténicos para funcionamento da aplicação, framework's de baixo nível da aplicação
        /// </summary>
        /// <param name="services"></param>
        private static void AddAppInfrastructure(IServiceCollection services)
        {
            services.AddScoped<ConfigMigrator>();
            services.TryAddSingleton<IHttpContextAccessor, HttpContextAccessor>();
            services.AddScoped<IAuthorizationHandler, JwtAuthorizationHandler>();            
            services.AddSingleton<IPublishMessageBusConfigurations>(provider => null);

            // Login
//            services.AddScoped<IAuthorizationHandler, ApiTokenBusAuthorizationHandler<
//                Administradora, IAdministradoraReadRepository, IAdministradoraWriteRepository,
//                Empresa, IEmpresaReadRepository, IEmpresaWriteRepository,
//                Usuario, IUsuarioReadRepository, IUsuarioWriteRepository>>();

            // Publicação de mensagem ao barramento - Configurações de saída em direção ao barramento
//            services.AddSingleton<IPublishMessageBusConfigurations, ApiPublishConfigurations>();
            // Publicação de mensagem ao barramento - Configurações de saída em direção ao barramento
            services.AddSingleton<IPublishMessageBusConfigurations>(provider => null);

            // Publicação de mensagem ao barramento - Conexões a barramentos
//            services.AddSingleton<ICloudServiceMessageBroker>(
//                new CloudServiceMessageBroker(RabbitMqUtils.GetConnectionFactory("CloudService")));
            
            // Log no barramento AMQP
//            services.AddScoped<IAmqpBusLogging, AmqpBusLogging>(provider => new AmqpBusLogging(
//                provider.GetRequiredService<ILogIntegracaoAppService>(),
//                provider.GetRequiredService<IAppEngine>(),
//                CartaoMessages.MicroServiceName));
        }

        /// <summary>
        /// Serviços de consultas para obter informações
        /// </summary>
        /// <param name="services"></param>
        private static void AddAppServices(IServiceCollection services)
        {
//            services.AddScoped<ICartaoAppServiceProcessadora, CartaoAppServiceProcessadoraRemoteBus>();
        }
    }
}