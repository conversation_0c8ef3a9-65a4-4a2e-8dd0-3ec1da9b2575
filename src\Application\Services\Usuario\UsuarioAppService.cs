﻿using System;
using System.Collections.Generic;
using System.IO;
using System.Linq;
using System.Linq.Dynamic.Core;
using System.Threading.Tasks;
using AutoMapper.QueryableExtensions;
using Microsoft.EntityFrameworkCore;
using NLog;
using SistemaInfo.BBC.Application.Email.Usuario;
using SistemaInfo.BBC.Application.Interface.ClientSecret;
using SistemaInfo.BBC.Application.Interface.Menu;
using SistemaInfo.BBC.Application.Interface.Modulo;
using SistemaInfo.BBC.Application.Interface.Usuario;
using SistemaInfo.BBC.Application.Objects.Base;
using SistemaInfo.BBC.Application.Objects.Web.Empresa;
using SistemaInfo.BBC.Application.Objects.Web.EmpresaUsuario;
using SistemaInfo.BBC.Application.Objects.Web.Menu;
using SistemaInfo.BBC.Application.Objects.Web.Modulo;
using SistemaInfo.BBC.Application.Objects.Web.Portador;
using SistemaInfo.BBC.Application.Objects.Web.Usuario;
using SistemaInfo.BBC.Application.Objects.Web.Usuario.Request;
using SistemaInfo.BBC.Application.Objects.Web.UsuarioHistorico;
using SistemaInfo.BBC.Domain.Components.Email;
using SistemaInfo.BBC.Domain.Grid;
using SistemaInfo.BBC.Domain.Helper;
using SistemaInfo.BBC.Domain.Models.Empresa.Repository;
using SistemaInfo.BBC.Domain.Models.EmpresaUsuario;
using SistemaInfo.BBC.Domain.Models.EmpresaUsuario.Commands;
using SistemaInfo.BBC.Domain.Models.EmpresaUsuario.Repository;
using SistemaInfo.BBC.Domain.Models.GrupoUsuario.Repository;
using SistemaInfo.BBC.Domain.Models.Parametros.Repository;
using SistemaInfo.BBC.Domain.Models.Portador.Commands;
using SistemaInfo.BBC.Domain.Models.Portador.Repository;
using SistemaInfo.BBC.Domain.Models.Posto.Repository;
using SistemaInfo.BBC.Domain.Models.Usuario.Commands;
using SistemaInfo.BBC.Domain.Models.Usuario.Repository;
using SistemaInfo.BBC.Domain.Models.UsuarioHistorico.Repository;
using SistemaInfo.BBC.Repository.External.SistemaInfo.Auth.Web.Client;
using SistemaInfo.Framework.DomainDrivenDesign.Application.Service;
using SistemaInfo.Framework.DomainDrivenDesign.Domain.Engine;
using SistemaInfo.Framework.Utils;

namespace SistemaInfo.BBC.Application.Services.Usuario
{
    public class UsuarioAppService : AppService<Domain.Models.Usuario.Usuario, IUsuarioReadRepository, IUsuarioWriteRepository>,
        IUsuarioAppService
    {
       private readonly IModuloAppService _moduloAppService;
       private readonly IMenuAppService _menuAppService;
       private readonly INotificationEmailExecutor _notificationEmailExecutor;
       private readonly IEmpresaReadRepository _empresaReadRepository;
       private readonly IPortadorReadRepository _portadorReadRepository;
       private IParametrosReadRepository _parametrosReadRepository;
       private IPostoReadRepository _postoReadRepository;
       private IUsuarioHistoricoReadRepository _usuarioHistoricoReadRepository;
       private readonly IEmpresaUsuarioReadRepository _empresaUsuarioReadRepository;
       
        public UsuarioAppService(IAppEngine engine, 
            IUsuarioReadRepository readRepository,
            IUsuarioWriteRepository writeRepository,
            IModuloAppService moduloAppService,
            IMenuAppService menuAppService,
            IEmpresaReadRepository empresaReadRepository,
            IParametrosReadRepository parametrosReadRepository,
            IUsuarioHistoricoReadRepository usuarioHistoricoReadRepository,
            INotificationEmailExecutor notificationEmailExecutor, 
            IPortadorReadRepository portadorReadRepository, 
            IPostoReadRepository postoReadRepository, 
            IEmpresaUsuarioReadRepository empresaUsuarioReadRepository) : base(engine, readRepository, writeRepository)
        {
            _moduloAppService = moduloAppService;
            _menuAppService = menuAppService;
            _notificationEmailExecutor = notificationEmailExecutor;
            _portadorReadRepository = portadorReadRepository;
            _postoReadRepository = postoReadRepository;
            _empresaUsuarioReadRepository = empresaUsuarioReadRepository;
            _empresaReadRepository = empresaReadRepository;
            _parametrosReadRepository = parametrosReadRepository;
            _usuarioHistoricoReadRepository = usuarioHistoricoReadRepository;
        }

        public UsuarioMenuResponse GetMenusUsuario()
        {
           var idUsuario = Engine.User.Id;
           
           var lUsuario = Repository.Query
               .Include(a => a.GrupoUsuario )
               .Include(a=>a.GrupoUsuario.GrupoUsuarioMenu)
               .FirstOrDefault (a=>a.Id==idUsuario);

           if (lUsuario == null)
               return null;
           #region Verificar periodo de senha para atualização

           var tempoDuracaoSenha = _parametrosReadRepository.GetParametrosAsync(-1, 
                   Domain.Models.Parametros.Parametros.TipoDoParametro.PeriodoDuracaoSenha,
                   Domain.Models.Parametros.Parametros.TipoDoValor.Number).Result
               .Valor.ToIntSafe();

           var mensagemUsuario = "";
           var senhaExpirada = false;

           if (lUsuario.DataCriacaoSenha != null 
               && ((lUsuario.DataCriacaoSenha.Value.AddDays(tempoDuracaoSenha) < DateTime.Now) || lUsuario.SenhaProvisoria == 1)
               && lUsuario.Id != 1)
           {
               mensagemUsuario = lUsuario.SenhaProvisoria == 1 ? "A senha provisória deve ser alterada!" 
                   : "Período de utilização de senha expirado, por gentileza crie uma nova senha!";
               senhaExpirada = true;
           }

           #endregion

           var lMenus = Repository.Query.GetUsuarioMenu(lUsuario.GrupoUsuario.GrupoUsuarioMenu).OrderBy(a=>a.Descricao);
           var lMenuPaiIdList = lMenus.Select(a => a.MenuPaiId).ToList();
           var lMenuPai = _menuAppService.Repository.Query.Where(a => a.IsMenuPai == 1 && lMenuPaiIdList.Contains(a.Id)).Include(x => x.ModuloMenu).ToList();
           List<int> lModuloInt = new List<int>();
        
           foreach (var lMenu in lMenus)
           {
               lModuloInt.AddRange(lMenu.ModuloMenu.Select(lModMenu => lModMenu.ModuloId));
           }
        
           lModuloInt = lModuloInt.Distinct().ToList();
           
           var lModulo = _moduloAppService.Repository.Query.Include(x => x.ModuloMenu).Where(a=>lModuloInt.Contains(a.Id) && a.Sistema == 0).OrderBy(a=>a.Descricao).ToList();
         
           return new UsuarioMenuResponse
           {
               idUsuario = lUsuario.Id,
               ativo = lUsuario.Ativo,
               
               mensagem = mensagemUsuario,
               senhaExpirada = senhaExpirada,
               
               grupoUsuarioId = lUsuario.GrupoUsuarioId,
               login = lUsuario.Login,
               cpf = lUsuario.Cpf,
               empresaId = lUsuario.EmpresaId,
               nome = lUsuario.Nome,
               administrador = (lUsuario.EmpresaId ?? 0) == 0 && !lUsuario.GrupoEmpresaId.HasValue,
               foto = lUsuario.Foto,
               Modulo = lModulo.Where(x=> x.ModuloMenu.Any() && x.Sistema == 0).Select(modulo => new ModuloResponse
               {
                   IdMenu = modulo.Id,
                   Icone = modulo.Icone,
                   Descricao = modulo.Descricao,
                   Sequencia = modulo.Sequencia,
                   MenuEstruturaModel = lMenuPai
                       .Where(m => m.IsMenuPai == 1 &&
                                   m.ModuloMenu.Any(menuModulo => menuModulo.ModuloId == modulo.Id))
                       .Select(menuPai => new MenuEstruturaModelResponse
                       {
                           Menu = menuPai.Descricao,
                           IdMenu = menuPai.Id,
                           IdMenuPai = null,
                           Link = "",
                           LinkNovo = menuPai.Link,
                           SeqMenu = menuPai.Sequencia,
                           SeqModulo = modulo.Sequencia,
                           Menus = lMenus
                               .Where(m => m.IsMenuPai == 0 && m.MenuPaiId == menuPai.Id &&
                                           m.ModuloMenu.Any(menuModulo => menuModulo.ModuloId == modulo.Id))
                               .Select(menuFilho => new MenuFilhoResponse()
                               {
                                   Menu = menuFilho.Descricao,
                                   IdMenuPai = menuPai.Id,
                                   IdMenu = menuFilho.Id,
                                   Link = "",
                                   LinkNovo = menuFilho.Link,
                                   SeqMenu = menuFilho.Sequencia,
                                   SeqModulo = modulo.Sequencia
                               }).ToList()
                       }).ToList()
               }).ToList()
           };
        }
        
        public UsuarioMenuResponse GetMenusUsuarioPosto(int idUsuario)
        {
            var lUsuario = Repository.Query
               .Include(a => a.GrupoUsuario )
               .Include(a => a.Posto )
               .Include(a=>a.GrupoUsuario.GrupoUsuarioMenu)
               .FirstOrDefault (a=>a.Id==idUsuario);

           #region Verificar periodo de senha para atualização

           var tempoDuracaoSenha = _parametrosReadRepository.GetParametrosAsync(-1, 
                   Domain.Models.Parametros.Parametros.TipoDoParametro.PeriodoDuracaoSenha,
                   Domain.Models.Parametros.Parametros.TipoDoValor.Number).Result
               .Valor.ToIntSafe();

           var mensagemUsuario = "";
           var senhaExpirada = false;

           if (lUsuario == null)
               return null;
           
           if (lUsuario.DataCriacaoSenha != null 
               && ((lUsuario.DataCriacaoSenha.Value.AddDays(tempoDuracaoSenha) < DateTime.Now) || lUsuario.SenhaProvisoria == 1)
               && lUsuario.Id != 1)
           {
               mensagemUsuario = lUsuario.SenhaProvisoria == 1 ? "A senha provisória deve ser alterada!" 
                   : "Período de utilização de senha expirado, por gentileza crie uma nova senha!";
               senhaExpirada = true;
           }

           #endregion
           
        
           var lMenus = Repository.Query.GetUsuarioMenu(lUsuario.GrupoUsuario.GrupoUsuarioMenu).OrderBy(a=>a.Descricao);
           
           List<int> lModuloInt = new List<int>();

           var usuarioPosto = _postoReadRepository.FirstOrDefault(x => x.Id == lUsuario.PostoId);
        
           foreach (var lMenu in lMenus)
           {
               lModuloInt.AddRange(lMenu.ModuloMenu.Select(lModMenu => lModMenu.ModuloId));
           }
        
           lModuloInt = lModuloInt.Distinct().ToList();
           
           var lModulo = _moduloAppService.Repository.Query
               .Include(x => x.ModuloMenu)
               .Where(a=>lModuloInt.Contains(a.Id) && a.Sistema == 1).OrderBy(a=>a.Descricao).ToList();
         
           return new UsuarioMenuResponse
           {
               idUsuario = lUsuario.Id,
               ativo = lUsuario.Ativo,
               
               mensagem = mensagemUsuario,
               senhaExpirada = senhaExpirada,
               
               grupoUsuarioId = lUsuario.GrupoUsuarioId,
               login = lUsuario.Login,
               cpf = lUsuario.Cpf,
               idPosto = usuarioPosto.Id,
               empresaId = lUsuario.EmpresaId,
               nome = lUsuario.Nome,
               nomePosto = lUsuario.Posto.NomeFantasia,
               administrador = (lUsuario.EmpresaId ?? 0) == 0,
               foto = lUsuario.Foto,
               Modulo = lModulo.Where(x=> x.ModuloMenu.Any() && x.Sistema == 1).Select(modulo => new ModuloResponse
               {
                   IdMenu = modulo.Id,
                   Icone = modulo.Icone,
                   Descricao = modulo.Descricao,
                   Sequencia = modulo.Sequencia,
                   MenuEstruturaModel = lMenus
                       .Where(m => m.ModuloMenu.Any(menuModulo => menuModulo.ModuloId == modulo.Id))
                       .Select(menuPai => new MenuEstruturaModelResponse
                       {
                           Menu = menuPai.Descricao,
                           IdMenu = menuPai.Id,
                           IdMenuPai = null,
                           Link = "",
                           LinkNovo = menuPai.Link,
                           LinkImagemMenu = menuPai.LinkImagemMenu,
                           SeqMenu = menuPai.Sequencia,
                           SeqModulo = modulo.Sequencia
                       }).ToList()
               }).ToList()
           };
        }

        public UsuarioMenuResponse GetMenusUsuarioPostoAdministrador(int idPosto)
        {
            var lPosto = _postoReadRepository.FirstOrDefault(x => x.Id == idPosto);

           if (lPosto == null)
               return null;
        
           //Todos os menus
           var lMenus = Repository.Query.GetMenuPosto().OrderBy(a=>a.Descricao);
           var lMenuPai = _menuAppService.Repository.Query
               .Where(a => a.MenuPaiId == null)
               .Include(x => x.ModuloMenu).ToList();
           List<int> lModuloInt = new List<int>();

         
           foreach (var lMenu in lMenus)
           {
               lModuloInt.AddRange(lMenu.ModuloMenu.Select(lModMenu => lModMenu.ModuloId));
           }
        
           lModuloInt = lModuloInt.Distinct().ToList();
           
           var lModulo = _moduloAppService.Repository.Query
               .Include(x => x.ModuloMenu)
               .Where(a=>lModuloInt.Contains(a.Id) && a.Sistema == 1).OrderBy(a=>a.Descricao).ToList();
         
           return new UsuarioMenuResponse
           {
               ativo = lPosto.Ativo,
               login = lPosto.Cnpj,
               cpf = lPosto.Cnpj,
               idPosto = lPosto.Id,
               nome = lPosto.NomeFantasia ?? lPosto.RazaoSocial,
               nomePosto = lPosto.RazaoSocial ?? lPosto.NomeFantasia,
               administrador = true,
               Modulo = lModulo.Where(x=> x.ModuloMenu.Any()).Select(modulo => new ModuloResponse
               {
                   IdMenu = modulo.Id,
                   Icone = modulo.Icone,
                   Descricao = modulo.Descricao,
                   Sequencia = modulo.Sequencia,
                   MenuEstruturaModel = lMenus
                       .Where(m => m.ModuloMenu.Any(menuModulo => menuModulo.ModuloId == modulo.Id))
                       .Select(menuPai => new MenuEstruturaModelResponse
                       {
                           Menu = menuPai.Descricao,
                           IdMenu = menuPai.Id,
                           IdMenuPai = null,
                           Link = "",
                           LinkNovo = menuPai.Link,
                           LinkImagemMenu = menuPai.LinkImagemMenu,
                           SeqMenu = menuPai.Sequencia,
                           SeqModulo = modulo.Sequencia
                       }).ToList()
               }).ToList()
           };
        }

        public async Task<ConsultarUsuarioGridResponse> GetUsuarios(int take, int page, OrderFilters orderFilters,
            List<QueryFilters> filters)
        {
            var lUsuario = await GetDataToGridAndReport(orderFilters, filters);

            lUsuario = lUsuario.Where(x => x.PostoId == null);
            
            var a = lUsuario.Count();

            var retorno = lUsuario.Skip((page - 1) * take)
                .Take(take)
                .ProjectTo<ConsultaUsuarioGrid>(Engine.Mapper.ConfigurationProvider).ToList();

            return new ConsultarUsuarioGridResponse
            {
                items = retorno,
                totalItems = a
            };
        }

        public ConsultarUsuarioGridResponse ConsultarGridUsuarioPosto (int take, int page, OrderFilters orderFilters,
            List<QueryFilters> filters, int statusUsuario)
        {
            var lUsuario = Repository.Query.Where(x => x.PostoId == Engine.User.AdministradoraId);

            lUsuario = statusUsuario switch
            {
                0 => lUsuario.Where(x => x.Ativo == 0 && x.UsuarioCancelado == 0),
                1 => lUsuario.Where(x => x.Ativo == 1),
                2 => lUsuario.Where(x => x.UsuarioCancelado == 1),
                _ => lUsuario
            };

            var total = lUsuario.Count();
            
            lUsuario = lUsuario.AplicarFiltrosDinamicos(filters);
            lUsuario = string.IsNullOrWhiteSpace(orderFilters?.Campo)
                ? lUsuario.OrderByDescending(o => o.Id)
                : lUsuario.OrderBy($"{orderFilters.Campo} {orderFilters.Operador.DescriptionAttr()}");

            var retorno = lUsuario
                .Skip((page - 1) * take)
                .Take(take)
                .ProjectTo<ConsultaUsuarioGrid>()
                .ToList();

            return new ConsultarUsuarioGridResponse()
            {
                items = retorno,
                totalItems = total
            };
        }

        public async Task<RespPadrao> GetUsuarioById(int usuarioId)
        {
            var lLog = LogManager.GetCurrentClassLogger();
            try
            {
                if (usuarioId <= 0) 
                    return new RespPadrao(false, "Não foi possível efetuar a consulta.");
                
                var lUsuario = await Repository.Query
                    .Where(x => x.Id == usuarioId)
                    .ProjectTo<ConsultarUsuarioResponse>()
                    .FirstOrDefaultAsync();
                
                if (lUsuario == null) 
                    return new RespPadrao(false, "Usuário não encontrado.");
                
                var empresasVinculadas = await GetEmpresasVinculadasUsuario(usuarioId);

                if (lUsuario.EmpresaId.HasValue)
                {
                    var empresaUsuario = await GetEmpresaUsuarioPorId(lUsuario.EmpresaId.Value);
                    if (empresaUsuario != null)
                        empresasVinculadas.Insert(0, empresaUsuario);
                }
                
                // Select para remover empresas duplicadas (empresasVinculadas e empresaUsuario)
                lUsuario.EmpresasVinculadas = empresasVinculadas
                    .GroupBy(e => e.Id)
                    .Select(g => g.First())
                    .ToList();
                
                return new RespPadrao(true, "Sucesso ao consultar o usuário!", lUsuario);
                
            } 
            catch (Exception e) 
            {
                lLog.Error(e);
                return new RespPadrao(false, e.Message);
            }
        }
        
        private async Task<List<ConsultarEmpresaCombo>> GetEmpresasVinculadasUsuario(int usuarioId)
        {
            return await _empresaUsuarioReadRepository
                .Where(x => x.UsuarioId == usuarioId)
                .Select(y => new ConsultarEmpresaCombo
                {
                    Id = y.Empresa.Id,
                    Cnpj = y.Empresa.Cnpj.ToCNPJFormato(),
                    NomeFantasia = y.Empresa.NomeFantasia,
                    RazaoSocial = y.Empresa.RazaoSocial
                }).ToListAsync();
        }

        private async Task<ConsultarEmpresaCombo> GetEmpresaUsuarioPorId(int empresaId)
        {
            return await _empresaReadRepository
                .Where(e => e.Id == empresaId)
                .Select(e => new ConsultarEmpresaCombo
                {
                    Id = e.Id,
                    Cnpj = e.Cnpj.ToCNPJFormato(),
                    NomeFantasia = e.NomeFantasia,
                    RazaoSocial = e.RazaoSocial
                }).FirstOrDefaultAsync();
        }
        
        public ConsultarUsuarioResponse GetUsuarioByIdPosto(int usuarioId)
        {
            
            var idPostoLogado = _postoReadRepository.FirstOrDefault(p => p.Id == User.AdministradoraId).Id;
            
            var lUsuario = Repository.Query
                .Where(x => x.Id == usuarioId)
                .Where(p => p.PostoId == idPostoLogado)
                .ProjectTo<ConsultarUsuarioResponse>()
                .FirstOrDefault();

            return lUsuario;
        }

        public async Task<RespPadrao> Save(SaveUsuarioResponse lModel)
        {
            try
            {
                
                if(lModel.EmpresaId != null && lModel.GrupoEmpresaId != null)
                    return new RespPadrao(false, "Não foi possível salvar o usuário.");
                
                #region Verifica se usuário logado tem permissão para editar determinado registro vendo o parâmetro de empresa

                if (User.EmpresaId > 0 && User.EmpresaId != lModel.EmpresaId)
                    return new RespPadrao(false, "Usuário não tem permissão para realizar esta ação.");

                #endregion
                
                if (lModel.Id == "Auto")
                {
                    lModel.Id = null;

                    // if ((lUsuarioLogado.EmpresaId > 0 || lUsuarioLogado.GrupoEmpresaId > 0) && lModel.EmpresasVinculadas.Any())
                    //     return new RespPadrao(false, "Usuário não tem permissão para realizar esta ação.");

                    var lUsuarioInsert = Mapper.Map<UsuarioAdicionarComRetornoCommand>(lModel);
                    lUsuarioInsert.Senha = GerarSenhaAleatoria();
                    lUsuarioInsert.SenhaProvisoria = 1;
                    lUsuarioInsert.DataCriacaoSenha = DateTime.Now;
                    lUsuarioInsert.Login = lModel.Login;
                    lUsuarioInsert.PostoId = null;
                    
                    var retornoEmail = await EmailUsuarioPrimeiroAcesso.EnviarEmail(_notificationEmailExecutor,
                        lUsuarioInsert.Nome, lUsuarioInsert.Senha, lUsuarioInsert.Email, lUsuarioInsert.Login);

                    if (!retornoEmail.sucesso)
                        return new RespPadrao(false, "Não foi possível enviar o e-mail de cadastro do usuário. Entre em contato com a administração.");
                    
                    var usuario = await Engine.CommandBus.SendCommandAsync<Domain.Models.Usuario.Usuario>(lUsuarioInsert);
                    
                    await ValidaSalvarEmpresaUsuario(lModel, usuario.Id);
                    return new RespPadrao(true, "Usuário cadastrado e e-mail enviado com sucesso.");

                }
                
                var lUsuarioUpdate = Mapper.Map<UsuarioEditarCommand>(lModel);

                // if ((lUsuarioLogado.EmpresaId > 0) && !Equals(empresasVinculadas.Select(a => a.EmpresaId), lModel.EmpresasVinculadas.Select(x => x.Id).ToList()))
                //     return new RespPadrao(false, "Usuário não tem permissão para realizar esta ação.");
                    
                lUsuarioUpdate.SenhaProvisoria = 0;

                await Engine.CommandBus.SendCommandAsync(lUsuarioUpdate);
                
                await ValidaSalvarEmpresaUsuario(lModel, lUsuarioUpdate.Id);

                return new RespPadrao(true, "Usuário salvo com sucesso.");
            }
            catch (DirectoryNotFoundException e)
            {
                LogManager.GetCurrentClassLogger().Error(e);
                return new RespPadrao(false, "Não foi possível montar o corpo de e-mail do cadastro do usuário. Entre em contato com a administração.");
            }
            catch (Exception e)
            {
                LogManager.GetCurrentClassLogger().Error(e);
                return new RespPadrao(false, "Não foi possível salvar o usuário.");
            }
        }
        
        private async Task ValidaSalvarEmpresaUsuario(SaveUsuarioResponse lModel, int? idUsuario)
        {
            
            var empresasUsuario = await ConsultarEmpresasVinculadasUsuario(idUsuario);
            
            var novasEmpresasIds = lModel.EmpresasVinculadas.Select(e => e.Id).ToList();

            #region Adiciona novas empresas

            var empresasParaAdicionar = lModel.EmpresasVinculadas
                .Where(empresa => empresasUsuario.All(eu => eu.EmpresaId != empresa.Id))
                .ToList();

            foreach (var empresa in empresasParaAdicionar)
            {
                await Engine.CommandBus.SendCommandAsync(new EmpresaUsuarioSalvarCommand
                {
                    UsuarioId = idUsuario ?? lModel.Id.ToIntSafe(),
                    EmpresaId = empresa.Id
                });
            }

            #endregion

            #region Excluir empresas que foram removidas

            var empresasParaExcluir = empresasUsuario
                .Where(empresaUsuario => !novasEmpresasIds.Contains(empresaUsuario.EmpresaId))
                .ToList();

            foreach (var empresaUsuario in empresasParaExcluir)
            {
                await Engine.CommandBus.SendCommandAsync(new EmpresaUsuarioExcluirCommand
                {
                    UsuarioId = empresaUsuario.UsuarioId,
                    EmpresaId = empresaUsuario.EmpresaId
                });
            }

            #endregion
            
        }

        private async Task<List<EmpresaUsuario>> ConsultarEmpresasVinculadasUsuario(int? idUsuario)
        {
            var empresasUsuario = await _empresaUsuarioReadRepository
                .Where(eu => eu.UsuarioId == idUsuario)
                .ToListAsync();
            return empresasUsuario;
        }

        public bool ValidaUsuarioPertencePosto(string idPostoEditado)
        {
            try
            {
                if (idPostoEditado is null) return false;

                if (idPostoEditado == "Auto") return true;
            
                var idPostoLogado = _postoReadRepository.FirstOrDefault(p => p.Id == User.AdministradoraId).Id;

                var lUsuarioEditadoPostoId = Repository.Query.GetUsuarioFull(idPostoEditado.ToInt()).PostoId;

                return lUsuarioEditadoPostoId == idPostoLogado;
            }
            catch (Exception e)
            {
                Console.WriteLine(e.Message);
                return false;
            }
            
        }

        public async Task<ConsultarEmpresaComboResponse> ConsultaGridEmpresasVinculadasCombo(int requestTake, int requestPage, OrderFilters requestOrder,
            List<QueryFilters> requestFilters, List<int> empresaIds, int? idUsuario)
        {
            var lLog = LogManager.GetCurrentClassLogger();
            try
            {
                
                var empresasSemVinculo = _empresaReadRepository.GetAll();
                
                var empresas = await Repository.Query.GetEmpresasAcessoUsuario(Engine.User.Id);
                if (!empresas.IsEmpty())
                    empresasSemVinculo = empresasSemVinculo.Where(x => empresas.Contains(x.Id));
                
                if (empresaIds != null && empresaIds.Any())
                {
                    empresasSemVinculo = empresasSemVinculo.Where(e => !empresaIds.Contains(e.Id));
                }
                
                empresasSemVinculo = empresasSemVinculo.AplicarFiltrosDinamicos(requestFilters);
                
                var lCount = await empresasSemVinculo.CountAsync();
                    
                empresasSemVinculo = string.IsNullOrWhiteSpace(requestOrder?.Campo)
                    ? empresasSemVinculo.OrderByDescending(o => o.Id)
                    : empresasSemVinculo.OrderBy($"{requestOrder?.Campo} {requestOrder?.Operador.DescriptionAttr()}");

                var lRetorno = await empresasSemVinculo.Skip((requestPage - 1) * requestTake)
                    .Take(requestTake).ProjectTo<ConsultarEmpresaCombo>(Engine.Mapper.ConfigurationProvider).ToListAsync();

                return new ConsultarEmpresaComboResponse
                {
                    items = lRetorno,
                    totalItems = lCount
                };
            }
            catch (Exception e)
            {
                lLog.Error(e.Message);
                return new ConsultarEmpresaComboResponse();
            }
        }

        public async Task<RespPadrao> SalvarUsuarioPosto(SaveUsuarioPostoResponse request)
        {
            try
            {
                var idPostoLogado = _postoReadRepository.FirstOrDefault(x => x.Id == User.AdministradoraId).Id;


                #region Valida se é permitido salvar o usuário

                if (!ValidaUsuarioPertencePosto(request.Id))
                {
                    return new RespPadrao(false, "Não foi possível salvar! O usuário não pertence a esse posto.");
                }
                
                #endregion
                
                if (request.Id == "Auto")
                {
                    request.Id = null;

                    var lUsuarioInsert = Mapper.Map<UsuarioAdicionarCommand>(request);
                    lUsuarioInsert.Senha = GerarSenhaAleatoria();
                    lUsuarioInsert.SenhaProvisoria = 1;
                    lUsuarioInsert.DataCriacaoSenha = DateTime.Now;
                    lUsuarioInsert.Login = request.login;
                    lUsuarioInsert.PostoId = idPostoLogado;

                    var retonroEmail = await EmailUsuarioRecuperarSenha.EnviarEmail(_notificationEmailExecutor,
                        lUsuarioInsert.Nome, lUsuarioInsert.Senha, lUsuarioInsert.Email, lUsuarioInsert.Login);

                    if (retonroEmail.sucesso)
                    {
                        await Engine.CommandBus.SendCommandAsync(lUsuarioInsert);
                        return new RespPadrao(true, null);
                    }

                    return new RespPadrao(false, "Não foi possível enviar o e-mail de cadastro do usuário. Entre em contato com a administração.");
                }

                #region Valida edição

                if (User.AdministradoraId != 0 && User.Id != 0)
                {
                    return new RespPadrao(false, "Não foi possível salvar o usuário! Usuário sem permissão para realizar esta ação.");
                }

                #endregion
                
                var lUsuarioUpdate = Mapper.Map<UsuarioEditarCommand>(request);
                lUsuarioUpdate.SenhaProvisoria = 0;
                lUsuarioUpdate.PostoId = idPostoLogado;

                await Engine.CommandBus.SendCommandAsync(lUsuarioUpdate);

                return new RespPadrao(true, null);
            }
            catch (DirectoryNotFoundException e)
            {
                LogManager.GetCurrentClassLogger().Error(e);
                return new RespPadrao(false, "Não foi possível montar o corpo de e-mail do cadastro do usuário. Entre em contato com a administração.");
            }
            catch (Exception e)
            {
                LogManager.GetCurrentClassLogger().Error(e);
                return new RespPadrao(false, "Não foi possível salvar o usuário.");
            }
        }

        public string Validar(SaveUsuarioResponse lModel)
        {
            if (lModel.Id != "Auto") return !StringUtils.ValidaCPF(lModel.Cpf) ? "CPF inválido!" : "";
            
            var lUsuario = Repository.Query.FirstOrDefault(u => u.Cpf == lModel.Cpf || u.Login == lModel.Login);

            if (lUsuario != null) return lUsuario.Cpf == lModel.Cpf ? "CPF informado já cadastrado!" : "Login informado já cadastrado!";

            return !StringUtils.ValidaCPF(lModel.Cpf) ? "CPF inválido!" : "";
        }
        

        public string ValidarUsuarioPosto(SaveUsuarioPostoResponse request)
        {
            
            if (request.Id != "Auto")
            {
                request.Id.ToIntSafe();
            }

            var lUsuario = Repository.Query.FirstOrDefault(x => x.Cpf == request.cpf
                                                                && x.UsuarioCancelado != 1);

            if (lUsuario != null)
            {
                if (lUsuario.Cpf == request.cpf)
                {
                    return "CPF informado já cadastrado!";
                }

                return "Login informado já cadastrado!";
            }

            if (!StringUtils.ValidaCPF(request.cpf))
            {
                return "CPF inválido!";
            }

            return "";
        }

        public string ValidarSenha(string senha)
        {
            if (senha.IsNullOrWhiteSpace())
            {
                return "Senha inválida!";
            }

            if (senha.Length < 8)
            {
                return "A senha informada deve ter no mínimo 8 caracteres!";
            }

            var lTextosParaBloquear = new string[]
                {"JSL", "SCHIO", "TGABC", "ABC", "DEF", "GHI", "123", "456", "789", "BBC"};

            foreach (var lTexto in lTextosParaBloquear)
            {
                if (senha.ToUpper().IndexOf(lTexto) > -1)
                {
                    return
                        "A senha informada não atende os requisitos de segurança, forneça uma nova senha que não possua sequências e/ou nome da empresa!";
                }
            }

            var lLetrasMinusculas = "abcdefghijklmnopqrstuvwxyz";
            var lLetrasMaiusculas = "ABCDEFGHIJKLMNOPQRSTUVWXYZ";
            var lNumeros = "0123456789";
            var lEspeciais = "!@#$%¨&*()|\\?/}{[]§ªº.,:;";
            var lTemLetraMinuscula = false;
            var lTemLetraMaiuscula = false;
            var lTemNumero = false;
            var lTemEspecial = false;

            for (int i = 0; i < senha.Length; i++)
            {
                var lCaracterer = senha.Substring(i, 1);

                if (lLetrasMinusculas.IndexOf(lCaracterer) > -1) lTemLetraMinuscula = true;
                if (lLetrasMaiusculas.IndexOf(lCaracterer) > -1) lTemLetraMaiuscula= true;
                if (lNumeros.IndexOf(lCaracterer) > -1) lTemNumero = true;
                if (lEspeciais.IndexOf(lCaracterer) > -1) lTemEspecial = true;
            }

            if (!lTemLetraMaiuscula || !lTemLetraMinuscula || !lTemNumero || !lTemEspecial)
            {
                return "É necessário ao menos uma letra maiúscula e minúscula, um número e um caractere especial na senha!";
            }

            return null;
        }

        public string GetFoto(int usuarioId)
        {
            var lUsuario = Repository.Query.GetById(usuarioId);
            return lUsuario?.Foto;
        }

        public void AlterarStatus(int Idusuario)
        {
            var lUsuario = Repository.Query.GetById(Idusuario);

            if (lUsuario != null)
            {
                if (lUsuario.Ativo == 1)
                {
                    lUsuario.Ativo = 0;
                    lUsuario.DataBloqueio = DateTime.Now;
                    lUsuario.UsuarioBloqueioId = Engine.User.Id;
                }
                else
                {
                    lUsuario.Ativo = 1;
                    lUsuario.DataDesbloqueio = DateTime.Now;
                    lUsuario.UsuarioDesbloqueioId = Engine.User.Id;
                }

                Repository.Command.SaveChanges();
            }
        }
        
        public void CancelarUsuario(int Idusuario)
        {
            var lUsuario = Repository.Query.GetById(Idusuario);

            if (lUsuario != null)
            {
                lUsuario.UsuarioCancelado = 1;
                lUsuario.Ativo = 0;
                lUsuario.DataBloqueio = DateTime.Now;

                Repository.Command.SaveChanges();
            }
        }

        public ConsultarUsuarioResponse ConsultarUsuarioPorCpfEEmpresa(string cpf, int empresaId)
        {
            var usuario = Repository.Query.Where(o => o.Cpf.Equals(cpf) && o.EmpresaId.Equals(empresaId))
                .ProjectTo<ConsultarUsuarioResponse>().FirstOrDefault();

            return usuario;
        }

        public async Task<UsuarioAutenticacaoViewModel> ValidarUsuario(string aUsuario, string aSenha)
        {
            if (string.IsNullOrWhiteSpace(aSenha))
                throw new InvalidOperationException("Senha não informada.");

            var hashSenha = aSenha.GetHashSha1();

            var mensagem =
                "Usuário/senha inválidos. Caso não se lembre de sua senha, " +
                "solicite uma nova clicando na opção esqueci minha senha ou contate " +
                "a central de atendimento através do 0800-771-4080";

            const int maximoTentativas = 4;

            var usuario = await Repository.Query
                .Include(x => x.Empresa)
                .FirstOrDefaultAsync(u => u.Login == aUsuario && u.PostoId == null);
            
            if (usuario == null)
            {
                return new UsuarioAutenticacaoViewModel
                {
                    Usuario = null,
                    Mensagem = mensagem,
                    SenhaErrada = true,
                    Token = null
                };
            }
            
            if (usuario.Empresa?.Ativo == 0)
            {
                return new UsuarioAutenticacaoViewModel
                {
                    Usuario = Mapper.Map<UsuarioLogadoViewModel>(usuario),
                    Mensagem = mensagem,
                    SenhaErrada = true,
                    Token = null
                };
            }

            if (usuario.QtdeErroSenha >= maximoTentativas)
            {
                return new UsuarioAutenticacaoViewModel
                {
                    Usuario = Mapper.Map<UsuarioLogadoViewModel>(usuario),
                    Mensagem = mensagem,
                    SenhaErrada = true,
                    Token = null
                };
            }

            if (!usuario.Senha.Equals(hashSenha))
            {
                usuario.QtdeErroSenha += 1;
                await Repository.Command.SaveChangesAsync();
                return new UsuarioAutenticacaoViewModel
                {
                    Usuario = Mapper.Map<UsuarioLogadoViewModel>(usuario),
                    Mensagem = mensagem,
                    SenhaErrada = true,
                    Token = null
                };
            }
            
            usuario.QtdeErroSenha = 0;
            await Repository.Command.SaveChangesAsync();
            
            var user = Mapper.Map<UsuarioAutenticacaoViewModel>(usuario);
            user.SenhaErrada = false;
            return user;
        }

        public async Task<RespPadrao> ValidarStatusSenha(int usuarioId)
        {
            var usuario = Repository.Query.GetUsuarioFull(usuarioId);

            if (usuario == null)
            {
                return new RespPadrao
                {
                    sucesso = false,
                    mensagem = "Usuário não localizado."
                };
            }

            if (usuario.DataCriacaoSenha == null)
            {
                var usuarioEdit = Mapper.Map<UsuarioEditarCommand>(usuario);
                usuarioEdit.DataCriacaoSenha = DateTime.Now;
                await Engine.CommandBus.SendCommandAsync(usuarioEdit);
            }

            var tempoSenhaProvisoria = (await _parametrosReadRepository.GetParametrosAsync(-1,
                    Domain.Models.Parametros.Parametros.TipoDoParametro.PeriodoMaximoInatividadeSenhaProvisoria,
                    Domain.Models.Parametros.Parametros.TipoDoValor.Number))?
                .Valor.ToIntSafe();

            if (usuario.DataCriacaoSenha != null
                && usuario.DataCriacaoSenha.Value.AddMinutes(tempoSenhaProvisoria ?? 0) < DateTime.Now
                && usuario.SenhaProvisoria == 1
                && usuario.Id != 1)
            {
                return new RespPadrao
                {
                    sucesso = false,
                    mensagem = "Período máximo de utilização da senha provisoria alcançado!"
                };
            }
            
            return new RespPadrao
            {
                sucesso = true
            };
        }
        
          public async Task<RespPadrao> AlterarSenhaUsuario(AlterarSenhaUsuarioRequest request)
        {

            var usuario = await Repository.Query.GetByIdAsync(User.Id);

            var lUsuarioSaveCommand = Mapper.Map<UsuarioEditarCommand>(usuario);

            Repository.Query.Detach(usuario);
            
            if (request.SenhaAntiga.GetHashSha1() != lUsuarioSaveCommand?.Senha)
            {
                return new RespPadrao()
                {
                    sucesso = false,
                    mensagem = "Senha atual inválida!"
                };
            }

            #region Validar argumentos
            
            if (string.IsNullOrEmpty(request.NovaSenha))
            {
                return new RespPadrao()
                {
                    sucesso = false,
                    mensagem = "Informe a nova senha!"
                };
            }

            if (string.IsNullOrEmpty(request.ConfirmacaoSenha))
            {
                return new RespPadrao()
                {
                    sucesso = false,
                    mensagem = "Necessário confirmar a nova senha"
                };
            }

            if (request.NovaSenha != request.ConfirmacaoSenha)
            {
                return new RespPadrao()
                {
                    sucesso = false,
                    mensagem = "Confirmação da senha não confere com a nova senha."
                };
            }

            var validaSenha = ValidarSenha(request.NovaSenha);
            if (!string.IsNullOrEmpty(validaSenha))
            {
                return new RespPadrao()
                {
                    sucesso = false,
                    mensagem = validaSenha
                };
            }

            #endregion

            lUsuarioSaveCommand.Senha = request.NovaSenha;

            try
            {
                await Engine.CommandBus.SendCommandAsync(lUsuarioSaveCommand);

            }
            catch (Exception e)
            {
                LogManager.GetCurrentClassLogger().Error(e);
                throw;
            }


            return new RespPadrao()
            {
                sucesso = true,
                mensagem = "Senha alterada com sucesso"
            };
        }

        public async Task<RespPadrao> AlterarSenha(PortadorAlterarSenhaRequest request, string cpfCnpj)
        {
            try
            {
                var lPortador = await _portadorReadRepository.GetByCpfCnpjAsync(cpfCnpj);
                
                if (lPortador == null)
                {
                    return new RespPadrao
                    {
                        sucesso = false,
                        mensagem = "Não foi possível recuperar o cadastro do usuário logado."
                    };
                }
                
                var lSenhaAtual = lPortador.SenhaApi;
                string lSenhaNova = request.SenhaNova;
                var lSenhaAntiga = request.SenhaAntiga;

                if (lSenhaAntiga.GetHashSha1() != lSenhaAtual)
                {
                    return new RespPadrao
                    {
                        sucesso = false,
                        mensagem = "Senha antiga inválida."
                    };
                }

                if (lSenhaNova.IsNullOrWhiteSpace())
                {
                    return new RespPadrao
                    {
                        sucesso = false,
                        mensagem = "Senha não pode ser vazia!"
                    };
                }
                
                if (lSenhaNova.Length > 20)
                {
                    return new RespPadrao
                    {
                        sucesso = false,
                        mensagem = "Senha não pode conter mais de 20 caracteres!"
                    };
                }

                var lPortadorAlterarSenhaCommand = new PortadorAlterarSenhaCommand
                {
                    CpfCnpj = cpfCnpj,
                    SenhaApi = lSenhaNova
                };

                await Engine.CommandBus.SendCommandAsync(lPortadorAlterarSenhaCommand);

                return new RespPadrao
                {
                    sucesso = true,
                    mensagem = "Senha alterada com sucesso!"
                };

            }
            catch (Exception e)
            {
                return new RespPadrao
                {
                    sucesso = false,
                    mensagem = e.Message
                };
            }
        }
        
        private async Task<IQueryable<Domain.Models.Usuario.Usuario>> GetDataToGridAndReport(OrderFilters orderFilters,
            List<QueryFilters> filters)
        {
            var aUsuarios = Repository.Query.Include(x => x.GrupoUsuario);

            var empresas = await Repository.Query.GetEmpresasAcessoUsuario(Repository.Query.GetById(Engine.User.Id));
            
            if (empresas != null)
                aUsuarios = aUsuarios.Where(o => empresas.Contains(o.EmpresaId ?? 0));

            aUsuarios = aUsuarios.AplicarFiltrosDinamicos(filters);

            aUsuarios = string.IsNullOrWhiteSpace(orderFilters?.Campo) ?
                aUsuarios.OrderByDescending(o => o.Id) : 
                aUsuarios.OrderBy($"{orderFilters.Campo} {orderFilters.Operador.DescriptionAttr()}");

            return aUsuarios;
        }

        public async Task<RespPadrao> CadastrarPrimeiroUsuarioEmpresa(UsuarioValidacaoEmpresaRequest usuarioRequest)
        {
            try
            {
                var usuarioCadastrado = Repository.Query.FirstOrDefault(o => o.Cpf == usuarioRequest.Cpf);
                var loginCadastrado = Repository.Query.FirstOrDefault(o => o.Login == usuarioRequest.Login);
    
                if (usuarioCadastrado != null)
                {
                    if (usuarioCadastrado.GrupoUsuarioId == usuarioRequest.GrupoUsuarioId)
                        return new RespPadrao
                        {
                            sucesso = false,
                            mensagem = "Usuário já cadastrado."
                        };
                    
                    throw new Exception($"CPF já cadastrado para o login {usuarioCadastrado.Login}.");
                }
                
                if (loginCadastrado != null)
                    throw new Exception("Login já cadastrado.");
                
                var usuario = Mapper.Map<UsuarioAdicionarCommand>(usuarioRequest);
                usuario.ValidarCriacao();
                
                await Engine.CommandBus.SendCommandAsync(usuario);
                
                return new RespPadrao
                {
                    sucesso = true,
                    mensagem = ""
                };
            }
            catch (Exception e)
            {
                return new RespPadrao
                {
                    sucesso = false,
                    mensagem = e.Message
                };
            }
        }
        
        public EmpresaResponse EmpresaUsuarioLogado(int usuarioId)
        {
            var usuario = Repository.Query.Include(o => o.Empresa).FirstOrDefault(o => o.Id == usuarioId);
            
            if (usuario == null)
                throw new Exception();
            
            if (usuario.Empresa == null)
                throw new Exception();

            return new EmpresaResponse {Id = usuario.Empresa.Id, Nome = usuario.Empresa.RazaoSocial};
        }

        public async Task<RespPadrao> RecuperarSenha(RecuperarSenhaUsuarioRequest request)
        {
            try
            {
                var lUsuario = await Repository.Query.GetByCpfAndEmail(request.Cpf, request.Email);
                
                if (lUsuario == null)
                {
                    return new RespPadrao
                    {
                        sucesso = false,
                        mensagem = "Não foi possível recuperar a senha do usuário."
                    };
                }

                var lSenhaAntiga = lUsuario.Senha;

                var lSenhaNovaAleatoria = GerarSenhaAleatoria();

                var lAlterarSenhaCommand = new UsuarioAlterarSenhaCommand
                {
                    Id = lUsuario.Id,
                    Senha = lSenhaNovaAleatoria,
                    DataCriacaoSenha = DateTime.Now,
                    SenhaProvisoria = 1,
                    QtdeErroSenha = 0,
                    HasharSenha = true
                };

                await Engine.CommandBus.SendCommandAsync(lAlterarSenhaCommand);

                var lRetornoEmail = await EmailUsuarioRecuperarSenha
                    .EnviarEmail(_notificationEmailExecutor, lUsuario.Nome, lSenhaNovaAleatoria, lUsuario.Email, null);

                //Caso dê tudo certo retorna sucesso
                if (lRetornoEmail.sucesso)
                {
                    return new RespPadrao
                    {
                        sucesso = true,
                        mensagem = ""
                    };
                }

                //Caso não dê pra enviar o email, volta a senha antiga do usuário
                var lRetornarSenhaCommand = new UsuarioAlterarSenhaCommand
                {
                    Id = lUsuario.Id,
                    Senha = lSenhaAntiga,
                    DataCriacaoSenha = DateTime.Now,
                    SenhaProvisoria = 0,
                    HasharSenha = false
                };

                await Engine.CommandBus.SendCommandAsync(lRetornarSenhaCommand);

                return new RespPadrao
                {
                    sucesso = false,
                    mensagem = lRetornoEmail.mensagem
                };
            }
            catch (Exception e)
            {
                LogManager.GetCurrentClassLogger().Error(e);
                return new RespPadrao
                {
                    sucesso = false,
                    mensagem = "Não foi possível recuperar a senha do usuário."
                };
            }
        }

        private static string GerarSenhaAleatoria()
        {
            const string charsMinus = "abcdefghijklmnoz";
            const string charsMajors = "ABCDEFGHIJKLMNOZ";
            const string charsNumbers = "1234567890";
            const string charsSpecials = "!@#$%&*()_-=+/|";
            
            var random = new Random();
            
            var result = new string(Enumerable.Repeat(charsMinus, 3).Select(s => s[random.Next(s.Length)]).ToArray());
            result += new string(Enumerable.Repeat(charsMajors, 1).Select(s => s[random.Next(s.Length)]).ToArray());
            result += new string(Enumerable.Repeat(charsNumbers, 2).Select(s => s[random.Next(s.Length)]).ToArray());
            result += new string(Enumerable.Repeat(charsSpecials, 2).Select(s => s[random.Next(s.Length)]).ToArray());
            
            result = new string(result.OrderBy(x => Guid.NewGuid()).ToArray());
            
            return result;
        }

        public async Task<RespPadrao> AtualizarSenha(AtualizarSenhaRequest request)
        {
            try
            {
                var lUsuario = await Repository.Query
                    .Where(x => x.Id == User.Id)
                    .Include(x => x.UsuarioCentroCusto)
                    .Include(x => x.UsuarioFilial)
                    .ProjectTo<SaveUsuarioResponse>()
                    .FirstOrDefaultAsync();

                lUsuario.Senha = request.NovaSenha;

                var lUsuarioUpdate = Mapper.Map<UsuarioEditarCommand>(lUsuario);

                lUsuarioUpdate.SenhaProvisoria = 0;

                await Engine.CommandBus.SendCommandAsync(lUsuarioUpdate);

                return new RespPadrao(true, "Senha alterada com sucesso!");
            }
            catch (Exception e)
            {
                LogManager.GetCurrentClassLogger().Error(e);
                return new RespPadrao(false, "Não foi possível alterar a senha do usuário. Entre em contato com a administração.");
            }
        }

        public List<UsuarioHistoricoResponse> GetUsuarioHistorico(int usuarioId)
        {

            #region Valida se usuário logado tem permissão para acessar histórico do usuário passado por parâmetro

            if (User.Sistema != "Posto")
            {
                var empresasPermitidas = Repository.Query.GetEmpresasAcessoUsuario(Repository.Query.GetById(Engine.User.Id)).Result;
                if (empresasPermitidas != null)
                {
                    var usuarioConsultado = Repository.Query.GetById(usuarioId);
                    if (!empresasPermitidas.Contains(usuarioConsultado.EmpresaId ?? 0)) return null; 
                }
            }
            
            #endregion
            
            var consultaHistorico = _usuarioHistoricoReadRepository
                .Where(x => x.UsuarioId == usuarioId)
                .OrderByDescending(x => x.DataAlteracao)
                .ProjectTo<UsuarioHistoricoResponse>(Engine.Mapper.ConfigurationProvider)
                .ToList();

            foreach (var farmatarCampo in consultaHistorico
                         .Where(farmatarCampo => farmatarCampo.Campo is "Telefone" or "Celular"))
            {
                farmatarCampo.Valor = farmatarCampo.Valor.ToTelefoneFormato();
            }
            
            return consultaHistorico;
            
        }
        
    }
}