﻿using Microsoft.EntityFrameworkCore.Metadata;
using Microsoft.EntityFrameworkCore.Migrations;
using System;
using System.Collections.Generic;

namespace SistemaInfo.BBC.Infra.Data.Migrations
{
    public partial class SPRINT_55_ALTERANDO_NOT_NULL_EMPRESA_AUTHSESSION : Migration
    {
        protected override void Up(MigrationBuilder migrationBuilder)
        {
            migrationBuilder.DropForeignKey(
                name: "FK_AuthSessionApi_Empresa_EmpresaId",
                schema: "BBC",
                table: "AuthSessionApi");

            migrationBuilder.AlterColumn<int>(
                name: "Id",
                schema: "BBC",
                table: "EmpresaUsuario",
                type: "serial",
                nullable: false,
                oldClrType: typeof(int))
                .Annotation("Npgsql:ValueGenerationStrategy", NpgsqlValueGenerationStrategy.SerialColumn)
                .OldAnnotation("Npgsql:ValueGenerationStrategy", NpgsqlValueGenerationStrategy.SerialColumn);

            migrationBuilder.AlterColumn<int>(
                name: "EmpresaId",
                schema: "BBC",
                table: "AuthSessionApi",
                type: "int",
                nullable: true,
                oldClrType: typeof(int),
                oldType: "int");

            migrationBuilder.AddForeignKey(
                name: "FK_AuthSessionApi_Empresa_EmpresaId",
                schema: "BBC",
                table: "AuthSessionApi",
                column: "EmpresaId",
                principalSchema: "BBC",
                principalTable: "Empresa",
                principalColumn: "Id",
                onDelete: ReferentialAction.Restrict);
        }

        protected override void Down(MigrationBuilder migrationBuilder)
        {
            migrationBuilder.DropForeignKey(
                name: "FK_AuthSessionApi_Empresa_EmpresaId",
                schema: "BBC",
                table: "AuthSessionApi");

            migrationBuilder.AlterColumn<int>(
                name: "Id",
                schema: "BBC",
                table: "EmpresaUsuario",
                nullable: false,
                oldClrType: typeof(int),
                oldType: "serial")
                .Annotation("Npgsql:ValueGenerationStrategy", NpgsqlValueGenerationStrategy.SerialColumn)
                .OldAnnotation("Npgsql:ValueGenerationStrategy", NpgsqlValueGenerationStrategy.SerialColumn);

            migrationBuilder.AlterColumn<int>(
                name: "EmpresaId",
                schema: "BBC",
                table: "AuthSessionApi",
                type: "int",
                nullable: false,
                oldClrType: typeof(int),
                oldType: "int",
                oldNullable: true);

            migrationBuilder.AddForeignKey(
                name: "FK_AuthSessionApi_Empresa_EmpresaId",
                schema: "BBC",
                table: "AuthSessionApi",
                column: "EmpresaId",
                principalSchema: "BBC",
                principalTable: "Empresa",
                principalColumn: "Id",
                onDelete: ReferentialAction.Restrict);
        }
    }
}
