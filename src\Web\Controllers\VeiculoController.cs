using System;
using System.Threading.Tasks;
using Microsoft.AspNetCore.Mvc;
using SistemaInfo.BBC.Application.Interface.Veiculo;
using SistemaInfo.BBC.Application.Objects.Base;
using SistemaInfo.BBC.Application.Objects.Web.Veiculo;
using SistemaInfo.BBC.Domain.Enum;
using SistemaInfo.BBC.Web.Attributes;
using SistemaInfo.BBC.Web.Controllers.Base;
using SistemaInfo.Framework.DomainDrivenDesign.Domain.Engine;
using SistemaInfo.Framework.Utils;

namespace SistemaInfo.BBC.Web.Controllers
{
    /// <summary>
    /// 
    /// </summary>
    [Route("Veiculo")]
    public class VeiculoController : WebControllerBase<IVeiculoAppService>
    {
        /// <summary>
        /// 
        /// </summary>
        /// <param name="engine"></param>
        /// <param name="appService"></param>
        public VeiculoController(IAppEngine engine, IVeiculoAppService appService) : base(engine, appService)
        {
        }

        /// <summary>
        /// 
        /// </summary>
        /// <param name="lVeiculoStatus"></param>
        /// <returns></returns>
        [HttpPost("AlterarStatus")]
        [Menu(new[] { EMenus.Veiculo })]
        public JsonResult AlterarStatus([FromBody]VeiculoStatusRequest lVeiculoStatus)
        {
            try
            {
                AppService.AlterarStatus(lVeiculoStatus);
                return ResponseBase.ResponderSucesso(null);
            }
            catch (Exception e)
            {
                return  ResponseBase.ResponderErro("Não foi possível realizar a operação. Mensagem: " + e.Message);
            }
        }
        
        /// <summary>
        /// 
        /// </summary>
        /// <param name="request"></param>
        /// <returns></returns>
        [HttpPost("ConsultarGridVeiculoCombo")]
        [Menu(new[] { EMenus.PainelCiot })]
        public JsonResult ConsultarGridVeiculoCombo([FromBody]BaseGridRequest request)
        {
            var proprietarioId = request.Filters[0].Valor.ToIntSafe();
            
            var consultarGridVeiculoCombo = AppService.ConsultarGridVeiculoCombo(proprietarioId, request.Take, request.Page, request.Order, request.Filters);
            return ResponseBase.ResponderSucesso(consultarGridVeiculoCombo);
        }
        
        /// <summary>
        /// 
        /// </summary>
        /// <param name="request"></param>
        /// <returns></returns>
        [HttpPost("ConsultarGridVeiculoPortadorCiotCombo")]
        [Menu(new[] { EMenus.PainelCiot })]
        public async Task<JsonResult> ConsultarGridVeiculoPortadorCiotCombo([FromBody]BaseGridRequest request)
        {
            var cpfCnpjProprietario = request.Filters[0].Valor;
            
            var consultarGridVeiculoCombo = await AppService.ConsultarGridVeiculoPortadorCiotCombo(cpfCnpjProprietario, request.Take, request.Page, request.Order, request.Filters);
            return ResponseBase.ResponderSucesso(consultarGridVeiculoCombo);
        }
        
        /// <summary>
        /// 
        /// </summary>
        /// <param name="request"></param>
        /// <returns></returns>
        [HttpPost("ConsultarGridVeiculo")]
        [Menu(new[] { EMenus.Veiculo })]
        public JsonResult ConsultarGridVeiculo([FromBody]BaseGridRequest request)
        {
            var consultarGridVeiculo = AppService.ConsultarGridVeiculo( request.Take, request.Page, request.Order, request.Filters);
            return ResponseBase.ResponderSucesso(consultarGridVeiculo);
        }

        /// <summary>
        /// 
        /// </summary>
        /// <param name="lModel"></param>
        /// <returns></returns>
        [HttpPost("Salvar")]
        [Menu(new[] { EMenus.Veiculo })]
        public JsonResult SaveVeiculo([FromBody]VeiculoRequest lModel)
        {
            try
            {
                var lSaveVeiculo = AppService.Save(lModel).Result;
                
                return ResponseBase.BigJson(lSaveVeiculo);
            }
            catch (Exception e)
            {
                return ResponseBase.ResponderErro("Não foi possível realizar a operação. Mensagem: " + e.Message);
            }
        }
        
        /// <summary>
        /// 
        /// </summary>
        /// <param name="request"></param>
        /// <returns></returns>
        [HttpPost("CadastrarVeiculoCiot")]
        [Menu(new[] { EMenus.Veiculo })]
        public async Task<JsonResult> SaveVeiculoCiot([FromBody]VeiculoCiotRequest request)
        {
            try
            {
                var lSaveVeiculo = await AppService.CadastrarVeiculoCiot(request);
                
                return ResponseBase.BigJson(lSaveVeiculo);
            }
            catch (Exception e)
            {
                return ResponseBase.ResponderErro("Não foi possível realizar a operação. Mensagem: " + e.Message);
            }
        }
        
        /// <summary>
        /// 
        /// </summary>
        /// <param name="idVeiculo"></param>
        /// <returns></returns>
        [HttpGet("ConsultarPorId")]
        [Menu(new[] { EMenus.Veiculo })]
        public JsonResult ConsultarPorId(int idVeiculo)
        {
            try
            {
                var consultarVeiculo = AppService.ConsultarPorId(idVeiculo);
                return ResponseBase.ResponderSucesso(consultarVeiculo);
            }
            catch (Exception e)
            {
                return ResponseBase.ResponderErro("Veículo não encontrado! Mensagem: " + e.Message);
            }
        }
        
        /// <summary>
        /// 
        /// </summary>
        /// <param name="lVeiculoReq"></param>
        /// <returns></returns>
        [HttpGet("ConsultarPorPlaca")]
        [Menu(new[] { EMenus.Veiculo, EMenus.Portador })]
        public JsonResult ConsultarPorPlaca(VeiculoRequest lVeiculoReq)
        {
            try
            {
                var consultarVeiculo = AppService.ConsultaVeiculoEmpresaPorPlaca(lVeiculoReq).Result;
                return consultarVeiculo.sucesso ? ResponseBase.ResponderSucesso(consultarVeiculo.data) : ResponseBase.ResponderErro(consultarVeiculo.mensagem);
            }
            catch (Exception e)
            {
                return ResponseBase.ResponderErro("Veículo não encontrado! Mensagem: " + e.Message);
            }
        }
        
        /// <summary>
        /// 
        /// </summary>
        /// <param name="lVeiculoEmpReq"></param>
        /// <returns></returns>
        [HttpPost("SalvarVeiculoEmpresa")]
        [Menu(new[] { EMenus.PainelCiot,EMenus.Veiculo })]
        public JsonResult SaveVeiculoEmpresa([FromBody]VeiculoEmpresaRequest lVeiculoEmpReq)
        {
            try
            {
                var lSaveVeiculoEmp = AppService.SalvarVeiculoEmpresa(lVeiculoEmpReq); 
                return ResponseBase.BigJson(lSaveVeiculoEmp);
            }
            catch (Exception e)
            {
                return ResponseBase.ResponderErro("Não foi possível realizar a operação. Mensagem: " + e.Message);
            }
        }

        /// <summary>
        /// 
        /// </summary>
        /// <param name="placa"></param>
        /// <param name="frota"></param>
        /// <param name="portadorId"></param>
        /// <returns></returns>
        [HttpGet("ConsultarVeiculoAbastecimento")]
        [Menu(new[] { EMenus.LancamentoAbastecimento })]
        public async Task<JsonResult> ConsultarVeiculoAbastecimento(string placa, string frota, int portadorId)
        {
            var retorno = await AppService.ConsultarVeiculoAbastecimento(placa, frota, portadorId);
            return ResponseBase.Responder(retorno.sucesso, retorno.mensagem, retorno.data);
        }
        
        /// <summary>
        /// 
        /// </summary>
        /// <param name="filialId"></param>
        /// <param name="modeloId"></param>
        /// <returns></returns>
        [HttpGet("ConsultarVeiculoFilial")]
        [Menu(new[] { EMenus.AutorizacaoAbastecimento })]
        public JsonResult ConsultarVeiculoFilial(int filialId, int modeloId = 0)
        {
            try
            {
                var consultarAutorizacaoAbastecimento = AppService.ConsultarVeiculoFilial(filialId, modeloId);
                return ResponseBase.ResponderSucesso(consultarAutorizacaoAbastecimento.Result);
            }
            catch (Exception e)
            {
                return ResponseBase.ResponderErro("Filial não encontrada! Mensagem: " + e.Message);
            }
        }

        /// <summary>
        /// 
        /// </summary>
        /// <param name="veiculoId"></param>
        /// <returns></returns>
        [HttpGet("ConsultarVeiculoCombustivel")]
        [Menu(new[] { EMenus.AutorizacaoAbastecimento })]
        public JsonResult ConsultarVeiculoCombustivel(int veiculoId)
        {
            try
            {
                var consultarAutorizacaoAbastecimento = AppService.ConsultarVeiculoCombustivel(veiculoId);
                return ResponseBase.ResponderSucesso(consultarAutorizacaoAbastecimento.Result);
            }
            catch (Exception e)
            {
                return ResponseBase.ResponderErro("Filial não encontrada! Mensagem: " + e.Message);
            }
        }
    }
}