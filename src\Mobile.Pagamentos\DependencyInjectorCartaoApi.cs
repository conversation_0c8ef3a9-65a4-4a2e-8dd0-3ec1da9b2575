﻿using Microsoft.AspNetCore.Authorization;
using Microsoft.AspNetCore.Http;
using Microsoft.Extensions.DependencyInjection;
using Microsoft.Extensions.DependencyInjection.Extensions;
using SistemaInfo.BBC.Infra.Data.Context;
using SistemaInfo.BBC.Mobile.Pagamentos.Filters.SistemaInfo.Auth.Bus.Common.Infra.Security;
using SistemaInfo.Framework.CQRS.Message;

namespace SistemaInfo.BBC.Mobile.Pagamentos
{
    /// <summary>
    ///
    /// </summary>
    public class DependencyInjectorCartaoApi
    {
        /// <summary>
        /// Registrar serviços necessários para funcionamento da aplicação
        /// </summary>
        /// <param name="services"></param>
        public static void RegisterServices(IServiceCollection services)
        {
            AddAppInfrastructure(services);
            AddAppServices(services);
        }

        /// <summary>
        /// Recuros ténicos para funcionamento da aplicação, framework's de baixo nível da aplicação
        /// </summary>
        /// <param name="services"></param>
        private static void AddAppInfrastructure(IServiceCollection services)
        {
            services.AddScoped<ConfigMigrator>();
            services.TryAddSingleton<IHttpContextAccessor, HttpContextAccessor>();
            services.AddScoped<IAuthorizationHandler, JwtAuthorizationHandler>();            
            services.AddSingleton<IPublishMessageBusConfigurations>(provider => null);
            services.AddSingleton<IPublishMessageBusConfigurations>(provider => null);
            
        }

        /// <summary>
        /// Serviços de consultas para obter informações
        /// </summary>
        /// <param name="services"></param>
        private static void AddAppServices(IServiceCollection services)
        {
//            services.AddScoped<ICartaoAppServiceProcessadora, CartaoAppServiceProcessadoraRemoteBus>();
        }
    }
}