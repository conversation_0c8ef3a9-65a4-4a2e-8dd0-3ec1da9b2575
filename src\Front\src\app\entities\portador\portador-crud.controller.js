(function () {
    'use strict';

    angular.module('bbcWeb').controller('PortadorCrudController', PortadorCrudController);

    PortadorCrudController.$inject = ['toastr', '$rootScope', 'BaseService', '$state', '$stateParams', '$window', 'PERFIL_ADMINISTRADOR', '$scope', '$timeout', 'PersistentDataService', 'DefaultsService', '$uibModal', 'EmprestimoCrudService'];

    function PortadorCrudController(
        toastr, $rootScope,
        BaseService, $state,
        $stateParams, $window,
        PERFIL_ADMINISTRADOR,
        $scope, $timeout,
        PersistentDataService,
        DefaultsService,
        $uibModal,
        EmprestimoCrudService
    ) {
        var vm = this;
        vm.portador = {};
        vm.estados = [];
        vm.cidades = [];
        vm.portador.repLegaisList = [];
        vm.portador.contaConductor = [];
        vm.portador.portadorCentroCusto = [];
        vm.cartoesList = [];

        vm.optionsDatePicker = {
            maxDate: new Date()
        };

        vm.estadosDisabled = true;
        vm.cidadesDisabled = true;
        vm.carregandoEdit = false;
        vm.saving = false;
        vm.carregandoSpinner = false;

        vm.labelCpfCnpj = "CNPJ";
        vm.mascaraCpfCnpj = "99.999.999/9999-99";

        vm.cpfCnpjInformado = null;
        vm.tipoPessoaInformado = null;
        vm.portadorRecuperado = null;

        //----------------------Wizard tabs---------------------------

        vm.pessoaJuridicaOnlyTabs = [3]; //tipoPessoa 2
        vm.frotaOnlyTabs = [5]; //atividade 1
        vm.notFrotaOnlyTabs = [4]; //atividade 2 e 3
        
        function validaAtivoIndex(index, voltando) {
            var nextIndex = index + (voltando ? -1 : 1);

            vm.lastIndex = getLastIndex();

            if (nextIndex > vm.lastIndex) return index;

            if (vm.portador.tipoPessoa !== 2) {
                nextIndex = (vm.pessoaJuridicaOnlyTabs.includes(nextIndex) ? validaAtivoIndex(nextIndex, voltando) : nextIndex)
            }
            if (vm.portador.atividade === 1) {
                nextIndex = (vm.notFrotaOnlyTabs.includes(nextIndex) ? validaAtivoIndex(nextIndex, voltando) : nextIndex)
            }
            if (vm.portador.atividade !== 1) {
                nextIndex = (vm.frotaOnlyTabs.includes(nextIndex) ? validaAtivoIndex(nextIndex, voltando) : nextIndex)
            }
            
            return nextIndex;
        }

        function getLastIndex() {
            var lastIndex;
            if (vm.portador.atividade === 1) lastIndex = 5;
            if (vm.portador.atividade !== 1) lastIndex = 4;
            return lastIndex;
        }

        vm.onClickVoltar = function (wizard) {
            const currentIndex = wizard.getActivePosition();
            if (currentIndex === 1) $state.go('portador.index');
            const nextIndex = validaAtivoIndex(currentIndex, true);
            wizard.go(nextIndex);
        }

        vm.onClickAvancar = function (wizard) {
            const currentIndex = wizard.getActivePosition();
            const nextIndex = validaAtivoIndex(currentIndex, false);
            wizard.go(nextIndex);
        }

        //----------------------End Wizard tabs---------------------------

        vm.headerItems = [{
            name: 'Cadastro'
        }, {
            name: 'Portador',
            link: 'portador.index'
        }, {
            name: $stateParams.link == 'novo' ? 'Novo' : 'Editar'
        }];

        vm.comboTipoPessoa = {
            data: [
                {id: 1, descricao: 'Física'},
                {id: 2, descricao: 'Jurídica'}
            ]
        };

        vm.comboSexo = {
            data: [
                {id: 1, descricao: 'Masculino'},
                {id: 2, descricao: 'Feminino'},
                {id: 3, descricao: 'Outros'},
                {id: 4, descricao: 'Indefinido'}
            ]
        };

        vm.comboAtividade = {
            data: [
                {id: 1, descricao: 'Frota'},
                {id: 2, descricao: 'Agregado'},
                {id: 3, descricao: 'Terceiro'}
            ]
        };

        vm.load = function () {
            carregarFuncoesIniciais();
            vm.portador.tipoPessoa = 2;
            vm.portador.atividade = 1;
            vm.lastIndex = getLastIndex();
            if (!vm.isAdmin()) {
                vm.consultaEmpresa.selectedText = $window.localStorage.getItem("empresaNome");
                vm.consultaEmpresa.selectedValue = parseInt($window.localStorage.getItem("empresaId"));
            }
        };
        
        function carregarFuncoesIniciais() {
            carregarEstados();
        }

        vm.loadEdit = function (id) {
            vm.carregandoEdit = true;
            carregarFuncoesIniciais();

            $timeout(function () {
                consultarPorId(id, function (portadorEdit) {
                    carregarCidades(portadorEdit.estadoId);
                    vm.portador = portadorEdit;
                    if(portadorEdit.empresaId != 
                        $rootScope.usuarioLogado.empresaId &&
                        !vm.isAdmin()){
                        $state.go('portador.index');
                        toastr.error("Você não pode editar este usuário!")
                    }

                    vm.consultaEmpresa.selectedValue = vm.portador.empresaId;
                    vm.consultaEmpresa.selectedText = vm.portador.empresaNome;

                    vm.consultaVeiculo1.selectedValue = vm.portador.carretaId;
                    vm.consultaVeiculo1.selectedText = vm.portador.carreta ? vm.portador.carreta.placa : "";

                    vm.consultaVeiculo2.selectedValue = vm.portador.carreta2Id;
                    vm.consultaVeiculo2.selectedText = vm.portador.carreta2 ? vm.portador.carreta2.placa : "";

                    vm.consultaVeiculo3.selectedValue = vm.portador.carreta3Id;
                    vm.consultaVeiculo3.selectedText = vm.portador.carreta3 ? vm.portador.carreta3.placa : "";

                    if (vm.portador.cpfCnpj.length > 11) {
                        vm.labelCpfCnpj = "CNPJ";
                        vm.mascaraCpfCnpj = "99.999.999/9999-99"
                        vm.portador.tipoPessoa = 2;
                    } else {
                        vm.labelCpfCnpj = "CPF";
                        vm.mascaraCpfCnpj = "999.999.999-99"
                        vm.portador.tipoPessoa = 1;
                    }

                    vm.lastIndex = getLastIndex();
                    vm.carregandoEdit = false;
                });
            }, 1000);

            $timeout(function () {
                vm.carregandoEdit = false;
            }, 6000);
        };

        vm.consultaPortador = {
            columnDefs: [{
                name: 'Código',
                field: 'id',
                width: 80,
                type: 'number',
                primaryKey: true
            }, {
                name: 'Nome',
                field: 'nome',
                width: '*',
                minWidth: 160
            }, {
                name: 'CPF',
                field: 'cpfCnpj',
                enableGrouping: true,
                width: 145
            }],
            desiredValue: 'id',
            desiredText: 'nome',
            url: 'Portador/ConsultarGridPortadorPF',
            paramsMethod: function () {
                return {}
            }
        };

        vm.consultaVeiculo1 = {
            columnDefs: [{
                name: 'Código',
                field: 'id',
                width: 80,
                type: 'number',
                primaryKey: true
            }, {
                name: 'Placa',
                field: 'placa',
                width: '*',
                minWidth: 160
            }, {
                name: 'Renavam',
                field: 'renavam',
                enableGrouping: true,
                width: 145
            }],
            desiredValue: 'id',
            desiredText: 'placa',
            url: 'Veiculo/ConsultarGridVeiculo',
            paramsMethod: function () {
                return {}
            }
        };

        vm.consultaVeiculo2 = {
            columnDefs: [{
                name: 'Código',
                field: 'id',
                width: 80,
                type: 'number',
                primaryKey: true
            }, {
                name: 'Placa',
                field: 'placa',
                width: '*',
                minWidth: 160
            }, {
                name: 'Renavam',
                field: 'renavam',
                enableGrouping: true,
                width: 145
            }],
            desiredValue: 'id',
            desiredText: 'placa',
            url: 'Veiculo/ConsultarGridVeiculo',
            paramsMethod: function () {
                return {}
            }
        };
        vm.consultaVeiculo3 = {
            columnDefs: [{
                name: 'Código',
                field: 'id',
                width: 80,
                type: 'number',
                primaryKey: true
            }, {
                name: 'Placa',
                field: 'placa',
                width: '*',
                minWidth: 160
            }, {
                name: 'Renavam',
                field: 'renavam',
                enableGrouping: true,
                width: 145
            }],
            desiredValue: 'id',
            desiredText: 'placa',
            url: 'Veiculo/ConsultarGridVeiculo',
            paramsMethod: function () {
                return {}
            }
        };

        vm.consultaCentroCusto = {
            columnDefs: [{
                name: 'Código',
                field: 'id',
                width: 80,
                type: 'number',
                primaryKey: true
            }, {
                name: 'Descrição',
                field: 'descricao',
                width: '*',
                minWidth: 150
            }],
            desiredValue: 'id',
            desiredText: 'descricao',
            url: 'CentroCusto/ConsultarGridCentroCusto',
            paramsMethod: function () {
                return {
                    Ativo: 1
                }
            },
        };

        vm.consultaVeiculo = function (placa) {
            if (vm.portador.placa && vm.portador.placa.length == 7) {
                BaseService.get('Veiculo', 'ConsultarPorPlaca', {"placa": vm.portador.placa}, {}).then(function (response) {
                    if (!response.success) {
                        toastr.error(response.message);
                        return;
                    } else {
                        if (!response.data) {
                            toastr.warning("Placa não encontrada, informe uma placa previamente vinculada ao cadastro de Veículos!");
                            vm.portador.placa = "";
                            return;
                        }
                        vm.verificaPlacas(0);
                    }
                });
            } else {
                toastr.warning("Preencha corretamente o campo PLACA");
            }
        }

        $scope.$watch('vm.consultaVeiculo1.selectedValue', function () {
            if (vm.portador.atividade === 1) {
                vm.verificaPlacas(1);
            }
        });

        $scope.$watch('vm.consultaVeiculo2.selectedValue', function () {
            if (vm.portador.atividade === 1) {
                vm.verificaPlacas(2);
            }
        });

        $scope.$watch('vm.consultaVeiculo3.selectedValue', function () {
            if (vm.portador.atividade === 1) {
                vm.verificaPlacas(3);
            }
        });

        vm.verificaPlacas = function (fieldClearIfDuplicated) {
            var isDuplicated = false;
            var data = [];

            if ((vm.portador.placa != "" && vm.portador.placa != null && vm.portador.placa != undefined) ||
                (vm.consultaVeiculo1.selectedText != "" && vm.consultaVeiculo1.selectedText != undefined) ||
                (vm.consultaVeiculo2.selectedText != "" && vm.consultaVeiculo2.selectedText != undefined) ||
                (vm.consultaVeiculo3.selectedText != "" && vm.consultaVeiculo3.selectedText != undefined)) {
                var listaPlacas = [data = {
                    placa: vm.portador.placa ? vm.portador.placa.toUpperCase() : "",
                    carreta1: vm.consultaVeiculo1.selectedText ? vm.consultaVeiculo1.selectedText.replace("-", "") : "",
                    carreta2: vm.consultaVeiculo2.selectedText ? vm.consultaVeiculo2.selectedText.replace("-", "") : "",
                    carreta3: vm.consultaVeiculo3.selectedText ? vm.consultaVeiculo3.selectedText.replace("-", "") : ""
                }];

                for (var i = 0; i < listaPlacas.length; i++) {
                    if (listaPlacas[i].placa != "" && listaPlacas[i].placa == listaPlacas[i].carreta1 || listaPlacas[i].placa == listaPlacas[i].carreta2 || listaPlacas[i].placa == listaPlacas[i].carreta3) {
                        toastr.error("Placa já selecionada!");
                        isDuplicated = true;
                    }
                    if (listaPlacas[i].carreta1 != "") {
                        if (listaPlacas[i].carreta1 == listaPlacas[i].carreta2 || listaPlacas[i].carreta1 == listaPlacas[i].carreta3) {
                            toastr.error("Placa já selecionada!");
                            isDuplicated = true;
                        }
                    }
                    if (listaPlacas[i].carreta2 != "") {
                        if (listaPlacas[i].carreta2 == listaPlacas[i].carreta3 || listaPlacas[i].carreta2 == listaPlacas[i].carreta1) {
                            toastr.error("Placa já selecionada!");
                            isDuplicated = true;
                        }
                    }
                    if (listaPlacas[i].carreta3 != "") {
                        if (listaPlacas[i].carreta3 == listaPlacas[i].carreta2 || listaPlacas[i].carreta3 == listaPlacas[i].carreta1) {
                            toastr.error("Placa já selecionada!");
                            isDuplicated = true;
                        }
                    }
                }

                if (isDuplicated) {
                    if (fieldClearIfDuplicated == 0) {
                        vm.portador.placa = "";
                    }
                    if (fieldClearIfDuplicated == 1) {
                        vm.consultaVeiculo1.selectedText = "";
                        vm.consultaVeiculo1.selectedValue = "";
                    }
                    if (fieldClearIfDuplicated == 2) {
                        vm.consultaVeiculo2.selectedText = "";
                        vm.consultaVeiculo2.selectedValue = "";
                    }
                    if (fieldClearIfDuplicated == 3) {
                        vm.consultaVeiculo3.selectedText = "";
                        vm.consultaVeiculo3.selectedValue = "";
                    }
                }
            }
        }

        vm.adicionarCentroCusto = function () {
            if (vm.consultaCentroCusto.selectedValue != null) {

                for (var i = 0; i < vm.portador.portadorCentroCusto.length; i++) {
                    if (vm.portador.portadorCentroCusto[i].centroCustoId == vm.consultaCentroCusto.selectedValue) {
                        toastr.warning("Centro de custo já informado!");
                        return;
                    }
                }

                var centroCusto = {
                    centroCustoId: vm.consultaCentroCusto.selectedValue,
                    descricao: vm.consultaCentroCusto.selectedText
                };
                vm.portador.portadorCentroCusto.push(centroCusto);
                vm.consultaCentroCusto.selectedValue = null;
                vm.consultaCentroCusto.selectedText = null;
            } else {
                toastr.warning("Selecione um centro de custo!");
                return;
            }
        }

        vm.removeCentroCusto = function (centroCustoId) {
            for (var i = 0; i < vm.portador.portadorCentroCusto.length; i++) {
                if (vm.portador.portadorCentroCusto[i].centroCustoId == centroCustoId) {
                    var index = vm.portador.portadorCentroCusto.indexOf((vm.portador.portadorCentroCusto[i]));
                    vm.portador.portadorCentroCusto.splice(index, 1);
                    toastr.success("Centro de custo removido!");

                    $rootScope.portador.portadorCentroCusto = vm.portador.portadorCentroCusto;
                }
            }
        };

        vm.AtribuirTitular = function () {
            vm.carregandoSpinner = true;

            vm.portador.cartaoId = vm.cartaoId;

            if (vm.portador.contaConductor == null && vm.isNew() && vm.portador.tipoPessoa === 1)
                return "Nenhuma conta foi informada!";

            vm.portador.Motivo = vm.motivo;

            BaseService.post("Portador", "AdicionarCartao", vm.portador)
                .then(function (response) {
                    vm.carregandoSpinner = false;
                    if (response.success) {
                        toastr.success(response.message);

                        if (vm.portador.contaConductor == null)
                            $state.go('portador.index');
                    } else {
                        toastr.error(response.message);
                    }

                    vm.cartaoId = null;
                    vm.motivo = '';
                });
        };

        vm.ReativarConta = function (contaId) {
            BaseService.post("Portador", "ReativarConta", {
                idConta: contaId
            }).then(function (response) {
                if (response.success) {
                    toastr.success('Conta reativado com sucesso!');
                    vm.portador.contaConductor[0].statusConta = "Normal";
                } else {
                    toastr.error(response.message);
                }
            });
        };

        vm.BloquearConta = function (contaId) {
            BaseService.post("Portador", "BloquearConta", {
                idConta: contaId
            }).then(function (response) {
                if (response.success) {
                    toastr.success('Conta bloqueada com sucesso!');
                    vm.portador.contaConductor[0].statusConta = "Bloqueada";
                } else {
                    toastr.error(response.message);
                }
            });
        };

        vm.VerCartao = function () {
            $uibModal.open({
                animation: true,
                ariaLabelledBy: 'modal-title',
                ariaDescribedBy: 'modal-body',
                templateUrl: 'app/entities/portador/modal/modal-cartao-portador.html',
                controller: 'PortadorModalDadosCartaoCrudController',
                controllerAs: 'vm',
                keyboard: true,
                size: 'lg',
                resolve: {
                    contaId: vm.portador.contaConductor[0].contaId
                }
            });
        };

        vm.adicionarRepLegal = function () {
            var permiteAdicionar = false;

            if (!vm.consultaPortador.selectedEntity)
                return toastr.error("Nenhum representante foi selecionado.");

            var objetosValidos = _.filter(vm.portador.repLegaisList, function (v) {
                return v.id === vm.consultaPortador.selectedEntity.id;
            });

            if (objetosValidos.length > 0) {
                return toastr.error("Este representante já foi adicionado.");
            }

            if (vm.consultaPortador.selectedEntity != undefined && vm.consultaPortador.selectedEntity.cpfCnpj != undefined && vm.consultaPortador.desiredValue != "") {
                permiteAdicionar = true;
            }

            if (permiteAdicionar) {
                vm.portador.repLegaisList.push(angular.copy(vm.consultaPortador.selectedEntity));
                vm.clearConsultaPortador();
            } else
                toastr.error("Por favor, informe o representante legal.");
        };

        vm.clearConsultaPortador = function () {
            vm.consultaPortador.selectedEntity = undefined;
            vm.consultaPortador.selectedValue = undefined;
            vm.consultaPortador.selectedText = "Representante Legal";
        };

        vm.removerRepLegal = function (repLegal) {
            for (var i = 0; i < vm.portador.repLegaisList.length; i++) {
                if (vm.portador.repLegaisList[i].id == repLegal.id) {
                    var index = vm.portador.repLegaisList.indexOf((vm.portador.repLegaisList[i]));
                    vm.portador.repLegaisList.splice(index, 1)
                }
            }

            if (vm.portador.repLegaisList.length < 1) {
                vm.consultaPortador.selectedEntity = undefined;
                vm.consultaPortador.selectedValue = undefined;
                vm.consultaPortador.selectedText = undefined;
            }
        };

        function setDataEmissaoIdentidade(data) {
            if (angular.isDefinedNotNull(data)) {
                vm.portador.emissaoIdentidade = new Date(data);
            }
        };

        function setDataAberturaEmpresa(data) {
            if (angular.isDefinedNotNull(data)) {
                vm.portador.dataAberturaEmpresa = new Date(data);
            }
        };

        function setDataNascimento(data) {
            if (angular.isDefinedNotNull(data)) {
                vm.portador.dataNascimento = new Date(data);
            }
        };

        function consultarPorId(id, callback) {
            BaseService.get('Portador', 'BuscarPorId', {
                idPortador: id
            }).then(function (response) {
                if (!response.success) {
                    toastr.error(response.message);
                    return;
                }
                if (angular.isFunction(callback))
                    callback(angular.fromJson(response.data));
                carregarCidades(vm.portador.estadoId);
                setDataEmissaoIdentidade(vm.portador.emissaoIdentidade);
                setDataAberturaEmpresa(vm.portador.dataAberturaEmpresa);
                setDataNascimento(vm.portador.dataNascimento);
            });
        };

        function carregarEstados() {
            BaseService.get("Endereco", "ConsultarEstados")
                .then(function (response) {
                    if (response.success) {
                        vm.estados = response.data;
                        vm.estadosDisabled = false;
                    }
                });
        }

        function carregarCidades(EstadoId) {
            if(!EstadoId) return;
            BaseService.get("Endereco", "ConsultarCidadesPorEstado", {
                estadoId: EstadoId
            }).then(function (response) {
                if (response.success) {
                    vm.cidades = response.data;
                    vm.cidadesDisabled = false;
                }
            });
        }

        vm.tipoPessoaChange = function (tipoPessoa) {
            if (tipoPessoa == 1) {
                vm.labelCpfCnpj = "CPF";
                vm.mascaraCpfCnpj = "999.999.999-99"
                vm.portador.cpfCnpj = null;
            }

            if (tipoPessoa == 2) {
                vm.labelCpfCnpj = "CNPJ";
                vm.mascaraCpfCnpj = "99.999.999/9999-99"
                vm.portador.nomeMae = null;
                vm.portador.nomePai = null;
                vm.portador.sexo = null;
                vm.portador.numeroIdentidade = null;
                vm.portador.orgaoEmissao = null;
                vm.portador.ufEmissao = null;
                vm.portador.emissaoIdentidade = null;
                vm.portador.cpfCnpj = null;
            }
        };

        function init() {
            if (vm.isNew()) {
                vm.portador.id = 'Auto';
            }
            carregarEmpresas();
        }

        vm.isNew = function () {
            return $stateParams.link == 'novo';
        };

        vm.buscarEndereco = function (cep) {
            if (cep) {
                limparEndereco();
                BaseService.getEndereco(cep).then(function (response) {
                    if (response && response.erro) {
                        toastr.warning("Endereço não localizado.");
                    }

                    carregarEstados();

                    $timeout(function () {
                        var estado = _.find(vm.estados, ['sigla', response.uf]);

                        if (angular.isDefined(estado))

                            vm.portador.estadoId = estado.id;
                        carregarCidades(vm.portador.estadoId);
                        $timeout(function () {
                            var cidade = _.find(vm.cidades, ['descricao', response.localidade]);
                            if (angular.isDefined(cidade))
                                vm.portador.cidadeId = cidade.id;
                        }, 1500);
                    }, 1500);

                    $timeout(function () {
                        if (angular.isDefined(response.logradouro))
                            vm.portador.endereco = response.logradouro;
                    }, 1500);

                    $timeout(function () {
                        if (angular.isDefined(response.bairro))
                            vm.portador.bairro = response.bairro;
                    }, 1500);
                });
            }
        };

        function limparEndereco() {
            vm.portador.estadoId = null;
            vm.portador.cidadeId = null;
            vm.estados = null;
            vm.cidades = null;
            vm.portador.endereco = null;
            vm.portador.bairro = null;
            vm.portador.enderecoNumero = null;
            vm.portador.complemento = null;
        };

        vm.estadoChange = function (estadoId) {
            vm.cidades = null;
            vm.cidadesDisabled = true;
            vm.portador.cidadeId = null;
            carregarCidades(estadoId);
        };

        vm.save = function (form) {
            if (!form.$valid) {
                toastr.error('Todos os campos obrigatórios e/ou inválidos devem ser preenchidos corretamente com seus respectivos valores!');
                return;
            }

            vm.saving = true;

            if (vm.portador.repLegaisList <= 0 && vm.portador.tipoPessoa == 2 && !vm.isAdmin()) {
                vm.saving = false;
                return toastr.error("É obrigatório informar pelo menos um representante legal.");
            }

            if (vm.consultaEmpresa.selectedValue) {
                vm.portador.EmpresaId = vm.consultaEmpresa.selectedValue;
            }
            if (vm.consultaVeiculo1.selectedValue != null) {
                vm.portador.CarretaId = vm.consultaVeiculo1.selectedValue;
            }
            if (vm.consultaVeiculo2.selectedValue != null) {
                vm.portador.Carreta2Id = vm.consultaVeiculo2.selectedValue;
            }
            if (vm.consultaVeiculo3.selectedValue != null) {
                vm.portador.Carreta3Id = vm.consultaVeiculo3.selectedValue;
            }
            //limpeza de entidade de envio caso removida modais de carreta
            if (vm.consultaVeiculo1.selectedValue == 0) {
                vm.portador.CarretaId = null;
            }
            if (vm.consultaVeiculo2.selectedValue == 0) {
                vm.portador.Carreta2Id = null;
            }
            if (vm.consultaVeiculo3.selectedValue == 0) {
                vm.portador.Carreta3Id = null;
            }

            if (vm.portador.atividade !== 1 && (vm.portador.contaConductor == null || vm.portador.contaConductor.length == 0)) {
                Sistema.Msg.confirm('Deseja salvar o portador sem informar uma conta?', function (result) {
                    BaseService.post('Portador', 'Salvar', vm.portador).then(function (response) {
                        vm.saving = false;

                        if (response && response.data && response.data.cadastradoEmOutraEmpresa) {
                            return Sistema.Msg.confirm('Portador já cadastrado em outra empresa, deseja continuar?', function () {
                                BaseService.post('Portador', 'SalvarPortadorEmpresa', {
                                    portadorId: response.data.portadorId,
                                    empresaId: response.data.empresaId
                                }).then(function (response) {
                                    if (response && !response.sucesso) {
                                        return toastr.error(response.mensagem);
                                    }
                                    toastr.success('Registro salvo com sucesso!');
                                    $state.go('portador.index');
                                });
                            }, angular.noop());
                        }

                        if (response && !response.sucesso)
                            return toastr.error(response.mensagem);
                        else {
                            toastr.success(response && !angular.isUndefinedOrNullOrEmpty(response.mensagem) ? response.mensagem : "Portador salvo com sucesso!");
                            $state.go('portador.index');
                        }
                    });
                }, function () {
                    vm.saving = false;
                });
            } else {
                BaseService.post('Portador', 'Salvar', vm.portador).then(function (response) {
                    vm.saving = false;

                    if (response && response.data && response.data.cadastradoEmOutraEmpresa) {
                        return Sistema.Msg.confirm('Portador já cadastrado em outra empresa, deseja continuar?', function () {
                            BaseService.post('Portador', 'SalvarPortadorEmpresa', {
                                portadorId: response.data.portadorId,
                                empresaId: response.data.empresaId
                            }).then(function (response) {
                                if (response && !response.sucesso) {
                                    return toastr.error(response.mensagem);
                                }
                                toastr.success('Registro salvo com sucesso!');
                                $state.go('portador.index');
                            });
                        }, angular.noop());
                    }

                    if (response && !response.sucesso)
                        return toastr.error(response.mensagem);
                    else {
                        toastr.success(response && !angular.isUndefinedOrNullOrEmpty(response.mensagem) ? response.mensagem : "Portador salvo com sucesso!");
                        $state.go('portador.index');
                    }
                });
            }
        };

        vm.isAdmin = function () {
            return $rootScope.usuarioLogado.administrador == true;
        };

        var selfScope = PersistentDataService.get('PortadorCrudController');

        if ($stateParams.link == 'novo')
            vm.portador.id = 'Auto';


        init();

        // DO NOT TOUCH!! 666
        $scope.$on('$stateChangeStart', function (arg1, toState) {
            if (toState.name === 'portador.index')
                PersistentDataService.remove('PortadorCrudController');
            else
                PersistentDataService.store('PortadorCrudController', vm, "Cadastro - Portador", null, "portador.portador-crud", vm.portador.id);
        });

        if (angular.isDefined(selfScope))
            angular.extend(vm, selfScope.data);
        else if (!vm.isNew())
            vm.loadEdit($stateParams.link);
        else
            vm.load();

        $timeout(function () {
            PersistentDataService.remove('PortadorController');
        }, 15);

        function carregarEmpresas() {
            vm.consultaEmpresa = {
                columnDefs: [{
                    name: 'Cód.',
                    field: 'id',
                    width: 60,
                    type: 'number',
                    primaryKey: true
                }, {
                    name: 'Nome Fantasia',
                    field: 'nomeFantasia',
                    width: '*'
                }, {
                    name: 'Razão Social',
                    field: 'razaoSocial',
                    width: '*'
                }, {
                    name: 'Email',
                    field: 'email',
                    width: 120
                }],
                desiredValue: 'id',
                desiredText: 'nomeFantasia',
                url: 'Empresa/ConsultarGridEmpresaCombo',
                paramsMethod: function () {
                    return {}
                }
            };
        }

        vm.sinalizaClienteEmprestimo = function () {
            vm.sinalizando = true;
            EmprestimoCrudService.sinalizaClienteEmprestimo(vm.portador, function (sucesso, mensagem) {
                if (sucesso)
                    toastr.success(mensagem);
                else
                    toastr.error(mensagem);

                vm.sinalizando = false;
            });
        }
    }
})();
