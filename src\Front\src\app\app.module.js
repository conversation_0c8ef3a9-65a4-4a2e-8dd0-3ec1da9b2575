(function () {
    'use strict';

    angular.module('bbcWeb', [
        'ngAnimate',
        'ngCookies',
        'ngTouch',
        'ui.select',
        'ngSanitize',
        'ui.mask',
        'ngMessages',
        'ngAria',
        'ngResource',
        'ui.router',
        'ui.bootstrap',
        'toastr',
        'bootstrapLightbox',
        'dndLists',
        'ngBootbox',
        'ui.grid',
        'ui.grid.grouping',
        'ui.grid.cellNav',
        'ui.grid.edit',
        'ui.grid.resizeColumns',
        'ui.grid.pinning',
        'ui.grid.saveState',
        'ui.grid.selection',
        'ui.grid.moveColumns',
        'ui.grid.exporter',
        'ui.grid.importer',
        'ui.grid.pagination',
        'ui.bootstrap.tooltip',
        'ui.grid.expandable',
        'uiSwitch',
        'ngMap',
        'daterangepicker',
        'mwl.confirm',
        'ui.utils.masks',
        'angular-ladda',
        'cp.ngConfirm',
        'toggle-switch',
        'naif.base64',
        'star-rating',
        'oitozero.ngSweetAlert',
        // Routes
        'bbcWeb.veiculo.state',
        'bbcWeb.cliente.state',
        'bbcWeb.filial.state',
        'bbcWeb.configuracao',
        'bbcWeb.configuracao.modulo.state',
        'bbcWeb.usuario.state',
        'bbcWeb.ultimos-cadastros.state',
        'bbcWeb.request.state',
        'bbcWeb.menu.state',
        'bbcWeb.painel-pagamento.state',
        'bbcWeb.empresa.state',
        'bbcWeb.portador.state',
        'bbcWeb.painelCiot.state',
        'bbcWeb.retencao.state',
        'bbcWeb.central-mensagens.state',
        'bbcWeb.posto.state',
        'bbcWeb.modeloVeiculo.state',
        'bbcWeb.grupoUsuario.state',
        'bbcWeb.empresa-avaliacao.state',
        'bbcWeb.emprestimo.state',
        'bbcWeb.fabricante.state',
        'bbcWeb.tipo-empresa.state',
        'bbcWeb.combustivel.state',
        'bbcWeb.bloqueioSpd.state',
        'atsWeb.environment',
        'bbcWeb.parametros.state',
        'bbcWeb.banco.state',
        'bbcWeb.centro-custo.state',
        'bbcWeb.atualizacao-preco-combustivel.state',
        'bbcWeb.mdrprazos.state',
        'bbcWeb.central-notificacoes.state',
        'bbcWeb.central-pendencias.state',
        'bbcWeb.sem-acesso.state',
        'bbcWeb.auditoria-seguranca.state',
        'bbcWeb.autorizacao-abastecimento.state',
        'bbcWeb.autorizacao-contingencia.state',
        'bbcWeb.painel-pagamento-abastecimento.state',
        'bbcWeb.painel-protocolo-abastecimento.state',
        'bbcWeb.cfop.state',
        'bbcWeb.painel-financeiro.state',
        'bbcWeb.percentual-transferencia.state',
        'bbcWeb.documentos-processo-vinculado.state',
        'bbcWeb.painel-pedidos-pendentes.state',
        'bbcWeb.painel-abastecimento.state',
        'bbcWeb.gestao-transportador.state',
        'bbcWeb.grupo-empresa.state',
        'bbcWeb.client-secret.state',
        'bbcWeb.viagens.state',
        'bbcWeb.central-pendencias-movida.state',
        'bbcWeb.painel-pagamento-vale-pedagio.state',
        'bbcWeb.monitoramento-servidores-ciot.state',
        'bbcWeb.integrar-contas.state',
        'bbcWeb.painel-saldo.state'
        

    ]).run(function (stateHandler) {
        stateHandler.initialize();
    }).run(function (BaseService, $rootScope, $location, $window, $state) {
        $rootScope.$on('$stateChangeStart', function (event, toState, toParams, fromState, fromParams) {

            //direcionando para tela inicial caso o usuário não tenha permissão no link navegado
            if(toState.name !== 'index.main') {
                //verificador de senha atualizada
                if($rootScope.senhaAtualizada == false 
                    && toState.name.toString().split('.')[0] != "sem-acesso" 
                    && toState.name.toString().split('.')[0] != "logout"){
                    $rootScope.menucarregar = true; 

                    $rootScope.GetMenuUsuarioLogado(); 

                    $state.go('index.main');
                    $location.path("index");
                }
                
                var linksPermitidos = $window.localStorage.getItem('linksPermitidos');

                if (linksPermitidos != null) {
                    var link = toState.name.toString().split('.')[0];

                    if (linksPermitidos.includes(link) == false && link != "sem-acesso" && link != "logout") {

                        var AuditoriaSeguranca = {}

                        AuditoriaSeguranca.Id = 0
                        AuditoriaSeguranca.UsuarioId = 0;
                        AuditoriaSeguranca.Menu = link;

                        $state.go('sem-acesso.index');
                        $location.path("sem-acesso/index");

                        BaseService.post('AuditoriaSeguranca', 'Salvar', AuditoriaSeguranca).then(function (response) {
                            return;
                        });
                    }
                }
            }
        });
    }).run.$inject = ['stateHandler'];
})();