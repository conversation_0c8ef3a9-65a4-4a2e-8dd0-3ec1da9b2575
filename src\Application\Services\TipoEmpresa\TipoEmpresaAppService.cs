using System;
using System.Collections.Generic;
using System.Linq;
using System.Linq.Dynamic.Core;
using System.Threading.Tasks;
using AutoMapper.QueryableExtensions;
using SistemaInfo.BBC.Application.Interface.TipoEmpresa;
using SistemaInfo.BBC.Application.Objects.Base;
using SistemaInfo.BBC.Application.Objects.Web.TipoEmpresa;
using SistemaInfo.BBC.Domain.Grid;
using SistemaInfo.BBC.Domain.Helper;
using SistemaInfo.BBC.Domain.Models.TipoEmpresa.Commands;
using SistemaInfo.BBC.Domain.Models.TipoEmpresa.Repository;
using SistemaInfo.Framework.DomainDrivenDesign.Application.Service;
using SistemaInfo.Framework.DomainDrivenDesign.Domain.Engine;

namespace SistemaInfo.BBC.Application.Services.TipoEmpresa
{
    public class TipoEmpresaAppService : AppService<Domain.Models.TipoEmpresa.TipoEmpresa,
        ITipoEmpresaReadRepository, ITipoEmpresaWriteRepository>, ITipoEmpresaAppService
    {

        public TipoEmpresaAppService(
            IAppEngine engine,
            ITipoEmpresaReadRepository readRepository,
            ITipoEmpresaWriteRepository writeRepository) : base(
            engine, readRepository, writeRepository)
        {
        }

        public ConsultarGridTipoEmpresaResponse ConsultarGridTipoEmpresa(int take, int page, OrderFilters orderFilters,
            List<QueryFilters> filters, bool isModal)
        {
            IQueryable<Domain.Models.TipoEmpresa.TipoEmpresa> lTipoEmpresa;
            
            lTipoEmpresa = isModal ? Repository.Query.GetAll() : Repository.Query.Where(a => a.Ativo == 1);
            
            lTipoEmpresa = lTipoEmpresa.AplicarFiltrosDinamicos(filters);
            lTipoEmpresa = string.IsNullOrWhiteSpace(orderFilters?.Campo)
                ? lTipoEmpresa.OrderByDescending(o => o.Id)
                : lTipoEmpresa.OrderBy($"{orderFilters.Campo} {orderFilters.Operador.DescriptionAttr()}");

            var lCount = lTipoEmpresa.Count();
            var retorno = lTipoEmpresa.Skip((page - 1) * take)
                .Take(take).ProjectTo<ConsultarGridTipoEmpresa>(Engine.Mapper.ConfigurationProvider).ToList();

            return new ConsultarGridTipoEmpresaResponse
            {
                items = retorno,
                totalItems = lCount
            };
        }
        
        public TipoEmpresaResponse ConsultarPorId(int tipoEmpresaId)
        {
            return Mapper.Map<TipoEmpresaResponse>(Repository.Query
                .FirstOrDefault(a => a.Id == tipoEmpresaId));
        }
        
        public async Task<RespPadrao> Save(TipoEmpresaRequest lTipoEmpresaReq, bool integracao)
        {
            try
            {
                var command = Mapper.Map<TipoEmpresaSalvarComRetornoCommand>(lTipoEmpresaReq);
                var retorno = await Engine.CommandBus.SendCommandAsync<Domain.Models.TipoEmpresa.TipoEmpresa>(command);
        
                return new RespPadrao
                {
                    id = retorno.Id,
                    sucesso = true,
                    mensagem = "Registro salvo com sucesso!"
                };
            }
            catch (Exception e)
            {
                return new RespPadrao()
                {
                    sucesso = false,
                    mensagem = e.Message
                };
            }
        }
        
        public async Task<bool> AlterarStatus(TipoEmpresaStatusRequest lTipoEmpresaStatus)
        {
            var lTask = Engine.CommandBus.SendCommandAsync(Mapper.Map<TipoEmpresaAlterarStatusCommand>(lTipoEmpresaStatus));
            await lTask;
            return lTask.IsCompleted;
        }
    }
}
