﻿using System;
using System.Collections.Generic;

namespace SistemaInfo.BBC.Application.Objects.Mobile.Viagem.Response;

public class ConsultarViagensResponse
{
    public int Page { get; set; }
    public int Limit { get; set; }
    public int TotalPages { get; set; }
    public int TotalItems { get; set; }
    public List<ViagemResponseItem> Result { get; set; }
}

public class ViagemResponseItem
{
    public int ViagemId { get; set; }
    public string RazaoSocialEmpresa { get; set; } = string.Empty;
    public string CnpjEmpresa { get; set; } = string.Empty;
    public string Status { get; set; }
    public string FilialId { get; set; } = string.Empty;
    
    public string NomeProprietario { get; set; } = string.Empty;
    public string CpfCnpjProprietario { get; set; } = string.Empty;
    
    public string NomeMotorista { get; set; } = string.Empty;
    public string CpfCnpjMotorista { get; set; } = string.Empty;
    public DateTime DataCadastro { get; set; }
    public DateTime? DataAlteracao { get; set; }
    public long ViagemExternoId { get; set; }
    public DateTime? DataBaixa { get; set; }
    public string CidadeOrigem { get; set; } = string.Empty;
    public int? CidadeIbgeOrigem { get; set; }
    public string CidadeDestino { get; set; } = string.Empty;
    public int? CidadeIbgeDestino { get; set; }
    public string Ciot { get; set; } = string.Empty;
}
