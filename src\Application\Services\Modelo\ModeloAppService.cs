using System;
using System.Collections.Generic;
using System.Linq;
using System.Linq.Dynamic.Core;
using System.Threading.Tasks;
using AutoMapper.QueryableExtensions;
using SistemaInfo.BBC.Application.Interface.Modelo;
using SistemaInfo.BBC.Application.Objects.Base;
using SistemaInfo.BBC.Application.Objects.Web.Modelo;
using SistemaInfo.BBC.Domain.Grid;
using SistemaInfo.BBC.Domain.Helper;
using SistemaInfo.BBC.Domain.Models.Modelo.Commands;
using SistemaInfo.BBC.Domain.Models.Modelo.Repository;
using SistemaInfo.BBC.Domain.Models.Veiculo.Repository;
using SistemaInfo.Framework.DomainDrivenDesign.Application.Service;
using SistemaInfo.Framework.DomainDrivenDesign.Domain.Engine;
using SistemaInfo.Framework.Utils;

namespace SistemaInfo.BBC.Application.Services.Modelo
{
    public class ModeloAppService : AppService<Domain.Models.Modelo.Modelo,
        IModeloReadRepository, IModeloWriteRepository>, IModeloAppService
    {
        private readonly IVeiculoReadRepository _veiculoReadRepository;

        public ModeloAppService(
            IAppEngine engine,
            IVeiculoReadRepository veiculoReadRepository,
            IModeloReadRepository readRepository,
            IModeloWriteRepository writeRepository) : base(
            engine, readRepository, writeRepository)
        {
            _veiculoReadRepository = veiculoReadRepository;
        }

        public ConsultarGridModeloResponse ConsultarGridModelo(int take, int page, OrderFilters orderFilters,
            List<QueryFilters> filters)
        {
         
            IQueryable<Domain.Models.Modelo.Modelo> lModelo;

            lModelo = Repository.Query.GetAll();

            var filtrarUsuario = filters.Find(q => q.Campo == "FiltrarUsuario")?.Valor == "true";
            filters.Remove(filters.Find(q => q.Campo == "FiltrarUsuario"));

            if (filtrarUsuario)
            {
                var filialId = filters.Find(q => q.Campo == "FilialId").Valor.ToInt();
                var listaModelos = _veiculoReadRepository.Where(x => x.FilialId == filialId).Select(x => x.ModeloId);

                lModelo = lModelo.Where(x => listaModelos.Contains(x.Id));
            }

            lModelo = lModelo.AplicarFiltrosDinamicos(filters);
            lModelo = string.IsNullOrWhiteSpace(orderFilters?.Campo)
                ? lModelo.OrderByDescending(o => o.Id)
                : lModelo.OrderBy($"{orderFilters.Campo} {orderFilters.Operador.DescriptionAttr()}");

            var lCount = lModelo.Count();
            var retorno = lModelo.Skip((page - 1) * take)
                .Take(take).ProjectTo<ConsultarGridModelo>(Engine.Mapper.ConfigurationProvider).ToList();

            return new ConsultarGridModeloResponse
            {
                items = retorno,
                totalItems = lCount
            };
        }

        public ModeloResponse ConsultarPorId(int idModelo)
        {
            return Mapper.Map<ModeloResponse>(Repository.Query.ConsultarPorIdIncludeFabricante(idModelo));
        }

        
        public async Task<RespPadrao> Save(ModeloRequest lModeloReq, bool integracao)
        {
            try
            {
           
                var command = Mapper.Map<ModeloSalvarComRetornoCommand>(lModeloReq);
                var retorno = await Engine.CommandBus.SendCommandAsync<Domain.Models.Modelo.Modelo>(command);

                return new RespPadrao
                {
                    id = retorno.Id,
                    sucesso = true,
                    mensagem = "Registro salvo com sucesso!"
                };
            }
            catch (Exception e)
            {
                return new RespPadrao()
                {
                    sucesso = false,
                    mensagem = e.Message
                };
            }
        }

        public async Task AlterarStatus(ModeloStatusRequest lModeloStatus)
        {
            await Engine.CommandBus.SendCommandAsync(Mapper.Map<ModeloAlterarStatusCommand>(lModeloStatus));
        }
    }
}
