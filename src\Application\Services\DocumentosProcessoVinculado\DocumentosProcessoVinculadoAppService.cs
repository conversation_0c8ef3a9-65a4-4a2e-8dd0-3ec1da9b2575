using System;
using System.Collections.Generic;
using System.Linq;
using System.Linq.Dynamic.Core;
using System.Threading.Tasks;
using AutoMapper.QueryableExtensions;
using SistemaInfo.BBC.Application.Interface.DocumentosProcessoVinculado;
using SistemaInfo.BBC.Application.Objects.Base;
using SistemaInfo.BBC.Application.Objects.Web.DocumentosProcessoVinculado;
using SistemaInfo.BBC.Domain.Grid;
using SistemaInfo.BBC.Domain.Helper;
using SistemaInfo.BBC.Domain.Models.Documento.Repository;
using SistemaInfo.BBC.Domain.Models.DocumentosProcessoVinculado.Commands;
using SistemaInfo.BBC.Domain.Models.DocumentosProcessoVinculado.Repository;
using SistemaInfo.Framework.DomainDrivenDesign.Application.Service;
using SistemaInfo.Framework.DomainDrivenDesign.Domain.Engine;
using SistemaInfo.Framework.Utils;

namespace SistemaInfo.BBC.Application.Services.DocumentosProcessoVinculado
{
    public class DocumentosProcessoVinculadoAppService : AppService<Domain.Models.DocumentosProcessoVinculado.DocumentosProcessoVinculado, IDocumentosProcessoVinculadoReadRepository, IDocumentosProcessoVinculadoWriteRepository>,
        IDocumentosProcessoVinculadoAppService
    {
        private readonly IDocumentoReadRepository _documentoReadRepository;
            
        public DocumentosProcessoVinculadoAppService(
            IAppEngine engine,
            IDocumentosProcessoVinculadoReadRepository readRepository,
            IDocumentosProcessoVinculadoWriteRepository writeRepository,
            IDocumentoReadRepository documentoReadRepository)
            : base(engine, readRepository, writeRepository)
        {
            _documentoReadRepository = documentoReadRepository;
        }

        public ConsultarGridDocumentosProcessoVinculadoResponse ConsultarGridDocumentosProcessoVinculado(int take, int page, OrderFilters orderFilters,
            List<QueryFilters> filters)
        {
            var lDocumentosProcessoVinculado = Repository.Query.GetAll();

            var lCount = lDocumentosProcessoVinculado.Count();

            lDocumentosProcessoVinculado = lDocumentosProcessoVinculado.AplicarFiltrosDinamicos(filters);
            lDocumentosProcessoVinculado = string.IsNullOrWhiteSpace(orderFilters?.Campo)
                ? lDocumentosProcessoVinculado.OrderByDescending(o => o.Id)
                : lDocumentosProcessoVinculado.OrderBy($"{orderFilters.Campo} {orderFilters.Operador.DescriptionAttr()}");

            var retorno = lDocumentosProcessoVinculado.Skip((page - 1) * take)
                .Take(take)
                .ProjectTo<ConsultarGridDocumentosProcessoVinculado>(Engine.Mapper.ConfigurationProvider).ToList();

            return new ConsultarGridDocumentosProcessoVinculadoResponse
            {
                items = retorno,
                totalItems = lCount
            };
        }

        public DocumentosProcessoVinculadoResponse ConsultarPorId(int idReq)
        {
            var lDocumentoProcessoVinculado =  Mapper.Map<DocumentosProcessoVinculadoResponse>(Repository.Query
                .FirstOrDefault(a => a.Id == idReq));

            lDocumentoProcessoVinculado.NaoEditavel = _documentoReadRepository
                .Where(x => x.DocumentosProcessoVinculadoId == lDocumentoProcessoVinculado.Id).Any();

            return lDocumentoProcessoVinculado;
        }

        public async Task<RespPadrao> Save(DocumentosProcessoVinculadoRequest request)
        {
            try
            {
                #region Validar

                if (request.Documento.IsNullOrWhiteSpace())
                {
                    return new RespPadrao
                    {
                        sucesso = false,
                        mensagem = "Nome do documento deve ser informado."
                    };
                }
                
                char[] caracteresIndesejados = { '<', '>', '$', '\'' };
                bool contemCaracterIndesejado = false;

                foreach (char caractere in request.Documento)
                {
                    if (Array.IndexOf(caracteresIndesejados, caractere) != -1)
                    {
                        contemCaracterIndesejado = true;
                        break;
                    }
                }
                
                if (contemCaracterIndesejado)
                {
                    return new RespPadrao
                    {
                        sucesso = false,
                        mensagem = "O nome do documento contém caracteres indesejados."
                    };
                }

                // validar duplicidade de nome de documento por processo vinculado
                var lVinculoDuplicado = Repository.Query
                    .GetOthersByIdAndDocumentoAndProcessoVinculadoId(request.Id, request.Documento, request.ProcessoVinculadoId);

                if (lVinculoDuplicado.Any())
                {
                    return new RespPadrao
                    {
                        sucesso = false,
                        mensagem = "Vinculo entre processo e documento já cadastrado!"
                    };
                }
                
                // montar command
                var command = Mapper.Map<DocumentosProcessoVinculadoSalvarComRetornoCommand>(request);
                
                //Validar se DocumentoProcessoVinculado esta sendo utilizado
                command.Utilizado = request.Id > 0 && _documentoReadRepository
                    .Where(x => x.DocumentosProcessoVinculadoId == request.Id).Any();
                
                #endregion
                
                var retorno = await Engine.CommandBus.SendCommandAsync<Domain.Models.DocumentosProcessoVinculado.DocumentosProcessoVinculado>(command);

                return new RespPadrao
                {
                    sucesso = true,
                    mensagem = "Registro de vinculo entre documento " + retorno.Documento + " e processo " + retorno.ProcessoVinculadoId.GetDescription() + " salvo com sucesso!"
                };
            }
            catch (Exception e)
            {
                return new RespPadrao()
                {
                    sucesso = false,
                    mensagem = e.Message
                };
            }
        }

        public async Task AlterarStatus(DocumentosProcessoVinculadoStatusRequest id)
        {
            await Engine.CommandBus.SendCommandAsync(Mapper.Map<DocumentosProcessoVinculadoAlterarStatusCommand>(id));
        }
        
        public List<DocumentosProcessoVinculadoAnexarResponse> ConsultarDocumentosProcessoVinculado(int processoVinculado)
        {
            var consultaDocumentos =  Repository.Query
                .Where(x => x.ProcessoVinculadoId.GetHashCode() == processoVinculado && x.Ativo == 1);

            var lDocumentosVinculados = Mapper.Map<List<DocumentosProcessoVinculadoAnexarResponse>>(consultaDocumentos);

            return lDocumentosVinculados;
        }
    }
}