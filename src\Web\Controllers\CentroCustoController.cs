using System;
using Microsoft.AspNetCore.Mvc;
using SistemaInfo.BBC.Application.Interface.CentroCusto;
using SistemaInfo.BBC.Application.Objects.Base;
using SistemaInfo.BBC.Application.Objects.Web.CentroCusto;
using SistemaInfo.BBC.Domain.Enum;
using SistemaInfo.BBC.Web.Attributes;
using SistemaInfo.BBC.Web.Controllers.Base;
using SistemaInfo.Framework.DomainDrivenDesign.Domain.Engine;

namespace SistemaInfo.BBC.Web.Controllers
{
    /// <summary>
    ///
    /// 
    /// </summary>
    [Route("CentroCusto")]
    public class CentroCustoController : WebControllerBase<ICentroCustoAppService>
    {
        /// <summary>
        /// 
        /// </summary>
        /// <param name="engine"></param>
        /// <param name="appService"></param>
        public CentroCustoController(IAppEngine engine, ICentroCustoAppService appService) : base(engine, appService)
        {
        }

        /// <summary>
        /// 
        /// </summary>
        /// <param name="lCentroCustoStatus"></param>
        /// <returns></returns>
        [HttpPost("AlterarStatus")]
        [Menu(new[] { EMenus.CentroCusto })]
        public JsonResult AlterarStatus([FromBody]CentroCustoStatusRequest lCentroCustoStatus)
        {
            try
            {
                AppService.AlterarStatus(lCentroCustoStatus);
                return ResponseBase.ResponderSucesso(null);
            }
            catch (Exception e)
            {
                return  ResponseBase.ResponderErro("Não foi possível realizar a operação. Mensagem: " + e.Message);
            }
        }
        
        /// <summary>
        /// 
        /// </summary>
        /// <param name="request"></param>
        /// <returns></returns>
        [HttpPost("ConsultarGridCentroCusto")]
        [Menu(new[] { EMenus.CentroCusto, EMenus.Filial, EMenus.Portador, EMenus.Usuario, EMenus.Veiculo })]
        public JsonResult ConsultarGridCentroCusto([FromBody]BaseGridRequest request)
        {
            try
            {
                var consultarGridCentroCusto = AppService.ConsultarGridCentroCusto(request.Take, request.Page, request.Order, request.Filters);
                return ResponseBase.ResponderSucesso(consultarGridCentroCusto);
            }
            catch (Exception e)
            {
                return ResponseBase.ResponderErro(e);

            }
        }

        /// <summary>
        /// 
        /// </summary>
        /// <param name="lCentroCustoReq"></param>
        /// <returns></returns>
        [HttpPost("Salvar")]
        [Menu(new[] { EMenus.CentroCusto })]
        public JsonResult SaveCentroCusto([FromBody]CentroCustoRequest lCentroCustoReq)
        {
            try
            {
                var lSaveCentroCusto = AppService.Save(lCentroCustoReq).Result;
                
                return ResponseBase.BigJson(lSaveCentroCusto);
            }
            catch (Exception)
            {
                return ResponseBase.ResponderErro("Não foi possível realizar a operação.");
            }
        }
        
        /// <summary>
        /// 
        /// </summary>
        /// <param name="idCentroCusto"></param>
        /// <returns></returns>
        [HttpGet("ConsultarPorId")]
        [Menu(new[] { EMenus.CentroCusto })]
        public JsonResult ConsultarPorId(int idCentroCusto)
        {
            try
            {
                var consultarCentroCusto = AppService.ConsultarPorId(idCentroCusto);
                return ResponseBase.ResponderSucesso(consultarCentroCusto);
            }
            catch (Exception)
            {
                return ResponseBase.ResponderErro("Centro de custo não encontrado!");
            }
        }
        
        /// <summary>
        /// 
        /// </summary>
        /// <param name="lCentroCustoAlterarVinculo"></param>
        /// <returns></returns>
        [HttpPost("AlterarVinculoFilial")]
        [Menu(new[] { EMenus.Filial })]
        public JsonResult AlterarVinculoFilial([FromBody]CentroCustoAlterarVinculoFilialRequest lCentroCustoAlterarVinculo)
        {
            try
            {
                var lAlterarVinculo=  AppService.AlterarVinculoFilial(lCentroCustoAlterarVinculo);

                if (lAlterarVinculo.Result.sucesso)
                {
                    return ResponseBase.ResponderSucesso(lAlterarVinculo);
                }
                else
                {
                    return ResponseBase.Responder(
                        false,
                        lAlterarVinculo.Result.mensagem,
                        lCentroCustoAlterarVinculo.id);
                }
            }
            catch (Exception)
            {
                return  ResponseBase.ResponderErro("Não foi possível realizar a operação.");
            }
        }
    }
}