﻿using System;
using System.Collections.Generic;
using System.Linq;
using System.Linq.Dynamic.Core;
using System.Threading.Tasks;
using AutoMapper.QueryableExtensions;
using NLog;
using SistemaInfo.BBC.Application.Interface.BloqueioSpd;
using SistemaInfo.BBC.Application.Objects.Base;
using SistemaInfo.BBC.Domain.Grid;
using SistemaInfo.BBC.Application.Objects.Web.BloqueioSpd;
using SistemaInfo.BBC.Domain.Helper;
using SistemaInfo.BBC.Domain.Models.BloqueioSpd.Commands;
using SistemaInfo.BBC.Domain.Models.BloqueioSpd.Repository;
using SistemaInfo.Framework.DomainDrivenDesign.Application.Service;
using SistemaInfo.Framework.DomainDrivenDesign.Domain.Engine;

namespace SistemaInfo.BBC.Application.Services.BloqueioSpd
{
    public class BloqueioSpdAppService : AppService<Domain.Models.BloqueioSpd.BloqueioSpd, IBloqueioSpdReadRepository, IBloqueioSpdWriteRepository>, IBloqueioSpdAppService
    {
        public BloqueioSpdAppService(IAppEngine engine, IBloqueioSpdReadRepository readRepository, IBloqueioSpdWriteRepository writeRepository) 
            : base(engine, readRepository, writeRepository)
        {
        }
        public ConsultarGridBloqueioSpdResponse ConsultarGridBloqueioSpd(int take, int page, OrderFilters orderFilters, List<QueryFilters> filters)
        {
            var lBloqueioSpd = Repository.Query.GetAll();

            if (User.EmpresaId > 0)
            {
                lBloqueioSpd = Repository.Query.GetAll().Where(f => f.EmpresaId == User.EmpresaId);
            }

            var lCount = lBloqueioSpd.Count();
            
            lBloqueioSpd = lBloqueioSpd.AplicarFiltrosDinamicos(filters);
            lBloqueioSpd = string.IsNullOrWhiteSpace(orderFilters?.Campo)
                ? lBloqueioSpd.OrderByDescending(o => o.Id)
                : lBloqueioSpd.OrderBy($"{orderFilters.Campo} {orderFilters.Operador.DescriptionAttr()}");

            var retorno = lBloqueioSpd.Skip((page - 1) * take)
                .Take(take)
                .ProjectTo<ConsultarGridBloqueioSpd>(Engine.Mapper.ConfigurationProvider).ToList();
            
            return new ConsultarGridBloqueioSpdResponse
            {
                items = retorno,
                totalItems = lCount
            };
        }
        public BloqueioSpdResponse ConsultarPorId(int idBloqueioSpd)
        {
            return Mapper.Map<BloqueioSpdResponse>(Repository.Query.GetByIdIncludeEmpresa(idBloqueioSpd));
        }
        public async Task<RespPadrao> AlterarStatus(BloqueioSpdStatusRequest lBloqueioSpdStatus)
        {
            try
            {
                var bloqueio = Mapper.Map<BloqueioSpdAlterarStatusCommandComRentorno>(lBloqueioSpdStatus);
                var result = await Engine.CommandBus.SendCommandAsync<Domain.Models.BloqueioSpd.BloqueioSpd>(bloqueio);
                var mensagem = $"Liberação de bloqueio SPD {(result.Ativo == 0 ? "inativada" : "reativada")} com sucesso.";
                
                return new RespPadrao()
                {
                    sucesso = true,
                    mensagem = mensagem
                };
            }
            catch (Exception e)
            {
                LogManager.GetCurrentClassLogger().Error(e);
                return new RespPadrao()
                {
                    sucesso = false,
                    mensagem = "Ocorreu um erro na liberação de bloqueio SPD. " + e.Message
                };
            }
        }
        
        public  async Task<RespPadrao> Save(BloqueioSpdRequest lBloqueioSpdReq)
        {
            try
            {
                var lBloqueioSpd = Mapper.Map<BloqueioSpdSaveComRetornoCommand>(lBloqueioSpdReq);
                var bloqueioSpdValidation = Mapper.Map<Domain.Models.BloqueioSpd.BloqueioSpd>(lBloqueioSpd);
                bloqueioSpdValidation.ValidarCriacao();
                
                var result = await Engine.CommandBus.SendCommandAsync<Domain.Models.BloqueioSpd.BloqueioSpd>(lBloqueioSpd);
                
                return new RespPadrao()
                {
                    id = result.Id,
                    sucesso = true,
                    mensagem = ""
                };
            }
            catch (Exception e)
            {
                return new RespPadrao()
                {
                    sucesso = false,
                    mensagem = e.Message
                };
            }
        }
    }
}