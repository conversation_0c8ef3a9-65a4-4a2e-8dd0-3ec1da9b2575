﻿using System;
using System.Collections.Generic;
using System.Linq;
using System.Linq.Dynamic.Core;
using System.Threading.Tasks;
using AutoMapper.QueryableExtensions;
using SistemaInfo.BBC.Application.Interface.VeiculoCombustivel;
using SistemaInfo.BBC.Application.Objects.Base;
using SistemaInfo.BBC.Application.Objects.Web.VeiculoCombustivel;
using SistemaInfo.BBC.Domain.Grid;
using SistemaInfo.BBC.Domain.Helper;
using SistemaInfo.BBC.Domain.Models.VeiculoCombustivel.Commands;
using SistemaInfo.BBC.Domain.Models.VeiculoCombustivel.Repository;
using SistemaInfo.Framework.DomainDrivenDesign.Application.Service;
using SistemaInfo.Framework.DomainDrivenDesign.Domain.Engine;

namespace SistemaInfo.BBC.Application.Services.VeiculoCombustivel
{
    public class VeiculoCombustivelAppService : AppService<Domain.Models.VeiculoCombustivel.VeiculoCombustivel,
        IVeiculoCombustivelReadRepository, IVeiculoCombustivelWriteRepository>, IVeiculoCombustivelAppService
    {

        public VeiculoCombustivelAppService(
            IAppEngine engine,
            IVeiculoCombustivelReadRepository readRepository,
            IVeiculoCombustivelWriteRepository writeRepository) : base(
            engine, readRepository, writeRepository)
        {
        }

        public ConsultarGridVeiculoCombustivelResponse ConsultarGridVeiculoCombustivel(int take, int page, OrderFilters orderFilters,
            List<QueryFilters> filters)
        {
            var lVeiculoCombustivel = Repository.Query.GetAll();

            lVeiculoCombustivel = lVeiculoCombustivel.AplicarFiltrosDinamicos(filters);
            lVeiculoCombustivel = string.IsNullOrWhiteSpace(orderFilters?.Campo)
                ? lVeiculoCombustivel.OrderByDescending(o => o.Id)
                : lVeiculoCombustivel.OrderBy($"{orderFilters.Campo} {orderFilters.Operador.DescriptionAttr()}");

            var lCount = lVeiculoCombustivel.Count();
            
            var retorno = lVeiculoCombustivel
                .Skip((page - 1) * take)
                .Take(take)
                .ProjectTo<ConsultarVeiculoCombustivelGrid>(Engine.Mapper.ConfigurationProvider)
                .ToList();

            return new ConsultarGridVeiculoCombustivelResponse
            {
                items = retorno,
                totalItems = lCount
            };
        }

        public VeiculoCombustivelResponse ConsultarPorId(int idVeiculoCombustivel)
        {
            return Mapper.Map<VeiculoCombustivelResponse>(Repository.Query.GetById(idVeiculoCombustivel));
        }

        
        public async Task<RespPadrao> Save(VeiculoCombustivelRequest lVeiculoCombustivelReq, bool integracao)
        {
            try
            {
           
                var command = Mapper.Map<VeiculoCombustivelSalvarComRetornoCommand>(lVeiculoCombustivelReq);
                var retorno = await Engine.CommandBus.SendCommandAsync<Domain.Models.VeiculoCombustivel.VeiculoCombustivel>(command);

                return new RespPadrao
                {
                    id = retorno.Id,
                    sucesso = true,
                    mensagem = "Registro salvo com sucesso!"
                };
            }
            catch (Exception e)
            {
                return new RespPadrao()
                {
                    sucesso = false,
                    mensagem = e.Message
                };
            }
        }
    }
}
