﻿using SistemaInfo.BBC.Domain.Contracts.Operacoes;
using SistemaInfo.BBC.Domain.External.CIOT.DTO;

namespace SistemaInfo.BBC.Application.AutoMapper.OperacoesMapper;

public class OperacoesMappingProfile : SistemaInfoMappingProfile
{
    public OperacoesMappingProfile()
    {
        #region Request

        CreateMap<ConsultarFrotaTransportadorReq, ConsultarFrotaTransportadorReqMessage>();
        
        CreateMap<DeclararOperacaoTransporteReq, DeclararOperacaoTransporteReqMessage>();
        
        CreateMap<EncerrarOperacaoTransporteReq, EncerrarOperacaoTransporteReqMessage>();
        
        CreateMap<CancelarOperacaoTransporteReq, CancelarOperacaoTransporteReqMessage>()
            .ForMember(dest => dest.CpfCnpjCliente, opts => 
                opts.MapFrom(s => s.CpfCnpjClienteAdmOrCompanyGroup));
        
        CreateMap<VeiculoRetificar, Veiculo>();
        
        CreateMap<RetificarOperacaoTransporteReq, RetificarOperacaoTransporteReqMessage>()
            .ForMember(dest => dest.Veiculos, opt => opt.MapFrom(src => src.Veiculos.ToArray()));;

        CreateMap<ConsultarSituacaoCiotReq, ConsultarSituacaoCiotReqMessage>();
        
        CreateMap<ConsultarSituacaoTransportadorReq, ConsultarSituacaoTransportadorReqMessage>();
        
        CreateMap<ConsultarOperacaoTacAgregadoReq, ConsultarOperacaoTacAgregadoReqMessage>();

        #endregion
        
        #region Response

        CreateMap<ConsultarFrotaTransportadorRespMessage ,ConsultarFrotaTransportadorResp >()
            .ForPath(x => x.Excecao, opts => opts.MapFrom(src => src.Erro));
        
        CreateMap<DeclararOperacaoTransporteRespMessage ,DeclararOperacaoTransporteResp >()
            .ForPath(x => x.Excecao, opts => opts.MapFrom(src => src.Erro));
        
        CreateMap<EncerrarOperacaoTransporteRespMessage ,EncerrarOperacaoTransporteResp >()
            .ForMember(c => c.Excecao, opts => opts.MapFrom(x => x.Erro));
        
        CreateMap<CancelarOperacaoTransporteRespMessage ,CancelarOperacaoTransporteResp >()
            .ForMember(c => c.Excecao, opts => opts.MapFrom(x => x.Erro));

        CreateMap<RetificarOperacaoTransporteRespMessage, RetificarOperacaoTransporteResp>()
            .ForMember(c => c.Excecao, opts => opts.MapFrom(x => x.Erro));
        
        CreateMap<ConsultarSituacaoCiotRespMessage ,ConsultarSituacaoCiotResp >()
            .ForMember(c => c.Excecao, opts => opts.MapFrom(x => x.Erro));
        
        CreateMap<ConsultarSituacaoTransportadorRespMessage ,ConsultarSituacaoTransportadorResp >()
            .ForMember(c => c.Excecao, opts => opts.MapFrom(x => x.Erro))
            ;
        CreateMap<ConsultarOperacaoTacAgregadoRespMessage ,ConsultarOperacaoTacAgregadoResp >()
            .ForMember(c => c.Excecao, opts => opts.MapFrom(x => x.Erro));

        #endregion
    }
}