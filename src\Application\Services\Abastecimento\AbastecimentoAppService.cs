using System;
using System.Collections.Generic;
using System.Globalization;
using System.Linq;
using System.Linq.Dynamic.Core;
using System.Threading.Tasks;
using AutoMapper.QueryableExtensions;
using Microsoft.EntityFrameworkCore;
using Newtonsoft.Json;
using NLog;
using SistemaInfo.BBC.Application.Email.Abastecimento;
using SistemaInfo.BBC.Application.Interface.Abastecimento;
using SistemaInfo.BBC.Application.Interface.AutorizacaoAbastecimento;
using SistemaInfo.BBC.Application.Interface.AutorizacaoContingecia;
using SistemaInfo.BBC.Application.Interface.CentralPendencias;
using SistemaInfo.BBC.Application.Interface.Veiculo;
using SistemaInfo.BBC.Application.Interface.PagamentoAbastecimento;
using SistemaInfo.BBC.Application.Objects.Base;
using SistemaInfo.BBC.Application.Objects.Mobile.Abastecimento;
using SistemaInfo.BBC.Application.Objects.Mobile.Veiculo;
using SistemaInfo.BBC.Application.Objects.Web.Abastecimento;
using SistemaInfo.BBC.Application.Objects.Web.AutorizacaoContingecia;
using SistemaInfo.BBC.Application.Objects.Web.ManutencaoAbastecimento;
using SistemaInfo.BBC.Domain.Components.Email;
using SistemaInfo.BBC.Domain.Enum;
using SistemaInfo.BBC.Domain.External.Mobile2You.DTO;
using SistemaInfo.BBC.Domain.External.Mobile2You.Interface;
using SistemaInfo.BBC.Domain.Grid;
using SistemaInfo.BBC.Domain.Helper;
using SistemaInfo.BBC.Domain.Models.Abastecimento.Commands;
using SistemaInfo.BBC.Domain.Models.Abastecimento.Repository;
using SistemaInfo.BBC.Domain.Models.AutorizacaoAbastecimento.Commands;
using SistemaInfo.BBC.Domain.Models.AutorizacaoAbastecimento.Repository;
using SistemaInfo.BBC.Domain.Models.Empresa.Repository;
using SistemaInfo.BBC.Domain.Models.Filial.Repository;
using SistemaInfo.BBC.Domain.Models.Portador.Repository;
using SistemaInfo.BBC.Domain.Models.PortadorCentroCusto.Repository;
using SistemaInfo.BBC.Domain.Models.PortadorEmpresa.Repository;
using SistemaInfo.BBC.Domain.Models.Posto.Repository;
using SistemaInfo.BBC.Domain.Models.PostoCombustivel.Repository;
using SistemaInfo.BBC.Domain.Models.Veiculo.Commands;
using SistemaInfo.BBC.Domain.Models.Veiculo.Repository;
using SistemaInfo.BBC.Domain.Models.VeiculoCombustivel.Repository;
using SistemaInfo.Framework.DomainDrivenDesign.Application.Service;
using SistemaInfo.Framework.DomainDrivenDesign.Domain.Engine;
using SistemaInfo.Framework.DomainDrivenDesign.Infra.DataTransferObjects;
using SistemaInfo.Framework.Utils;

namespace SistemaInfo.BBC.Application.Services.Abastecimento
{
    public class AbastecimentoAppService : AppService<Domain.Models.Abastecimento.Abastecimento,
        IAbastecimentoReadRepository, IAbastecimentoWriteRepository>, IAbastecimentoAppService
    {
        private readonly IPortadorReadRepository _portadorReadRepository;
        private readonly IEmpresaReadRepository _empresaReadRepository;
        private readonly IAutorizacaoAbastecimentoReadRepository _autorizacaoAbastecimentoReadRepository;
        private readonly IAutorizacaoAbastecimentoAppService _autorizacaoAbastecimentoAppService;
        private readonly IPortadorEmpresaReadRepository _portadorEmpresaReadRepository;
        private readonly IPostoCombustivelReadRepository _postoCombustivelReadRepository;
        private readonly IPostoReadRepository _postoReadRepository;
        private readonly IVeiculoReadRepository _veiculoReadRepository;
        private readonly IVeiculoAppService _veiculoAppService;
        private readonly IVeiculoCombustivelReadRepository _veiculoCombustivelReadRepository;
        private readonly IPortadorCentroCustoReadRepository _portadorCentroCustoReadRepository;
        private readonly IFilialReadRepository _filialReadRepository;
        private readonly IAbastecimentoReadRepository _abastecimentoReadRepository;
        private readonly IPagamentoAbastecimentoAppService _pagamentoAbastecimentoAppService;
        private readonly IAutorizacaoContingeciaAppService _autorizacaoContingeciaAppService;
        private readonly IMobile2YouRepository _mobile2YouRepository;
        private readonly INotificationEmailExecutor _emailExecutor;
        private readonly ICentralPendenciasAppService _centralPendenciasAppService;

        public AbastecimentoAppService(
            IAppEngine engine,
            IAbastecimentoReadRepository readRepository,
            IPortadorReadRepository portadorReadRepository,
            IEmpresaReadRepository empresaReadRepository,
            IAutorizacaoAbastecimentoReadRepository autorizacaoAbastecimentoReadRepository,
            IAutorizacaoAbastecimentoAppService autorizacaoAbastecimentoAppService,
            IPortadorEmpresaReadRepository portadorEmpresaReadRepository,
            IPostoCombustivelReadRepository postoCombustivelReadRepository,
            IVeiculoReadRepository veiculoReadRepository,
            IVeiculoAppService veiculoAppService,
            IVeiculoCombustivelReadRepository veiculoCombustivelReadRepository,
            IPortadorCentroCustoReadRepository portadorCentroCustoReadRepository,
            IAbastecimentoWriteRepository writeRepository,
            IFilialReadRepository filialReadRepository,
            IAbastecimentoReadRepository abastecimentoReadRepository,
            IAutorizacaoContingeciaAppService autorizacaoContingeciaAppService,
            IPagamentoAbastecimentoAppService pagamentoAbastecimentoAppService,
            IMobile2YouRepository mobile2YouRepository,
            IPostoReadRepository postoReadRepository, 
            INotificationEmailExecutor emailExecutor, ICentralPendenciasAppService centralPendenciasAppService) : base(
            engine, readRepository, writeRepository)
        {
            _portadorReadRepository = portadorReadRepository;
            _empresaReadRepository = empresaReadRepository;
            _autorizacaoAbastecimentoReadRepository = autorizacaoAbastecimentoReadRepository;
            _autorizacaoAbastecimentoAppService = autorizacaoAbastecimentoAppService;
            _portadorEmpresaReadRepository = portadorEmpresaReadRepository;
            _postoCombustivelReadRepository = postoCombustivelReadRepository;
            _portadorCentroCustoReadRepository = portadorCentroCustoReadRepository;
            _veiculoReadRepository = veiculoReadRepository;
            _veiculoAppService = veiculoAppService;
            _veiculoCombustivelReadRepository = veiculoCombustivelReadRepository;
            _filialReadRepository = filialReadRepository;
            _abastecimentoReadRepository = abastecimentoReadRepository;
            _pagamentoAbastecimentoAppService = pagamentoAbastecimentoAppService;
            _autorizacaoContingeciaAppService = autorizacaoContingeciaAppService;
            _mobile2YouRepository = mobile2YouRepository;
            _postoReadRepository = postoReadRepository;
            _emailExecutor = emailExecutor;
            _centralPendenciasAppService = centralPendenciasAppService;
        }

        public ConsultarGridAbastecimentoResponse ConsultarGridAbastecimento(int idPosto, int take, int page, OrderFilters orderFilters,
            List<QueryFilters> filters, DateTime? dataInicial = null, DateTime? dataFinal = null)
        {
            IQueryable<Domain.Models.Abastecimento.Abastecimento> lAbastecimento;

            lAbastecimento = Repository.Query.GetAll();
            
            if (idPosto > 0)
                lAbastecimento = lAbastecimento.Where(x => x.PostoId == idPosto);

            if (dataInicial.HasValue)
                lAbastecimento = lAbastecimento.Where(x => x.DataCadastro >= dataInicial);
            
            if (dataInicial.HasValue)
                lAbastecimento = lAbastecimento.Where(x => x.DataCadastro <= dataFinal);

            lAbastecimento = lAbastecimento.AplicarFiltrosDinamicos(filters);
            lAbastecimento = string.IsNullOrWhiteSpace(orderFilters?.Campo)
                ? lAbastecimento.OrderByDescending(o => o.Id)
                : lAbastecimento.OrderBy($"{orderFilters.Campo} {orderFilters.Operador.DescriptionAttr()}");

            var lCount = lAbastecimento.Count();
            var retorno = lAbastecimento.Skip((page - 1) * take)
                .Take(take).ProjectTo<ConsultarGridAbastecimento>(Engine.Mapper.ConfigurationProvider).ToList();

            return new ConsultarGridAbastecimentoResponse
            {
                items = retorno,
                totalItems = lCount
            };
        }

        public AbastecimentoResponse ConsultarPorId(int idAbastecimento, string portadorCpfCnpj)
        {
            return Mapper.Map<AbastecimentoResponse>(Repository.Query.GetAbastecimentosPortador(idAbastecimento, portadorCpfCnpj).FirstOrDefault());
        }

        public List<AbastecimentosResponse> ConsultarAbastecimentosPorPortadorId(DateTime dtIni, DateTime dtFim, int idPortador)
        {
            
            var abastecimentos =  Repository.Query.GetAbastecimentosPortadorId(idPortador)
                .Include(p => p.Portador)
                .Include(v => v.Veiculo)
                .Include(c => c.Combustivel)
                .Include(p => p.Posto)
                .Where(a => a.DataCadastro >= dtIni && a.DataCadastro <= dtFim)
                .ProjectTo<AbastecimentosResponse>(Engine.Mapper.ConfigurationProvider)
                .OrderByDescending(x=>x.Id)
                .Take(10)
                .ToList();
            
            return abastecimentos;
        }
        
        public async Task<RespPadrao> Save(AbastecimentoRequest lAbastecimentoReq, bool integracao)
        {
            try 
            {
                #region dados para validacao

                var veiculoValidador = _veiculoReadRepository.Where(x => x.Id == lAbastecimentoReq.VeiculoId)
                    .Include(x => x.Empresa)
                    .Include(x => x.Portador)
                    .Include(x => x.CentroCusto)
                    .FirstOrDefault();

                var lPortador = _portadorReadRepository.FirstOrDefault(x => x.Id == lAbastecimentoReq.PortadorId);

                var portadorEmpresaValidador =
                    _empresaReadRepository.FirstOrDefault(x => x.Id == lPortador.EmpresaIdFrota);

                #endregion
                
                #region Validacoes
                
                if (portadorEmpresaValidador == null)
                {
                    return new RespPadrao
                    {
                        sucesso = false,
                        mensagem = "Sem permissão para o veículo apresentado."
                    };
                }
                
                //validar autorização de abastecimento
                var autorizacaoAbastecimento = _autorizacaoAbastecimentoReadRepository.Where(x =>
                    x.VeiculoId == lAbastecimentoReq.VeiculoId
                    && x.CombustivelId == lAbastecimentoReq.CombustivelId
                    && x.Status == StatusAutorizacaoAbastecimento.Aberto
                    && (x.LitragemUtilizada <= 0 && x.Metodo == 2
                        || x.Metodo == 1)).FirstOrDefault();
                
                if (autorizacaoAbastecimento == null)
                {
                    return new RespPadrao
                    {
                        sucesso = false,
                        mensagem = "Veículo não possui autorização de abastecimento."
                    };
                }
                
                if (lAbastecimentoReq.Litragem > autorizacaoAbastecimento.Litragem ||
                         lAbastecimentoReq.Litragem > autorizacaoAbastecimento.Litragem - autorizacaoAbastecimento.LitragemUtilizada)
                {
                    return new RespPadrao
                    {
                        sucesso = false,
                        mensagem = "Verificar autorização de abastecimento."
                    };
                } 
                
                if (autorizacaoAbastecimento.LitragemUtilizada > 0 && autorizacaoAbastecimento.Metodo == 2)
                {
                    return new RespPadrao
                    {
                        sucesso = false,
                        mensagem = "Autorização já utilizada."
                    };
                } 
                
                //verificador tempo entre abastecimentos
                if (portadorEmpresaValidador.TempoAbastecimento > 0)
                {
                    var ultimoAbastecimento = Repository.Query.Where(x => x.VeiculoId == veiculoValidador.Id && 
                                                                          x.CombustivelId == lAbastecimentoReq.CombustivelId)
                        .OrderByDescending(x=>x.Id).FirstOrDefault();

                    if (ultimoAbastecimento != null)
                    {
                        if (ultimoAbastecimento.DataCadastro.AddMinutes(portadorEmpresaValidador.TempoAbastecimento.GetValueOrDefault()) >=
                             DateTime.Now &&
                            ultimoAbastecimento.CombustivelId == lAbastecimentoReq.CombustivelId)
                        {
                            return new RespPadrao
                            {
                                sucesso = false,
                                mensagem = "Intervalo entre abastecimento, não é permitido realizar a operação!"
                            };
                        } 
                    }
                }

                //verifica capacidade tanque
                var combustivelVeiculoAbst = _veiculoCombustivelReadRepository.Where(x =>
                        x.VeiculoId == lAbastecimentoReq.VeiculoId &&
                        x.CombustivelId == lAbastecimentoReq.CombustivelId)
                    .FirstOrDefault();

                if (lAbastecimentoReq.Litragem > combustivelVeiculoAbst?.Capacidade)
                {
                    return new RespPadrao
                    {
                        sucesso = false,
                        mensagem = "Capacidade do tanque excedida."
                    };
                }
                
                // verificar autonomia se habilitado
                // (Sprint 13 recurso não entrou na sprint aprimorar em futuras sprints)
                 if (portadorEmpresaValidador.ControlaAutonomia != 0 && 
                     portadorEmpresaValidador.ControlaOdometro != 0 &&
                     veiculoValidador?.ControlaAutonomia != 0)
                 {
                     if (lAbastecimentoReq.OdometroAtual == 0)
                     {
                         return new RespPadrao
                         {
                             sucesso = false,
                             mensagem = "Odômetro não informado."
                         }; 
                     }

                     var autonomiaLitrosVeiculo = combustivelVeiculoAbst.Autonomia / combustivelVeiculoAbst.Capacidade;
                     
                     var mediaOdometro = lAbastecimentoReq.OdometroAtual - lAbastecimentoReq.OdometroAnterior;

                     var percentualAutonomiaInferior = portadorEmpresaValidador.PercentualAutonomiaInferior ?? 0;
                     var percentualAutonimiaSuperior = portadorEmpresaValidador.PercentualAutonomiaSuperior ?? 0;

                     #region autonomiaLitros

                     //Manter trecho de código para futuramente salvar informações no BD
                     //Para que seja possível montar DashBoards
                     var autonomiaLitrosRodados = mediaOdometro / lAbastecimentoReq.Litragem;
                     
                     var autonomiaLitrosMinima = autonomiaLitrosVeiculo * (1 - (percentualAutonomiaInferior/ 100));

                     if (percentualAutonomiaInferior < 97)
                     {
                         autonomiaLitrosMinima = autonomiaLitrosMinima * (1 - 0.03).ToDecimalSafe();
                     }

                     var autonomiaLitrosMaxima = autonomiaLitrosVeiculo * (1 + (percentualAutonimiaSuperior/ 100));

                     if (percentualAutonimiaSuperior < 97)
                     {
                         autonomiaLitrosMaxima = autonomiaLitrosMaxima * (1 + 0.03).ToDecimalSafe();
                     }

                     #endregion

                     var autonomiaKmMinima = (combustivelVeiculoAbst.Autonomia * (1 - (percentualAutonomiaInferior/ 100))).ToDecimalSafe();
                     
                     if (percentualAutonomiaInferior < 97)
                     {
                         autonomiaKmMinima = autonomiaKmMinima * (1 - 0.03).ToDecimalSafe();
                     }

                     var autonomiaKmMaxima = (combustivelVeiculoAbst.Autonomia * (1 + (percentualAutonimiaSuperior/ 100))).ToDecimalSafe();
                     
                     if (percentualAutonimiaSuperior < 97)
                     {
                         autonomiaKmMaxima = autonomiaKmMaxima * (1 + 0.03).ToDecimalSafe();
                     }

                     if (mediaOdometro >  autonomiaKmMaxima || mediaOdometro < autonomiaKmMinima)
                     {
                         return new RespPadrao()
                         {
                             sucesso = false,
                             mensagem = "Verificar autonomia do veículo."
                         };
                     }
                 }
                
                #endregion

                #region Save do abastecimento

                var command = Mapper.Map<AbastecimentoSalvarComRetornoCommand>(lAbastecimentoReq);
                                
                var lVeiculo = _veiculoReadRepository.Where(x => x.Id == command.VeiculoId)
                    .Include(x=>x.Filial)
                    .Include(x=>x.Empresa)
                    .Include(x=>x.Modelo)
                    .FirstOrDefault();
                
                if (lVeiculo?.Empresa?.ControlaContingencia == 0)
                {
                    command.Status = EStatusAbastecimento.Aprovado;
                }
                
                if (lVeiculo?.Filial != null)
                {

                    #region Validação de horário limite de abastecimento

                    var horarioLimiteAbastecimentoInicio = lVeiculo.Filial.HorarioLimiteAbastecimentoInicio;
                    var horarioLimiteAbastecimentoFim = lVeiculo.Filial.HorarioLimiteAbastecimentoFim;

                    if (horarioLimiteAbastecimentoInicio != null && horarioLimiteAbastecimentoFim != null)
                    {
                        var horarioInicioDateTime = DateTime.Parse(horarioLimiteAbastecimentoInicio);
                        var horarioFimDateTime = DateTime.Parse(horarioLimiteAbastecimentoFim);

                        if (DateTime.Compare(horarioFimDateTime, horarioInicioDateTime) <= 0)
                            horarioInicioDateTime = horarioInicioDateTime.AddDays(-1);

                        var horaAtual = DateTime.UtcNow.AddHours(-3);

                        var dataAtualMenorQueHorarioInicio = DateTime.Compare(horarioInicioDateTime, horaAtual);
                        var dataAtualMaiorQueHorarioFim = DateTime.Compare(horarioFimDateTime, horaAtual);

                        if (dataAtualMenorQueHorarioInicio != dataAtualMaiorQueHorarioFim)
                        {
                            return new RespPadrao
                            {
                                sucesso = false,
                                mensagem =
                                    "Horário atual é divergente do horário permitido para realizar abastecimentos."
                            };
                        }
                    }

                    #endregion

                    // if (lVeiculo.Filial?.Faturar == 1)
                    // {
                    //     command.CnpjAFaturar = _filialReadRepository.GetCnpj(lVeiculo.FilialId ?? 0);
                    //     command.FilialId = lVeiculo.FilialId;
                    // }
                    // else
                    // {
                    command.CnpjAFaturar =
                        await _postoReadRepository.GetCnpjFaturamentoByIdAsync(lAbastecimentoReq.PostoId);
                    //}
                }
                else
                {
                    command.CnpjAFaturar = await _postoReadRepository.GetCnpjFaturamentoByIdAsync(lAbastecimentoReq.PostoId);
                }

                command.EmpresaId = portadorEmpresaValidador.Id;

                if (portadorEmpresaValidador.DebitoPrazo == 1)
                {
                    command.TipoDebito = ETipoDebitoAbastecimento.Prazo;
                }

                if (portadorEmpresaValidador.DebitoProtocolo == 1)
                {
                    command.TipoDebito = ETipoDebitoAbastecimento.Protocolo;
                }

                command.AutorizacaoAbastecimentoId = autorizacaoAbastecimento.Id;

                var retorno = await Engine.CommandBus
                    .SendCommandAsync<Domain.Models.Abastecimento.Abastecimento>(command);

                #endregion

                #region Atualização de dados de autorizacao e veiculo referente ao abastecimento

                var retencaoStatus = false;
                var retencaoSolicitacao = new RespPadrao();
                
                if (retorno.Id > 0)
                {
                    #region Retencao

                    if (retorno.Status == EStatusAbastecimento.Aprovado && retorno.TipoDebito == ETipoDebitoAbastecimento.Abastecimento)
                    {
                        retencaoSolicitacao = _pagamentoAbastecimentoAppService.IntegrarRetencao(retorno.Id, false).Result;

                        if (retencaoSolicitacao.sucesso)
                        {
                            retencaoStatus = true;
                        }
                    }

                    #endregion

                    if (((retencaoStatus && retorno.TipoDebito == ETipoDebitoAbastecimento.Abastecimento) 
                         || (!retencaoStatus && retorno.TipoDebito != ETipoDebitoAbastecimento.Abastecimento))
                        && (retorno.Status == EStatusAbastecimento.Aprovado 
                            || retorno.Status == EStatusAbastecimento.AguardandoAprovacao))
                    {
                        //atualiza litragem utilizada da autorização
                        autorizacaoAbastecimento.LitragemUtilizada =
                            autorizacaoAbastecimento.LitragemUtilizada + command.Litragem;
                        
                        autorizacaoAbastecimento.Status = autorizacaoAbastecimento.Metodo == 2
                            ? StatusAutorizacaoAbastecimento.Baixado
                            : autorizacaoAbastecimento.Status;

                        var commandAutorizacaoAbatecimento =
                            Mapper.Map<AutorizacaoAbastecimentoSalvarCommand>(autorizacaoAbastecimento);

                        _autorizacaoAbastecimentoReadRepository.Detach(autorizacaoAbastecimento);

                        await _autorizacaoAbastecimentoAppService.Engine.CommandBus
                            .SendCommandAsync<Domain.Models.AutorizacaoAbastecimento.AutorizacaoAbastecimento>(
                                commandAutorizacaoAbatecimento);

                        //atualiza odometro do veiculo
                        veiculoValidador.Odometro = lAbastecimentoReq.OdometroAtual;

                        var commandVeiculoUpdate = Mapper.Map<VeiculoSalvarCommand>(veiculoValidador);

                        _veiculoReadRepository.Detach(veiculoValidador);

                        await _veiculoAppService.Engine.CommandBus
                            .SendCommandAsync<Domain.Models.Veiculo.Veiculo>(commandVeiculoUpdate);
                    }

                }

                #endregion
                
                #region Integracao com a movida se TipoEmpresa é Movida

                if (retorno.Status == EStatusAbastecimento.Aprovado)
                {
                    Repository.Query.Detach(retorno);
                    await _centralPendenciasAppService.ReenviarAbastecimentoMovida(retorno.Id);
                }
                
                #endregion

                #region Validar falha retencao

                if (!retencaoStatus && retorno.Status == EStatusAbastecimento.Aprovado && retorno.TipoDebito == ETipoDebitoAbastecimento.Abastecimento)
                {
                    var abastecimentoUpdate = Repository.Query.FirstOrDefault(x => x.Id == retorno.Id);

                    abastecimentoUpdate.Status = EStatusAbastecimento.Cancelado;
                    
                    var commandUpdate = Mapper.Map<AbastecimentoSalvarComRetornoCommand>(abastecimentoUpdate);
                    
                    
                    Repository.Query.Detach(abastecimentoUpdate);

                    var retornoUpdate = await Engine.CommandBus
                        .SendCommandAsync<Domain.Models.Abastecimento.Abastecimento>(commandUpdate);
                    
                    return new RespPadrao
                    {
                        id = retornoUpdate.Id,
                        sucesso = false,
                        mensagem = "Abastecimento não realizado. " + retencaoSolicitacao.mensagem
                    };
                }
                
                if (retencaoStatus && retorno.Status == EStatusAbastecimento.Aprovado)
                {
                    var abastecimentoUpdate = Repository.Query.FirstOrDefault(x => x.Id == retorno.Id);
                    
                    var commandUpdate = Mapper.Map<AbastecimentoSalvarComRetornoCommand>(abastecimentoUpdate);
                    
                    Repository.Query.Detach(abastecimentoUpdate);

                    var retornoUpdate = await Engine.CommandBus
                        .SendCommandAsync<Domain.Models.Abastecimento.Abastecimento>(commandUpdate);
                    
                    if (lVeiculo.Empresa.ControlaContingencia == 0)
                    {
                        
                        var lAbastecimento = new AutorizacaoContingeciaRequest()
                        {
                            AutorizacaoContingeciaRequestItems = new List<AutorizacaoContingeciaRequestItem>()
                        };

                        var abastecimento = new AutorizacaoContingeciaRequestItem()
                        {
                            Id = retornoUpdate.Id,
                            Motivo = "Aprovação gerada automaticamente",
                            Status = EStatusAbastecimento.Aprovado
                        };
                        lAbastecimento.AutorizacaoContingeciaRequestItems.Add(abastecimento);
                        
                       await _autorizacaoContingeciaAppService.SaveAprovacao(lAbastecimento, true);
                    }
                    
                    return new RespPadrao
                    {
                        id = retornoUpdate.Id,
                        sucesso = true,
                        mensagem = "Abastecimento realizado com sucesso."
                    };
                }

                #endregion
                
                return new RespPadrao
                {
                    id = retorno.Id,
                    sucesso = true,
                    mensagem = "Abastecimento realizado com sucesso."
                };
            }
            catch (Exception e)
            {
                return new RespPadrao()
                {
                    sucesso = false,
                    mensagem = e.Message
                };
            }
        }

        public async Task<RespPadrao> ConsultarFuncionario(string portadorCpfCnpj)
        {
            try
            {
                var portador = await _portadorReadRepository.GetByCpfCnpjAsync(portadorCpfCnpj);

                if (portador == null)
                    return new RespPadrao(false, "Funcionário não encontrado.", null);

                var centroCusto = _portadorCentroCustoReadRepository
                    .Where(x => x.PortadorId == portador.Id).Any();

                var portadorEmpresa = await _portadorEmpresaReadRepository
                    .FirstOrDefaultAsync(p => p.PortadorId == portador.Id);
                
                var controlaOdometro = false;

                if (portadorEmpresa != null)
                {
                    var empresa = await _empresaReadRepository.GetByIdAsync(portadorEmpresa.EmpresaId);
                    controlaOdometro = empresa?.ControlaOdometro.ToIntSafe() == 1;
                }
                
                var funcionario = new ConsultaFuncionarioResponse();
                funcionario.Id = portador.Id;
                funcionario.Nome = portador.Nome;
                funcionario.CpfCnpj = portador.CpfCnpj;
                funcionario.PossuiCentroCusto = centroCusto;
                funcionario.ControlaOdometro = controlaOdometro;
                funcionario.TipoPessoa = portador.TipoPessoa.GetHashCode() == 1;

                return new RespPadrao(true, "Funcionário encontrado.", funcionario);
            }
            catch (Exception e)
            {
                LogManager.GetCurrentClassLogger().Error(e);
                return new RespPadrao(false, "Não foi possível consultar o funcionário. Mensagem: " + e.Message);
            }
        }

        public async Task<RespPadrao> Cancelar(int abastecimentoId, string portador, bool integracao = false)
        {
            #region Manuseio de informacoes

            var abastecimentoConsulta = Repository.Query.Where(x => x.Id == abastecimentoId).FirstOrDefault();

            if (abastecimentoConsulta == null)
            {
                return new RespPadrao
                {
                    sucesso = false,
                    mensagem = "Nenhum abastecimento encontrado com este ID"
                };
            }

            abastecimentoConsulta.Status = EStatusAbastecimento.Cancelado;

            if (integracao)
            {
                var portadorId = _portadorReadRepository.FirstOrDefault(x => x.CpfCnpj == portador).Id;
                
                abastecimentoConsulta.Motivo = "Cancelamento via mobile. portador Id: " + portadorId;
            }
            
            #region Extorno da retencao

            var retencaoId =
                (_pagamentoAbastecimentoAppService.Repository.Query.FirstOrDefault(x =>
                    x.AbastecimentoId == abastecimentoConsulta.Id && x.Status == StatusPagamentoAbastecimento.Baixado)?.Id).ToInt();

            var retornoCancelamentoRetencao = new RespPadrao()
            {
                sucesso = false
            };

            if (retencaoId > 0)
            {
                retornoCancelamentoRetencao = _pagamentoAbastecimentoAppService.CancelarRetencao(retencaoId, false).Result;

                if (!retornoCancelamentoRetencao.sucesso)
                {
                    return new RespPadrao
                    {
                        sucesso = false,
                        mensagem = "Erro ao realizar o cancelamento! " + retornoCancelamentoRetencao.mensagem
                    };
                }
            }
            

            var abstecimentoUpd =
                Mapper.Map<AbastecimentoSalvarComRetornoCommand>(abastecimentoConsulta);

            Repository.Query.Detach(abastecimentoConsulta);

            if (User.AdministradoraId > 0)
            {
                abstecimentoUpd.UsuarioAlteracaoId = 1;
            }
            
            var retornoCancelamento = await Engine.CommandBus
                .SendCommandAsync<Domain.Models.Abastecimento.Abastecimento>(abstecimentoUpd);
            
            if (retornoCancelamento.Id <= 0)
            {
                throw new Exception();
            }

            #endregion

            #region reversao de informacoes de veiculo e autorizacao viculadas ao abastecimento
            
            var lAutorizacaoAbastecimento = _autorizacaoAbastecimentoAppService.Repository.Query
                .FirstOrDefault(x => x.Id == abstecimentoUpd.AutorizacaoAbastecimentoId);

            if (lAutorizacaoAbastecimento != null)
            {
                var autorizacaoList =
                    Mapper.Map<AutorizacaoAbastecimentoSalvarCommand>(
                        lAutorizacaoAbastecimento);

                _autorizacaoAbastecimentoAppService.Repository.Query.Detach(lAutorizacaoAbastecimento);

                autorizacaoList.LitragemUtilizada = (lAutorizacaoAbastecimento.LitragemUtilizada -
                                                     abstecimentoUpd.Litragem) < 0
                    ? 0
                    : lAutorizacaoAbastecimento.LitragemUtilizada -
                      abstecimentoUpd.Litragem;
                
                autorizacaoList.Status = autorizacaoList.Metodo == "2"
                    ? StatusAutorizacaoAbastecimento.Aberto
                    : autorizacaoList.Status;
                
                await _autorizacaoAbastecimentoAppService.Engine.CommandBus
                    .SendCommandAsync<Domain.Models.AutorizacaoAbastecimento.AutorizacaoAbastecimento>(
                        autorizacaoList);

                if (abstecimentoUpd.OdometroAtual != abstecimentoUpd.OdometroAnterior)
                {
                    var veiculoRetornoOdometro =
                        _veiculoAppService.Repository.Query.FirstOrDefault(x => x.Id == abstecimentoUpd.VeiculoId);

                    var veiculoList =
                        Mapper.Map<VeiculoSalvarCommand>(veiculoRetornoOdometro);

                    _veiculoAppService.Repository.Query.Detach(veiculoRetornoOdometro);

                    veiculoList.Odometro = abstecimentoUpd.OdometroAnterior;

                    await _veiculoAppService.Engine.CommandBus
                        .SendCommandAsync<Domain.Models.Veiculo.Veiculo>(
                            veiculoList);
                }
            }

            //removendo autorizacao do abastecimento devido ao cancelamento
            var abstecimentoRemoverAutorizacao = Mapper.Map<RemoverAutorizacaoAbastecimentoCommand>(abstecimentoUpd);
            
            await Engine.CommandBus.SendCommandAsync(abstecimentoRemoverAutorizacao);

            #endregion

            if (retornoCancelamento.Status == EStatusAbastecimento.Cancelado || (retencaoId > 0 && retornoCancelamentoRetencao.sucesso))
            {
                return new RespPadrao
                {
                    data = retornoCancelamento.Id,
                    sucesso = true,
                    mensagem = "Abastecimento cancelado!"
                };
            }
            
            return new RespPadrao
            {
                sucesso = false,
                mensagem = "Erro ao realizar o cancelamento!"
            };

            #endregion
        }
        
        public RespPadrao IntegracaoAbastecimentoMobile(IntegrarAbastecimentoMobileRequest request, string portadorCpfCnpj)
        {
            #region validaçao seguranca de vinculo do veiculo e odometro

            var veiculoConsulta = _veiculoReadRepository.FirstOrDefault(x => x.Id == request.VeiculoId);

            var placaFrota =
                _portadorReadRepository.FirstOrDefault(x => x.CpfCnpj == portadorCpfCnpj)
                    .ControlaAbastecimentoCentroCusto == 1
                    ? veiculoConsulta.NumeroFrota
                    : veiculoConsulta.Placa; 
            
            var requestValidarVeiculo = new ConsultaVeiculoAbastecimentoMobileRequest()
            {
                PlacaFrota = placaFrota,
                PostoId = request.PostoId
            };

            var validaVeiculo = _veiculoAppService.ConsultaVeiculoAbastecimento(requestValidarVeiculo, portadorCpfCnpj);

            if (!validaVeiculo.sucesso)
            {
                return new RespPadrao()
                {
                    sucesso = false,
                    mensagem = validaVeiculo.mensagem
                };
            }

            if (request.OdometroAtual <= veiculoConsulta.Odometro)
            {
                return new RespPadrao()
                {
                    sucesso = false,
                    mensagem = "Odômetro menor ou igual anterior."
                };
            }

            #endregion

            #region Criador do abastecimento e salvamento

            var solicitacaoAbastecimento = new AbastecimentoRequest();

            solicitacaoAbastecimento.Litragem = request.Litragem;
            solicitacaoAbastecimento.OdometroAnterior =
                _veiculoReadRepository.FirstOrDefault(x => x.Id == request.VeiculoId).Odometro.ToInt();
            solicitacaoAbastecimento.OdometroAtual = request.OdometroAtual;
            
            var valorUnitario = _postoCombustivelReadRepository
                .Where(x => x.PostoId == request.PostoId && x.CombustivelId == request.CombustivelId)?
                .FirstOrDefault()?
                .ValorCombustivelBBC.ToDecimalSafe();

            if (valorUnitario == null || valorUnitario == 0)
            {
                return new RespPadrao()
                {
                    sucesso = false,
                    mensagem = "Combustivel não cadastrado no posto!"
                };
            }

            solicitacaoAbastecimento.ValorUnitario = (decimal) valorUnitario;
            solicitacaoAbastecimento.ValorAbastecimento = (decimal) (valorUnitario * request.Litragem);
            
            solicitacaoAbastecimento.CombustivelId = request.CombustivelId;
            solicitacaoAbastecimento.VeiculoId = request.VeiculoId;
            solicitacaoAbastecimento.PostoId = request.PostoId;
           

            //Informacoes tachadas
            var portadorId = _portadorReadRepository.FirstOrDefault(x => x.CpfCnpj == portadorCpfCnpj).Id; 
            
            solicitacaoAbastecimento.PortadorId = portadorId;
            
            solicitacaoAbastecimento.Motivo =
                "Abastecimento realizado via Mobile. Portador Id: " + portadorId;
            
            solicitacaoAbastecimento.Status = EStatusAbastecimento.Aprovado;

            var lancamentoBastecimento = Save(solicitacaoAbastecimento, false).Result;

            #endregion

            #region Validacao de sucesso

            if (lancamentoBastecimento.sucesso)
            {
                return lancamentoBastecimento;
            }

            #endregion
            
            return new RespPadrao
            {
                sucesso = false,
                mensagem = "Abastecimento não realizado! "+lancamentoBastecimento.mensagem
            };
        }

        public RespPadrao ImpressaoComprovanteAbastecimentoApi(ImpressaoComprovanteAbastecimentoMobileRequest printRequest,
            string portadorCpfCnpj)
        {
            var portadorId = _portadorReadRepository.FirstOrDefault(x => x.CpfCnpj == portadorCpfCnpj).Id;

            var abastecimentoConsulta = Mapper.Map<ImpressaoComprovanteAbastecimentoMobileResponse>(
                _abastecimentoReadRepository
                    .Where(x => x.Id == printRequest.AbastecimentoId && x.PortadorId == portadorId)
                    .Include(x => x.Posto)
                    .ThenInclude(p => p.Cidade)
                    .Include(x => x.Portador)
                    .Include(x => x.Veiculo)
                    .ThenInclude(v=> v.Modelo)
                    .Include(x => x.Combustivel)
                    .FirstOrDefault());

            
            if (abastecimentoConsulta != null)
            {
                return new RespPadrao()
                {
                    sucesso = true,
                    data = abastecimentoConsulta
                };
            }

            return new RespPadrao()
            {
                sucesso = false,
                mensagem = "Abastecimento não encontrado!"
            };
        }
        
        public ConsultarGridHistoricoManutencaoAbastecimentoResponse ConsultarGridHistoricoCancelamentos(int IdPosto, DateTime dtInicial, DateTime dtFinal, int status, int take, int page, OrderFilters orderFilters,
            List<QueryFilters> filters)
        {
            IQueryable<Domain.Models.Abastecimento.Abastecimento> lHistoricoSolicitacoesPendentes;

            lHistoricoSolicitacoesPendentes = Repository.Query.GetAll();

            IdPosto = IdPosto > 0 ? IdPosto : Engine.User.AdministradoraId;

            if (IdPosto > 0)
            {
                lHistoricoSolicitacoesPendentes = lHistoricoSolicitacoesPendentes.Where(x => x.PostoId == IdPosto);
            }
            
            
            if(dtInicial > ("01/01/0001 00:00:00").ToDateTime() || dtFinal > ("01/01/0001 00:00:00").ToDateTime())
            {
                var dtIni = dtInicial.ToShortDateString().ToDateTime();
                var dtFim = dtFinal.ToShortDateString().ToDateTime().AddDays(1).AddSeconds(-1);

                lHistoricoSolicitacoesPendentes = lHistoricoSolicitacoesPendentes.Where(p => p.DataCadastro >= dtIni && p.DataCadastro <= dtFim);
            }
            
            lHistoricoSolicitacoesPendentes = lHistoricoSolicitacoesPendentes.Where(x => x.Status == EStatusAbastecimento.Cancelado);

            lHistoricoSolicitacoesPendentes = lHistoricoSolicitacoesPendentes.AplicarFiltrosDinamicos(filters);
            lHistoricoSolicitacoesPendentes = string.IsNullOrWhiteSpace(orderFilters?.Campo)
                ? lHistoricoSolicitacoesPendentes.OrderByDescending(o => o.Id)
                : lHistoricoSolicitacoesPendentes.OrderBy($"{orderFilters.Campo} {orderFilters.Operador.DescriptionAttr()}");

            var lCount = lHistoricoSolicitacoesPendentes.Count();
            var retorno = lHistoricoSolicitacoesPendentes.Skip((page - 1) * take)
                .Take(take).ProjectTo<ConsultarGridHistoricoManutencaoAbastecimento>(Engine.Mapper.ConfigurationProvider).ToList();

            return new ConsultarGridHistoricoManutencaoAbastecimentoResponse
            {
                items = retorno,
                totalItems = lCount
            };
        }

        public ConsultarGridManutencaoAbastecimento ConsultarAbastecimentos(int idEmpresa, DateTime dtInicial, DateTime dtFinal, int take, int page, OrderFilters orderFilters, List<QueryFilters> filters)
        {
            var idPosto = Engine.User.AdministradoraId;

            List<int> veiculoEmpresa;
            
            if (idEmpresa != 0)
            {
                veiculoEmpresa = _veiculoAppService.Repository.Query.GetAll().Where(e => e.EmpresaId == idEmpresa)
                    .Select(x => x.Id)
                    .ToList();
            }
            else
            {
                veiculoEmpresa = _veiculoAppService.Repository.Query.GetAll()
                    .Select(x => x.Id)
                    .ToList();
            }

            var lAbastecimento =  Repository.Query
                .Include(c => c.Combustivel)
                .Include(v=>v.Veiculo)
                .Include(u=>u.UsuarioAlteracao)
                .Include(u=>u.UsuarioCadastro)
                .Where(a => a.DataCadastro >= dtInicial.ToShortDateString().ToDateTime() 
                            && a.DataCadastro <= dtFinal.ToShortDateString().ToDateTime().AddDays(1).AddSeconds(-1) 
                            && a.PostoId == idPosto 
                            && a.Status != EStatusAbastecimento.Cancelado)
                .Where(a => veiculoEmpresa.Contains(a.VeiculoId));
            
            var lCount = lAbastecimento.Count();
            
            lAbastecimento = lAbastecimento.OrderByDescending(o => o.Id);
            var retorno = lAbastecimento.Skip((page - 1) * take)
                .ProjectTo<ManutencaoAbastecimentoResponse>()
                .Take(take).ToList();

            return new ConsultarGridManutencaoAbastecimento()
            {
                items = retorno,
                totalItems = lCount
            };
        }
        
        public RespPadrao ReimpressaoProtocolo(ManutencaoAbastecimentoReimpressaoRequest request)
        {
            try
            {
                var abastecimento = Repository.Query
                    .Include(x => x.Posto)
                    .Include(x => x.Combustivel)
                    .Include(x => x.Veiculo)
                    .Include(x => x.Portador).FirstOrDefault(x => x.Id == request.Id);

                if (abastecimento == null)
                {
                    return new RespPadrao()
                    {
                        sucesso = false,
                        data = null,
                        mensagem = $"Abastecimento com {request.Id} não encontrado!"
                    };
                }

                return new RespPadrao()
                {
                    sucesso = true,
                    data = new ComprovanteAbastecimentoResponse()
                    {
                        NomePosto = abastecimento.Posto.RazaoSocial == null
                            ? abastecimento.Posto.NomeFantasia
                            : abastecimento.Posto.RazaoSocial,
                        CnpjPosto = abastecimento.Posto.Cnpj.FormatarCpfCnpj(),
                        CodAutorizacao = abastecimento.AutorizacaoAbastecimentoId.ToString(),
                        Funcionario = abastecimento.Portador.Nome,
                        Placa = abastecimento.Veiculo.Placa.ToPlacaFormato(),
                        CombustivelNome = abastecimento.Combustivel.Nome,
                        Litragem = abastecimento.Litragem.ToString("F3",CultureInfo.InvariantCulture),
                        Valor = StringHelper.FormatMoney(abastecimento.ValorAbastecimento)
                    }
                };
            }
            catch (Exception e)
            {
                return new RespPadrao()
                {
                    sucesso = false,
                    mensagem = e.Message
                };
            }
        }
        
         public RespPadrao EnviarComprovanteAbastecimentoEmail(ManutencaoAbastecimentoReimpressaoRequest request)
        {
            try
            {
                var abastecimento = Repository.Query
                    .Include(x => x.Posto)
                    .Include(x => x.Combustivel)
                    .Include(x => x.Veiculo)
                    .Include(x => x.Portador).FirstOrDefault(x => x.Id == request.Id);

                if (abastecimento == null)
                {
                    return new RespPadrao()
                    {
                        sucesso = false,
                        data = null,
                        mensagem = $"Abastecimento com {request.Id} não encontrado!"
                    };
                }

                var data = new ComprovanteAbastecimentoResponse()
                {
                    NomePosto = abastecimento.Posto.RazaoSocial == null
                        ? abastecimento.Posto.NomeFantasia
                        : abastecimento.Posto.RazaoSocial,
                    CnpjPosto = abastecimento.Posto.Cnpj.FormatarCpfCnpj(),
                    CodAutorizacao = abastecimento.AutorizacaoAbastecimentoId.ToString(),
                    Funcionario = abastecimento.Portador.Nome,
                    Placa = abastecimento.Veiculo.Placa.ToPlacaFormato(),
                    CombustivelNome = abastecimento.Combustivel.Nome,
                    Litragem = abastecimento.Litragem.ToString("F3", CultureInfo.InvariantCulture),
                    Valor = StringHelper.FormatMoney(abastecimento.ValorAbastecimento)
                };
                
                EmailComprovanteAbastecimento.EnviarEmailComprovanteAbastecimento(_emailExecutor,abastecimento.Portador.Email,data);
                
                return new RespPadrao()
                {
                    sucesso = true,
                    data = null,
                    mensagem = $"Email enviado com sucesso!"
                };
            }
            catch (Exception e)
            {
                return new RespPadrao()
                {
                    sucesso = false,
                    mensagem = e.Message
                };
            }
        }

        public ConsultarGridAbastecimentoPainelFinanceiroResponse ConsultarGridLoteAbastecimentos(ISessionUser user, int pagamentoAbastecimento, int take, int page, OrderFilters orderFilters,
            List<QueryFilters> filters)
        {
            try
            {
                var pagamentoAbastecimentoConsulta = _pagamentoAbastecimentoAppService.Repository.Query.GetByIdAllInclude(pagamentoAbastecimento).Result;

                IQueryable<Domain.Models.Abastecimento.Abastecimento> lAbastecimento;
                
                if (pagamentoAbastecimentoConsulta.TipoOperacao == TipoOperacaoPagamentoAbastecimento.Pagamento ||
                    (pagamentoAbastecimentoConsulta.TipoOperacao == TipoOperacaoPagamentoAbastecimento.Retencao 
                     && pagamentoAbastecimentoConsulta.Abastecimento == null 
                     && pagamentoAbastecimentoConsulta.LotePagamento != null))
                {
                    
                    
                    if (pagamentoAbastecimentoConsulta.LotePagamento.EmpresaId != user.EmpresaId) {
                        return null;
                    }
                    
                    lAbastecimento = Repository.Query.GetAll()
                        .Include(x => x.PagamentoAbastecimentos)
                        .Include(x => x.ProtocoloAbastecimento)
                        .ThenInclude(a => a.LotePagamento)
                        .Include(x => x.Veiculo)
                        .Include(x => x.Combustivel)
                        .Where(x => pagamentoAbastecimentoConsulta.TipoOperacao == TipoOperacaoPagamentoAbastecimento.Pagamento 
                            ? x.ProtocoloAbastecimento.LotePagamento.Id == pagamentoAbastecimentoConsulta.LotePagamentoId 
                            : x.LotePagamento.Id == pagamentoAbastecimentoConsulta.LotePagamentoId); 
                }
                else
                {
                    
                    if (pagamentoAbastecimentoConsulta.Abastecimento != null && pagamentoAbastecimentoConsulta.Abastecimento.EmpresaId != user.EmpresaId) {
                        return null;
                    }
                    
                    lAbastecimento = Repository.Query.GetAll()
                        .Include(x => x.PagamentoAbastecimentos)
                        .Include(x => x.ProtocoloAbastecimento)
                        .Include(x => x.Veiculo)
                        .Include(x => x.Combustivel)
                        .Where(x => x.Id == pagamentoAbastecimentoConsulta.AbastecimentoId);
                }

                lAbastecimento =
                    lAbastecimento.AplicarFiltrosDinamicos(filters);
                lAbastecimento = string.IsNullOrWhiteSpace(orderFilters?.Campo)
                    ? lAbastecimento.OrderByDescending(o => o.Id)
                    : lAbastecimento.OrderBy($"{orderFilters.Campo} {orderFilters.Operador.DescriptionAttr()}");

                var lCount = lAbastecimento.Count();
                var retorno = lAbastecimento.Skip((page - 1) * take)
                    .Take(take).ToList();

                var retornoGrid = Mapper.Map<List<ConsultarGridGridAbastecimentoPainelFinanceiro>>(retorno);

                return new ConsultarGridAbastecimentoPainelFinanceiroResponse
                {
                    items = retornoGrid,
                    totalItems = lCount
                };

            }
            catch (Exception e)
            {
                LogManager.GetCurrentClassLogger().Error(e);
                throw;
            }
        }
        
        public ConsultarGridAbastecimentoPainelFinanceiroResponse ConsultarGridLoteAbastecimentos(int idPosto, int pagamentoAbastecimento, int take, int page, OrderFilters orderFilters,
            List<QueryFilters> filters)
        {
            try
            {
                var pagamentoAbastecimentoConsulta = _pagamentoAbastecimentoAppService.Repository.Query.GetByIdAllInclude(pagamentoAbastecimento).Result;
                IQueryable<Domain.Models.Abastecimento.Abastecimento> lAbastecimento;

                if (pagamentoAbastecimentoConsulta.LotePagamento != null && pagamentoAbastecimentoConsulta.LotePagamento.PostoId != idPosto) {
                    return null;
                }
                
                if (pagamentoAbastecimentoConsulta.TipoOperacao == TipoOperacaoPagamentoAbastecimento.Pagamento ||
                    (pagamentoAbastecimentoConsulta.TipoOperacao == TipoOperacaoPagamentoAbastecimento.Retencao 
                     && pagamentoAbastecimentoConsulta.Abastecimento == null 
                     && pagamentoAbastecimentoConsulta.LotePagamento != null))
                {
                    if (pagamentoAbastecimentoConsulta.LotePagamento != null && 
                        pagamentoAbastecimentoConsulta.LotePagamento.PostoId != idPosto) {
                        return null;
                    }
                    
                    lAbastecimento = Repository.Query.GetAll()
                        .Include(x => x.PagamentoAbastecimentos)
                        .Include(x => x.ProtocoloAbastecimento)
                        .ThenInclude(a => a.LotePagamento)
                        .Include(x => x.Veiculo)
                        .Include(x => x.Combustivel)
                        .Where(x => x.ProtocoloAbastecimento.LotePagamento.PostoId == idPosto && pagamentoAbastecimentoConsulta.TipoOperacao == TipoOperacaoPagamentoAbastecimento.Pagamento 
                            ? x.ProtocoloAbastecimento.LotePagamento.Id == pagamentoAbastecimentoConsulta.LotePagamentoId 
                            : x.LotePagamento.Id == pagamentoAbastecimentoConsulta.LotePagamentoId); 
                }
                else
                {
                    if (pagamentoAbastecimentoConsulta.Abastecimento != null 
                        && pagamentoAbastecimentoConsulta.Abastecimento.PostoId != idPosto) {
                        return null;
                    }
                    
                    lAbastecimento = Repository.Query.GetAll()
                        .Include(x => x.PagamentoAbastecimentos)
                        .Include(x => x.ProtocoloAbastecimento)
                        .Include(x => x.Veiculo)
                        .Include(x => x.Combustivel)
                        .Where(x => x.Id == pagamentoAbastecimentoConsulta.AbastecimentoId);
                }

                lAbastecimento =
                    lAbastecimento.AplicarFiltrosDinamicos(filters);
                lAbastecimento = string.IsNullOrWhiteSpace(orderFilters?.Campo)
                    ? lAbastecimento.OrderByDescending(o => o.Id)
                    : lAbastecimento.OrderBy($"{orderFilters.Campo} {orderFilters.Operador.DescriptionAttr()}");

                var lCount = lAbastecimento.Count();
                var retorno = lAbastecimento.Skip((page - 1) * take)
                    .Take(take).ToList();

                var retornoGrid = Mapper.Map<List<ConsultarGridGridAbastecimentoPainelFinanceiro>>(retorno);

                return new ConsultarGridAbastecimentoPainelFinanceiroResponse
                {
                    items = retornoGrid,
                    totalItems = lCount
                };

            }
            catch (Exception e)
            {
                LogManager.GetCurrentClassLogger().Error(e);
                throw;
            }
        }
        
        
        public ConsultarGridLoteAbastecimentoResponse ConsultarGridLoteAbastecimento(int lotePagamento, int take, int page, OrderFilters orderFilters,
            List<QueryFilters> filters)
        {
            var lAbastecimento = Repository.Query.GetAbastecimentosPortadorLotePagamento(lotePagamento);
            
            lAbastecimento = lAbastecimento.AplicarFiltrosDinamicos<Domain.Models.Abastecimento.Abastecimento>(filters);
            lAbastecimento = string.IsNullOrWhiteSpace(orderFilters?.Campo)
                ? lAbastecimento.OrderByDescending(o => o.Id)
                : lAbastecimento.OrderBy($"{orderFilters.Campo} {orderFilters.Operador.DescriptionAttr()}");

            var lCount = lAbastecimento.Count();
            var retorno = lAbastecimento.Skip((page - 1) * take)
                .Take(take).ProjectTo<ConsultarGridLote>(Engine.Mapper.ConfigurationProvider).ToList();

            return new ConsultarGridLoteAbastecimentoResponse
            {
                items = retorno,
                totalItems = lCount
            };
        }
        
        public ConsultarGridAbastecimentosProtocoloResponse ConsultarGridProtocoloAbastecimento(int protocolo, int take, int page, OrderFilters orderFilters,
            List<QueryFilters> filters)
        {
            var lAbastecimento = Repository.Query
                .Include(x => x.PagamentoAbastecimentos)
                .Include(x => x.ProtocoloAbastecimento)
                .Where(x => x.ProtocoloAbastecimentoId == protocolo);
            
            lAbastecimento = lAbastecimento.AplicarFiltrosDinamicos<Domain.Models.Abastecimento.Abastecimento>(filters);
            lAbastecimento = string.IsNullOrWhiteSpace(orderFilters?.Campo)
                ? lAbastecimento.OrderByDescending(o => o.Id)
                : lAbastecimento.OrderBy($"{orderFilters.Campo} {orderFilters.Operador.DescriptionAttr()}");

            var lCount = lAbastecimento.Count();
            var retorno = lAbastecimento.Skip((page - 1) * take)
                .Take(take).ProjectTo<ConsultarGridAbastecimentoProtocolo>().ToList();

            return new ConsultarGridAbastecimentosProtocoloResponse
            {
                items = retorno,
                totalItems = lCount
            };
        }

        public ConsultarPostoAbastecimentoMobileResponse ConsultaPostoAbastecimento(CnpjPostQrCodeRequest request)
        {
            var retorno = new ConsultarPostoAbastecimentoMobileResponse
            {
                Validations = new List<string>(),
            };
            
            var retornoExternal = _mobile2YouRepository.PostAsyncHttpClient("external/getEstabelecimento",request).Result;

            if (retornoExternal.Sucesso)
            {
                var objetoRetorno = JsonConvert.DeserializeObject<CnpjPostQrCodeResponse>(retornoExternal.RetornoJson);
                
                var posto = _postoReadRepository
                .GetAll()
                .FirstOrDefault(x => x.Cnpj == objetoRetorno.Cnpj.OnlyNumbers() && x.Ativo == 1);

                if (posto == null)
                    retorno.Validations.Add("Posto não existe na base da BBC!");
                else
                    retorno.PostoAbastecimentoId = posto.Id.ToString();
            }
            else
                retorno.Validations.Add(retornoExternal.message);
            
            return retorno;
        }

        public ConsultarGridPainelAbastecimentoResponse ConsultarGridPainelAbastecimento(DtoConsultaGridPainelAbastecimento request)
        {
            var dtIni = request.DataInicial.ToShortDateString().ToDateTime();
            var dtFim = request.DataFinal.ToShortDateString().ToDateTime().AddDays(1).AddSeconds(-1);
            
            var lAbastecimento = Repository.Query
                .Where(x => x.DataCadastro >= dtIni 
                            && x.DataCadastro <= dtFim);
            
            if (request.EmpresaId > 0)
            {
                lAbastecimento = lAbastecimento.Where(x => x.PostoId == request.EmpresaId);
            }
            
            if (request.Status != 4)
            {
                if (request.Status == 0)
                    lAbastecimento = lAbastecimento.Where(x => x.Status == EStatusAbastecimento.Reprovado || x.Status == EStatusAbastecimento.Cancelado);
                else
                    lAbastecimento = lAbastecimento.Where(x => x.Status.GetHashCode() == request.Status);
            }

            lAbastecimento = lAbastecimento.AplicarFiltrosDinamicos(request.Filters);
            lAbastecimento = string.IsNullOrWhiteSpace(request.Order?.Campo)
                ? lAbastecimento.OrderByDescending(o => o.Id)
                : lAbastecimento.OrderBy($"{request.Order.Campo} {request.Order.Operador.DescriptionAttr()}");
            
            var retorno = lAbastecimento.Skip((request.Page - 1) * request.Take)
                .Take(request.Take).ProjectTo<ConsultarGridPainelAbastecimento>(Engine.Mapper.ConfigurationProvider).ToList();

            return new ConsultarGridPainelAbastecimentoResponse
            {
                items = retorno,
                totalPgtos = retorno.Count(x => x.Status == "Realizado"),
                totalItems = lAbastecimento.Count()
            };
        }
    }
}
