﻿using Microsoft.EntityFrameworkCore;
using Microsoft.EntityFrameworkCore.Metadata.Builders;
using SistemaInfo.BBC.Domain.Models.AuthSessionApi;
using SistemaInfo.Framework.EntityFramework.Configuration;

namespace SistemaInfo.BBC.Infra.Data.Mappings
{
    public class AuthSessionApiMapping : EntityTypeConfiguration<AuthSessionApi>
    {
        public override void Map(EntityTypeBuilder<AuthSessionApi> builder)
        {
            {
                builder.ToTable("AuthSessionApi");
                builder.HasKey(b => b.Id);
                
                builder.Property(b => b.Id).IsRequired().HasColumnName("Id").ValueGeneratedOnAdd();
                
                builder.Property(b => b.DataCadastro).IsRequired().HasColumnName("DataCadastro").HasColumnType("timestamp");
                builder.Property(b => b.DataUltimaReq).IsRequired().HasColumnName("DataUltimaReq").HasColumnType("timestamp");
                builder.Property(b => b.EmpresaId).HasColumnName("EmpresaId").HasColumnType("int");
                builder.Property(b => b.GrupoEmpresaId).HasColumnName("GrupoEmpresaId").HasColumnType("int");
                builder.Property(b => b.Token).IsRequired().HasColumnName("Token").HasColumnType("varchar(300)");
                builder.Property(b => b.Login).HasColumnName("Login").HasColumnType("varchar(300)");
                
                builder.HasOne(b => b.Empresa).WithMany().HasForeignKey(b => b.EmpresaId);
                builder.HasOne(b => b.GrupoEmpresa).WithMany().HasForeignKey(b => b.GrupoEmpresaId);
            }
        }
    }
}