﻿using System;
using System.Collections.Generic;
using System.Linq;
using System.Linq.Dynamic.Core;
using System.Threading.Tasks;
using AutoMapper.QueryableExtensions;
using Microsoft.EntityFrameworkCore;
using NLog;
using SistemaInfo.BBC.Application.Interface.Mensagem;
using SistemaInfo.BBC.Application.Objects.Base;
using SistemaInfo.BBC.Application.Objects.Web.Mensagem;
using SistemaInfo.BBC.Domain.External.CIOT.DTO;
using SistemaInfo.BBC.Domain.Helper;
using SistemaInfo.BBC.Domain.Models.Mensagem.Commands;
using SistemaInfo.BBC.Domain.Models.Mensagem.Repository;
using SistemaInfo.Framework.DomainDrivenDesign.Application.Service;
using SistemaInfo.Framework.DomainDrivenDesign.Domain.Engine;
using SistemaInfo.Framework.Utils;

namespace SistemaInfo.BBC.Application.Services.Mensagem
{
    public class MensagemAppService : AppService<Domain.Models.Mensagem.Mensagem,
        IMensagemReadRepository, IMensagemWriteRepository>, IMensagemAppService
    {

        public MensagemAppService(
            IAppEngine engine,
            IMensagemReadRepository readRepository,
            IMensagemWriteRepository writeRepository) : base(
            engine, readRepository, writeRepository)
        {
        }

        public ConsultarGridMensagemResponse ConsultarGridMensagem(ConsultarGridMensagemRequest request)
        {
         
            IQueryable<Domain.Models.Mensagem.Mensagem> lMensagem;
            
            lMensagem = Repository.Query.GetAll();

            if (request.Status != null && request.Status != 100)
            {
                lMensagem = lMensagem.Where(p => p.MensagemTratada == request.Status);
            }

            if (!request.Mensagem.IsNullOrWhiteSpace())
            {
                lMensagem = lMensagem.Where(m => m.TextoMensagemOriginal.ToLower().Contains(request.Mensagem.ToLower()) 
                                                 || m.TextoMensagemPadrao.ToLower().Contains(request.Mensagem.ToLower()) 
                                                 || m.TextoMensagem.ToLower().Contains(request.Mensagem.ToLower()));
            }   
            
            lMensagem = lMensagem.AplicarFiltrosDinamicos(request.Filters);
            lMensagem = string.IsNullOrWhiteSpace(request.Order?.Campo)
                ? lMensagem.OrderByDescending(o => o.Id)
                : lMensagem.OrderBy($"{request.Order?.Campo} {request.Order?.Operador.DescriptionAttr()}");

            var lCount = lMensagem.Count();
            var retorno = lMensagem.Skip((request.Page - 1) * request.Take).Take(request.Take).ProjectTo<ConsultarGridMensagem>(Engine.Mapper.ConfigurationProvider).ToList();

            return new ConsultarGridMensagemResponse
            {
                items = retorno,
                totalItems = lCount
            };
        }

        public MensagemResponse ConsultarPorId(int idMensagem)
        {
            return Mapper.Map<MensagemResponse>(Repository.Query
                .FirstOrDefault(a => a.Id == idMensagem));
        }

        
        public async Task<RespPadrao> Save(MensagemRequest mensagemReq, bool integracao)
        {
            try
            {
                var lMensagemExistente = await Repository.Query.AsNoTracking().FirstOrDefaultAsync(m => m.TextoMensagemOriginal == mensagemReq.TextoMensagemOriginal);
                
                if (lMensagemExistente != null)
                {
                    if (mensagemReq.Id <= 0 && lMensagemExistente.TextoMensagemOriginal != null)
                    {
                        return new RespPadrao
                        {
                            sucesso = false,
                            mensagem = "Mensagem já existente!"
                        }; 
                    }
                }

                var command = Mapper.Map<MensagemSalvarComRetornoCommand>(mensagemReq);
                var retorno = await Engine.CommandBus.SendCommandAsync<Domain.Models.Mensagem.Mensagem>(command);

                return new RespPadrao
                {
                    id = retorno.Id,
                    sucesso = true,
                    mensagem = "Registro salvo com sucesso!"
                };
            }
            catch (Exception e)
            {
                return new RespPadrao()
                {
                    sucesso = false,
                    mensagem = e.Message
                };
            }
        }

        public async Task<RespRegistroMensagem> RegistraMensagem(MensagemRequest request)
        {
            var lLog = LogManager.GetCurrentClassLogger();
            try
            {
                // Verifica se existe alguma mensagem com esse texto já cadastrada no banco
                var lMensagem = Repository.Query.FirstOrDefault(x => x.TextoMensagemOriginal == request.TextoMensagemOriginal);
                
                if (lMensagem == null)
                {
                    // Cadastra nova mensagem e retorna
                    var command = Mapper.Map<MensagemAdicionarComRetornoCommand>(request);
                    var retorno = await Engine.CommandBus.SendCommandAsync<Domain.Models.Mensagem.Mensagem>(command);

                    return new RespRegistroMensagem
                    {
                        data = retorno,
                        novaMensagem = true,
                        sucesso = true
                    };
                }
                
                // Retorna mensagem ja existente
                return new RespRegistroMensagem
                {
                    sucesso = true,
                    data = lMensagem,
                    novaMensagem = false
                };
            }
            catch (Exception e)
            {
                lLog.Error(e);
                return new RespRegistroMensagem
                {
                    sucesso = false,
                    mensagem = e.Message
                };
            }
        }

        public async Task<RespPadrao> AlterarStatus(MensagemStatusRequest request)
        {
            try
            {
                var command = Mapper.Map<MensagemAlterarStatusCommand>(request);
                var retorno = await Engine.CommandBus.SendCommandAsync<Domain.Models.Mensagem.Mensagem>(command);
                return new RespPadrao(true, $"Mensagem {(retorno.Ativo == 1 ? "ativada" : "inativada")} com sucesso.");
            }
            catch (Exception e)
            {
                LogManager.GetCurrentClassLogger().Error(e);
                return new RespPadrao(false, "Erro ao alterar status da Mensagem: " + e.Message);
            }
        }
    }
}
