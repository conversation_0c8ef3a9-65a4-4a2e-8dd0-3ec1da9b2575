using System;
using System.Collections.Generic;
using System.Linq;
using System.Threading.Tasks;
using SistemaInfo.BBC.Application.Interface.Documento;
using SistemaInfo.BBC.Application.Objects.Api.Documento;
using SistemaInfo.BBC.Domain.Enum;
using SistemaInfo.BBC.Domain.External.Conductor.Interface;
using SistemaInfo.BBC.Domain.Models.Documento.Commands;
using SistemaInfo.BBC.Domain.Models.Documento.Repository;
using SistemaInfo.BBC.Domain.Models.Portador.Repository;
using SistemaInfo.Framework.DomainDrivenDesign.Application.Service;
using SistemaInfo.Framework.DomainDrivenDesign.Domain.Engine;
using SistemaInfo.Framework.Utils;

namespace SistemaInfo.BBC.Application.Services.Documento
{
    public class DocumentoAppService : AppService<Domain.Models.Documento.Documento, 
        IDocumentoReadRepository,IDocumentoWriteRepository>,IDocumentoAppService
    {
        private ICartaoRepository _cartaoRepository;
        private IPortadorReadRepository _portadorReadRepository;
        
        public DocumentoAppService(
            IAppEngine engine, 
            IDocumentoReadRepository readRepository, 
            IDocumentoWriteRepository writeRepository,
            ICartaoRepository cartaoRepository,
            IPortadorReadRepository portadorReadRepository) 
            : base(engine, readRepository, writeRepository)
        {
            _cartaoRepository = cartaoRepository;
            _portadorReadRepository = portadorReadRepository;
        }

        public async Task<DocumentoResponse> EnviarImagem(DocumentoRequest request)
        {
            try
            {
                var lRetSalvarDocumento = await SalvarDocumento(request);

                if (lRetSalvarDocumento.Sucesso)
                    await SetarDocumentoEnviadoBBC(request.CpfCnpjPortador);
                else
                    await ExcluirDocumentoFalhaEnvioBBC(request.CpfCnpjPortador);

                return lRetSalvarDocumento;
            }
            catch (Exception e)
            {
                return new DocumentoResponse
                {
                    Sucesso = false,
                    message = e.Message
                };
            }
        }
        
        private async Task<DocumentoResponse> SalvarDocumento(DocumentoRequest request)
        {
            try
            {
                var portador = _portadorReadRepository.FirstOrDefault(x => x.CpfCnpj == request.CpfCnpjPortador);
                if (portador != null)
                {
                    #region Verifica se Portador existe na Conductor
                    var portadorConductor = _cartaoRepository.ConsultarPortador(portador.CpfCnpj);
                    if (portadorConductor == null ||
                        portadorConductor?.Result?.Sucesso == false || 
                        (portadorConductor?.Result?.results == null && portadorConductor?.Result?.items == null))
                    {
                        return new DocumentoResponse
                        {
                            Sucesso = false,
                            message = "[BaaS]Não foi encontrado um portador com o CPF/CNPJ informado."
                        };
                    }
                    var idRegistration = portadorConductor.Result.items != null
                        ? portadorConductor.Result.items.First().idRegistration
                        : portadorConductor.Result.results?.First().idRegistration;
                    #endregion

                    #region Salvar documentos no BBC Digital
                    request.IdPortador = portador.Id;
                    var lSalvarDocumentoRequest = await SalvarDocumentoBBC(request);
                    if (!lSalvarDocumentoRequest.Sucesso)
                    {
                        return new DocumentoResponse
                        {
                            Sucesso = false,
                            message = "Falha ao salvar documentos no BBC Digital."
                        };
                    }
                    #endregion

                    #region Enviar Documento à Conductor
                    var documentosPortador = Repository.Query.Where(x => x.PortadorId == request.IdPortador && x.EnviadoBBC == false);
                    if (documentosPortador == null)
                    {
                        return new DocumentoResponse
                        {
                            Sucesso = false,
                            message = "Não há documentos para envio à Conductor."
                        };
                    }

                    foreach (var documento in documentosPortador)
                    {
                        var lRet = await EnviarImagemConductor(idRegistration, portador.TipoPessoa.GetHashCode(), documento.Tipo.ToInt(),
                            documento.Foto);
                        
                        if (!lRet.Sucesso)
                        {
                            return new DocumentoResponse
                            {
                                Sucesso = false,
                                message = "Falha ao enviar os documentos para a Conductor!"
                            };
                        }
                    }
                    #endregion

                    return new DocumentoResponse
                    {
                        Sucesso = true
                    };
                }
                return new DocumentoResponse
                {
                    Sucesso = false,
                    message = "Não foi encontrado um portador com o CPF/CNPJ informado."
                };
            }
            catch (Exception e)
            {
                return new DocumentoResponse
                {
                    Sucesso = false,
                    message = "Não foi possível realizar a operação. Mensagem: " + e.Message
                };
            }
        }
        
        private async Task<DocumentoResponse> SalvarDocumentoBBC(DocumentoRequest request)
        {
            try
            {
                #region Validação de Portador
                if (request == null || !(request.IdPortador > 0))
                {
                    return new DocumentoResponse
                    {
                        Sucesso = false,
                        message = "Não foi possível realizar a operação."
                    };
                }
                #endregion

                #region Adição de Imagens do Portador
                if (request.Imagens.Count > 0)
                {
                    foreach (var foto in request.Imagens)
                    {
                        var lDocumento = new DocumentoSalvarCommand
                        {
                            Tipo = foto.TipoImagem,
                            Foto = foto.Foto,
                            PortadorId = request.IdPortador ?? 0,
                            DataCadastro = DateTime.Now,
                            EnviadoBBC = false
                        };
                        lDocumento.ValidarCadastro();
                        await Engine.CommandBus.SendCommandAsync(lDocumento);
                    }
                }
                #endregion

                #region Adição de Imagens dos Representantes Legais do Portador
                if (request.DocumentosRepLegais.Count > 0)
                {
                    foreach (var repLegal in request.DocumentosRepLegais)
                    {
                        foreach (var fotoRepLegal in repLegal.Imagens)
                        {
                            var lDocumento = new DocumentoSalvarCommand
                            {
                                Tipo = fotoRepLegal.TipoImagem,
                                Foto = fotoRepLegal.Foto,
                                PortadorId = request.IdPortador ?? 0,
                                DataCadastro = DateTime.Now,
                                EnviadoBBC = false
                            };
                            lDocumento.ValidarCadastro();
                            await Engine.CommandBus.SendCommandAsync(lDocumento);
                        }
                    }
                }
                #endregion
                
                return new DocumentoResponse
                {
                    Sucesso = true,
                    message = "Operação realizada com sucesso!"
                };
            }
            catch (Exception e)
            {
                return new DocumentoResponse
                {
                    Sucesso = false,
                    message = "Não foi possível realizar a operação. Mensagem: " + e.Message
                };
            }
        }
        
        private async Task<DocumentoResponse> EnviarImagemConductor(string idRegistration, int tipoPessoa, int tipoImagem, string foto)
        {
            try
            {
                string result = "";
                if (ETipoPessoa.Juridica.ToInt().Equals(tipoPessoa))
                {
                    #region Pessoa Jurídica
                    if (tipoImagem == ETipoDocumento.CCMEI.ToInt())
                        result = await _cartaoRepository.EnviarImagemPessoaJuridica(idRegistration,
                            GetDescricaoEnumTipoDoc(tipoImagem), Convert.FromBase64String(foto));
                    if (tipoImagem == ETipoDocumento.EI_REGISTRATION_REQUIREMENT.ToInt())
                        result = await _cartaoRepository.EnviarImagemPessoaJuridica(idRegistration,
                            GetDescricaoEnumTipoDoc(tipoImagem), Convert.FromBase64String(foto));
                    if (tipoImagem == ETipoDocumento.ARTICLES_OF_ASSOCIATION.ToInt())
                        result = await _cartaoRepository.EnviarImagemPessoaJuridica(idRegistration,
                            GetDescricaoEnumTipoDoc(tipoImagem), Convert.FromBase64String(foto));
                    if (tipoImagem == ETipoDocumento.EIRELI_INCORPORATION_STATEMENT.ToInt())
                        result = await _cartaoRepository.EnviarImagemPessoaJuridica(idRegistration,
                            GetDescricaoEnumTipoDoc(tipoImagem), Convert.FromBase64String(foto));
                    if (tipoImagem == ETipoDocumento.COMPANY_BYLAWS.ToInt())
                        result = await _cartaoRepository.EnviarImagemPessoaJuridica(idRegistration,
                            GetDescricaoEnumTipoDoc(tipoImagem), Convert.FromBase64String(foto));
                    #endregion

                    #region Representante Legal
                    if (tipoImagem == ETipoDocumento.SELFIE.ToInt())
                        result = await _cartaoRepository.EnviarImagemPessoaJuridica(idRegistration,
                            GetDescricaoEnumTipoDoc(tipoImagem), Convert.FromBase64String(foto));
                    if (tipoImagem == ETipoDocumento.IDENTITY_CARD_FRONT.ToInt())
                        result = await _cartaoRepository.EnviarImagemPessoaJuridica(idRegistration,
                            GetDescricaoEnumTipoDoc(tipoImagem), Convert.FromBase64String(foto));
                    if (tipoImagem == ETipoDocumento.IDENTITY_CARD_VERSE.ToInt())
                        result = await _cartaoRepository.EnviarImagemPessoaJuridica(idRegistration,
                            GetDescricaoEnumTipoDoc(tipoImagem), Convert.FromBase64String(foto));
                    if (tipoImagem == ETipoDocumento.DRIVER_LICENSE_FRONT.ToInt())
                        result = await _cartaoRepository.EnviarImagemPessoaJuridica(idRegistration,
                            GetDescricaoEnumTipoDoc(tipoImagem), Convert.FromBase64String(foto));
                    if (tipoImagem == ETipoDocumento.DRIVER_LICENSE_VERSE.ToInt())
                        result = await _cartaoRepository.EnviarImagemPessoaJuridica(idRegistration,
                            GetDescricaoEnumTipoDoc(tipoImagem), Convert.FromBase64String(foto));
                    #endregion
                    
                }

                if (ETipoPessoa.Fisica.ToInt().Equals(tipoPessoa))
                {
                    if (tipoImagem == ETipoDocumento.SELFIE.ToInt())
                        result = await _cartaoRepository.EnviarImagemPessoaFisica(idRegistration,
                            GetDescricaoEnumTipoDoc(tipoImagem), Convert.FromBase64String(foto));
                    if (tipoImagem == ETipoDocumento.IDENTITY_CARD_FRONT.ToInt())
                        result = await _cartaoRepository.EnviarImagemPessoaFisica(idRegistration,
                            GetDescricaoEnumTipoDoc(tipoImagem), Convert.FromBase64String(foto));
                    if (tipoImagem == ETipoDocumento.IDENTITY_CARD_VERSE.ToInt())
                        result = await _cartaoRepository.EnviarImagemPessoaFisica(idRegistration,
                            GetDescricaoEnumTipoDoc(tipoImagem), Convert.FromBase64String(foto));
                    if (tipoImagem == ETipoDocumento.DRIVER_LICENSE_FRONT.ToInt())
                        result = await _cartaoRepository.EnviarImagemPessoaFisica(idRegistration,
                            GetDescricaoEnumTipoDoc(tipoImagem), Convert.FromBase64String(foto));
                    if (tipoImagem == ETipoDocumento.DRIVER_LICENSE_VERSE.ToInt())
                        result = await _cartaoRepository.EnviarImagemPessoaFisica(idRegistration,
                            GetDescricaoEnumTipoDoc(tipoImagem), Convert.FromBase64String(foto));
                }

                if (string.IsNullOrEmpty(result))
                {
                    return new DocumentoResponse
                    {
                        Sucesso = false,
                        message = "Não foi possível enviar as imagens para a conductor."
                    };  
                }
                return new DocumentoResponse
                {
                    Sucesso = true
                };
            }
            catch (Exception e)
            {
                return new DocumentoResponse
                {
                    Sucesso = false,
                    message = "Não foi possível enviar as imagens para conductor. Motivo: " + e.Message
                };
            }
        }

        private async Task<DocumentoResponse> ExcluirDocumentoFalhaEnvioBBC(string aCpfCnpjPortador)
        {
            try
            {
                var portador = _portadorReadRepository.FirstOrDefault(x => x.CpfCnpj == aCpfCnpjPortador);
                var lIdPortador = portador?.Id ?? 0;
                var lListDocumentos = Repository.Query.Where(x => x.PortadorId == lIdPortador && x.EnviadoBBC == false);
                List<int> lListIdDocumentos = new List<int>();
                foreach (var documento in lListDocumentos)
                {
                    lListIdDocumentos.Add(documento.Id);
                }
                foreach (var idDocumento in lListIdDocumentos)
                {
                    var lDocumentoExcluirCommand = new DocumentoExcluirCommand{Id = idDocumento};
                    await Engine.CommandBus.SendCommandAsync(lDocumentoExcluirCommand);
                }
                
                return new DocumentoResponse
                {
                    Sucesso = true,
                    message = "Operação realizada com sucesso!"
                };
            }
            catch (Exception e)
            {
                return new DocumentoResponse
                {
                    Sucesso = false,
                    message = "Não foi possível realizar a operação. Mensagem: " + e.Message
                };
            }
        }
        
        private async Task<DocumentoResponse> SetarDocumentoEnviadoBBC(string aCpfCnpjPortador)
        {
            try
            {
                var portador = _portadorReadRepository.FirstOrDefault(x => x.CpfCnpj == aCpfCnpjPortador);
                var lIdPortador = portador?.Id ?? 0;
                var lListDocumentos = Repository.Query.Where(x => x.PortadorId == lIdPortador && x.EnviadoBBC == false);
                List<int> lListIdDocumentos = new List<int>();
                foreach (var documento in lListDocumentos)
                {
                    lListIdDocumentos.Add(documento.Id);
                }
                foreach (var idDocumento in lListIdDocumentos)
                {
                    var lDocumentoSetarEnviadoBBCCommand = new DocumentoSetarEnviadoBBCCommand{Id = idDocumento};
                    await Engine.CommandBus.SendCommandAsync(lDocumentoSetarEnviadoBBCCommand);
                }

                return new DocumentoResponse
                {
                    Sucesso = true,
                    message = "Operação realizada com sucesso!"
                };
            }
            catch (Exception e)
            {
                return new DocumentoResponse
                {
                    Sucesso = false,
                    message = "Não foi possível realizar a operação. Mensagem: " + e.Message
                };
            }
        }

        private string GetDescricaoEnumTipoDoc(int tipo)
        {
            switch (tipo)
            {
                case 0:
                    return ETipoDocumento.DRIVER_LICENSE_FRONT.GetDescription();
                case 1:
                    return ETipoDocumento.SELFIE.GetDescription();
                case 2:
                    return ETipoDocumento.DRIVER_LICENSE_VERSE.GetDescription();
                case 3:
                    return ETipoDocumento.IDENTITY_CARD_FRONT.GetDescription();
                case 4:
                    return ETipoDocumento.IDENTITY_CARD_VERSE.GetDescription();
                case 5:
                    return ETipoDocumento.SIGNATURE_CARD.GetDescription();
                case 10:
                    return ETipoDocumento.CCMEI.GetDescription();
                case 11:
                    return ETipoDocumento.EI_REGISTRATION_REQUIREMENT.GetDescription();
                case 12:
                    return ETipoDocumento.ARTICLES_OF_ASSOCIATION.GetDescription();
                case 13:
                    return ETipoDocumento.EIRELI_INCORPORATION_STATEMENT.GetDescription();
                case 14:
                    return ETipoDocumento.COMPANY_BYLAWS.GetDescription();
            }

            return "";
        }
    }
}