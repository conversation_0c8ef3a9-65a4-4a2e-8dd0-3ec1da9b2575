using System;
using System.Collections.Generic;
using System.Linq;
using System.Linq.Dynamic.Core;
using System.Threading.Tasks;
using AutoMapper.QueryableExtensions;
using NLog;
using SistemaInfo.BBC.Application.Interface.Fabricante;
using SistemaInfo.BBC.Application.Objects.Base;
using SistemaInfo.BBC.Application.Objects.Web.Fabricante;
using SistemaInfo.BBC.Domain.Grid;
using SistemaInfo.BBC.Domain.Helper;
using SistemaInfo.BBC.Domain.Models.Fabricante.Commands;
using SistemaInfo.BBC.Domain.Models.Fabricante.Repository;
using SistemaInfo.Framework.DomainDrivenDesign.Application.Service;
using SistemaInfo.Framework.DomainDrivenDesign.Domain.Engine;

namespace SistemaInfo.BBC.Application.Services.Fabricante
{
    public class FabricanteAppService : AppService<Domain.Models.Fabricante.Fabricante,
        IFabricanteReadRepository, IFabricanteWriteRepository>, IFabricanteAppService
    {

        public FabricanteAppService(
            IAppEngine engine,
            IFabricanteReadRepository readRepository,
            IFabricanteWriteRepository writeRepository) : base(
            engine, readRepository, writeRepository)
        {
        }

        public ConsultarGridFabricanteResponse ConsultarGridFabricante(int take, int page, OrderFilters orderFilters,
            List<QueryFilters> filters)
        {
         
            IQueryable<Domain.Models.Fabricante.Fabricante> lFabricante;

            lFabricante = Repository.Query.GetAll();

            lFabricante = lFabricante.AplicarFiltrosDinamicos<Domain.Models.Fabricante.Fabricante>(filters);
            lFabricante = string.IsNullOrWhiteSpace(orderFilters?.Campo)
                ? lFabricante.OrderByDescending(o => o.Id)
                : lFabricante.OrderBy($"{orderFilters.Campo} {orderFilters.Operador.DescriptionAttr()}");

            var lCount = lFabricante.Count();
            var retorno = lFabricante.Skip((page - 1) * take)
                .Take(take).ProjectTo<ConsultarGridFabricante>(Engine.Mapper.ConfigurationProvider).ToList();

            return new ConsultarGridFabricanteResponse
            {
                items = retorno,
                totalItems = lCount
            };
        }

        public FabricanteResponse ConsultarPorId(int idFabricante)
        {
            if (idFabricante <= 0)
            {
                throw new Exception("ID inválido!");
            }
            return Mapper.Map<FabricanteResponse>(Repository.Query
                .FirstOrDefault(a => a.Id == idFabricante));
        }
        
        public async Task<RespPadrao> Save(FabricanteRequest request)
        {
            try
            {
                var command = Mapper.Map<FabricanteSalvarComRetornoCommand>(request);
                await Engine.CommandBus.SendCommandAsync<Domain.Models.Fabricante.Fabricante>(command);
                return new RespPadrao(true, "Fabricante salva com sucesso.");
            }
            catch (Exception e)
            {
                LogManager.GetCurrentClassLogger().Error(e);
                return new RespPadrao(false, "Ocorreu um erro ao salvar a Fabricante: " + e.Message);
            }
        }

        public async Task<RespPadrao> AlterarStatus(FabricanteStatusRequest request)
        {
            try
            {
                var command = Mapper.Map<FabricanteAlterarStatusCommand>(request);
                var retorno = await Engine.CommandBus.SendCommandAsync<Domain.Models.Fabricante.Fabricante>(command);
                return new RespPadrao(true, $"Fabricante {(retorno.Ativo == 1 ? "ativada" : "inativada")} com sucesso.");
            }
            catch (Exception e)
            {
                LogManager.GetCurrentClassLogger().Error(e);
                return new RespPadrao(false, "Ocorreu um erro ao alterar status da Fabricante: " + e.Message);
            }
        }
    }
}
