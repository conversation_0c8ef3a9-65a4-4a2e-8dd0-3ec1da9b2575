﻿using System;
using System.Globalization;
using System.Text;
using System.Text.RegularExpressions;
using SistemaInfo.Framework.Utils;

namespace SistemaInfo.BBC.Domain.Helper
{
    public static class StringHelper
    {
        /// <summary>
        /// Formatar o valor da placa
        /// </summary>
        /// <param name="placa">Placa a ser formatada</param>
        /// <returns></returns>
        public static string FormatarPlaca(string placa)
        {
            return $"{placa.Substring(0, 3)}-{placa.Substring(3, 4)}";
        }

        public static string ToPlacaFormato(this string placa)
        {
            if (placa.Length < 7 || placa.Length > 8)
                return placa;
            return $"{placa.Substring(0, 3)}-{placa.Substring(3, 4)}";
        }

        public static string ToCNPJFormato(this string cnpj)
        {
            try
            {
                return !string.IsNullOrWhiteSpace(cnpj) ? Convert.ToUInt64(cnpj).ToString(@"00\.000\.000\/0000\-00") : "";
            }  
            catch (Exception ex)
            {
                return $"Erro ao formatar o número de cnpj: {ex.Message}";
            }
        }

        public static string ToCPFFormato(this string cpf)
        {
            try
            {
                return !string.IsNullOrWhiteSpace(cpf) ? Convert.ToUInt64(cpf).ToString(@"000\.000\.000\-00") : "";
            }  
            catch (Exception ex)
            {
                return $"Erro ao formatar o número de cpf: {ex.Message}";
            }

           
        }
        
        
        public static string ToTelefoneFormatoNovo(this string numero)
        {
            if (string.IsNullOrWhiteSpace(numero) || !long.TryParse(numero, out long numeroLong))
            {
                return numero;
            }

            // Caso o número tenha o formato de telefone fixo (10 dígitos)
            if (numero.Length == 10)
            {
                return String.Format("{0:(##) ####-####}", numeroLong);
            }
            
            if (numero.Length == 11 && numero.StartsWith("9"))
            {
                return String.Format("{0:(##) #####-####}", numeroLong);
            }
            
            if (numero.Length == 11 && numero.StartsWith("0800"))
            {
                return String.Format("{0:#### ### ####}", numeroLong);
            }
            
            if (numero.Length == 10 && numero.StartsWith("0300"))
            {
                return String.Format("{0:#### ### ####}", numeroLong);
            }
            
            if (numero.Length == 8 && (numero.StartsWith("4000") || numero.StartsWith("0900")))
            {
                return String.Format("{0:#### ####}", numeroLong);
            }

            // Se não conseguir identificar o formato, retorna o número original.
            return numero;
        }


        public static string ToTelefoneFormato(this string numero)
        {
            long numeroLong = 0;
            if (string.IsNullOrWhiteSpace(numero) || numero.Length <= 10 || !long.TryParse(numero, out numeroLong))
            {
                return numero;
            }
            return String.Format("{0:(##) #####-####}", numeroLong);
        }

        public static string ToCEPFormato(this string cep)
        {
            try
            {
                if (string.IsNullOrEmpty(cep))
                    return "";

                return Convert.ToUInt64(cep).ToString(@"00000\-000");
            }  
            catch (Exception ex)
            {
                return $"Erro ao formatar o número de cep: {ex.Message}";
            }
        }

        public static string ToTELFormato(this string tel)
        {
            try
            {
                if (string.IsNullOrWhiteSpace(tel))
                    return tel;

                if (tel.Length < 8 || tel.Length > 11)
                    return tel;

                switch (tel.Length)
                {
                    case 8:
                        return Convert.ToUInt64(tel).ToString(@"0000\-0000");
                    case 9:
                        return Convert.ToUInt64(tel).ToString(@"00000\-0000");
                    case 10:
                        return Convert.ToUInt64(tel).ToString(@"\(00\)0000\-0000");
                    case 11:
                        return Convert.ToUInt64(tel).ToString(@"\(00\)00000\-0000");
                    default:
                        return Convert.ToUInt64(tel).ToString(@"\(00\)00000\-0000");
                }
            }  
            catch (Exception ex)
            {
                return $"Erro ao formatar o número de telefone: {ex.Message}";
            }
        }

        public static string ToTELFormatoWithSpace(this string tel)
        {
            try
            {
                if (string.IsNullOrWhiteSpace(tel))
                    return tel;

                switch (tel.Length)
                {
                    case 8:
                        return Convert.ToUInt64(tel).ToString(@"0000\-0000");
                    case 9:
                        return Convert.ToUInt64(tel).ToString(@"00000\-0000");
                    case 10:
                        return Convert.ToUInt64(tel).ToString(@"\(00\) 0000\-0000");
                    case 11:
                        return Convert.ToUInt64(tel).ToString(@"\(00\) 00000\-0000");
                    default:
                        return Convert.ToUInt64(tel).ToString(@"\(00\) 00000\-0000");
                }
            }
            catch (Exception ex)
            {
                return $"Erro ao formatar o número de telefone: {ex.Message}";
            }
        }

        public static bool Contem(this string str, string valor)
        {
            if (!str.IsNullOrWhiteSpace())
                return str.StartsWith(valor) || str.EndsWith(valor) || str.Contains(valor);

            return true;
        }

        /// <summary>
        /// Formatar string para a máscara de CPF ou CNPJ
        /// </summary>
        /// <param name="CpfCnpj">String contendo o valor a ser formatado</param>
        /// <param name="exceptionIfUnexpected">Se o valor indicador possuir menos de 11 caracteres, executa exceção</param>
        /// <returns></returns>
        public static string FormatarCpfCnpj(this string CpfCnpj, bool exceptionIfUnexpected = true)
        {
            if (!string.IsNullOrWhiteSpace(CpfCnpj))
            {
                CpfCnpj = CpfCnpj.Replace("-", "").Replace(".", "").Replace(@"/", "");
                if (CpfCnpj.Length < 11 && exceptionIfUnexpected)
                    throw new Exception("CNPJ/CPF com valor inválido.");

                return string.Format(CpfCnpj.Length < 14
                    ? @"{0:000\.000\.000\-00}" : @"{0:00\.000\.000\/0000\-00}", long.Parse(CpfCnpj));
            }

            return string.Empty; 
        }

        public static string ParaFormatoBrasileiroStr(this DateTime dt)
        {
            return dt.ToString("dd/MM/yyyy HH:mm");
        }

        public static string RemoveDiacritics(this string s)
        {
            var normalizedString = s.Normalize(NormalizationForm.FormD);
            StringBuilder stringBuilder = new StringBuilder();

            foreach (char c in normalizedString)
            {
                if (CharUnicodeInfo.GetUnicodeCategory(c) != UnicodeCategory.NonSpacingMark)
                    stringBuilder.Append(c);
            }

            return stringBuilder.ToString();
        }

        public static double ConvertStringTimeSpanToMinutes(this string time)
        {
            try
            {

                if (string.IsNullOrWhiteSpace(time))
                    return 0;

                double minuto = 0;
                var splitHoraExtra = time.Split(':');
                double hora = Convert.ToInt32(splitHoraExtra[0]);
                minuto += Convert.ToInt32(splitHoraExtra[1]);

                minuto += hora * 60;

                return minuto;
            }
            catch (Exception)
            {
                return 0;
            }
        }

        
        public static decimal MonetaryStringToDecimal(this string valor)
        {
            if (string.IsNullOrWhiteSpace(valor))
                throw new ArgumentException("O valor não pode ser nulo ou vazio.");

            // Remover "R$", espaços extras e separadores de milhares
            valor = valor.Replace("R$", "").Replace(".", "").Trim();

            // Converter para decimal usando cultura brasileira
            return decimal.Parse(valor, new CultureInfo("pt-BR"));
        }
        
        public static string ToCpfOrCnpj(this string value)
        {
            switch (value.Length)
            {
                case 11:
                    return ToCPFFormato(value);
                case 14:
                    return ToCNPJFormato(value);
            }

            return string.Empty;
        }

        /// <summary>
        /// Formatar dinheiro R$ 0.000,00
        /// </summary>
        /// <param name="value"></param>
        /// <returns></returns>
        public static string FormatMoney(this decimal value)
        {
            return value.ToString("C2", CultureInfo.GetCultureInfo("pt-BR"));
        }
        
        /// <summary>
        /// Formatar dinheiro 0.000,00
        /// </summary>
        /// <param name="value"></param>
        /// <returns></returns>
        public static string FormatMonetario(this decimal value)
        {
            return value.ToString("F2", CultureInfo.GetCultureInfo("pt-BR"));
        }

//
//        /// <summary>
//        /// Formatar dinheiro R$ 0.000,00
//        /// </summary>
//        /// <param name="value"></param>
//        /// <returns></returns>
//        public static string FormatMoney(this decimal? value)
//        {
//            return value?.FormatMoney();
//        }

        public static string GetAutonomiaBateriaFormatada(TimeSpan? autonomia)
        {
            var tempoFormatado = string.Empty;

            if (!autonomia.HasValue)
                return string.Empty;

            if (autonomia.Value.Days > 0)
                if (autonomia.Value.Days == 1)
                    tempoFormatado += $"{autonomia.Value.Days} dia";
                else
                    tempoFormatado += $"{autonomia.Value.Days} dias";

            if (autonomia.Value.Hours > 0)
                if (autonomia.Value.Hours < 10)
                    tempoFormatado += $" 0{autonomia.Value.Hours}h";
                else
                    tempoFormatado += $" {autonomia.Value.Hours}h";
            else
                tempoFormatado += " 00h";

            if (autonomia.Value.Minutes > 0)
                if (autonomia.Value.Minutes < 10)
                    tempoFormatado += $"0{autonomia.Value.Minutes}min.";
                else
                    tempoFormatado += $"{autonomia.Value.Minutes}min.";
            else
                tempoFormatado += "00min.";

            return tempoFormatado;
        }
 
        // o metodo isCPFCNPJ recebe dois parâmetros:
        // uma string contendo o cpf ou cnpj a ser validado
        // e um valor do tipo boolean, indicando se o método irá
        // considerar como válido um cpf ou cnpj em branco.
        // o retorno do método também é do tipo boolean:
        // (true = cpf ou cnpj válido; false = cpf ou cnpj inválido)
        public static bool isCPFCNPJ(string cpfcnpj, bool vazio)
        {
            if (string.IsNullOrEmpty(cpfcnpj))
                return vazio;
            else
            {
                int[] d = new int[14];
                int[] v = new int[2];
                int j, i, soma;
                string Sequencia, SoNumero;

                SoNumero = Regex.Replace(cpfcnpj, "[^0-9]", string.Empty);

                //verificando se todos os numeros são iguais
                if (new string(SoNumero[0], SoNumero.Length) == SoNumero) return false;

                // se a quantidade de dígitos numérios for igual a 11
                // iremos verificar como CPF
                if (SoNumero.Length == 11)
                {
                    for (i = 0; i <= 10; i++) d[i] = Convert.ToInt32(SoNumero.Substring(i, 1));
                    for (i = 0; i <= 1; i++)
                    {
                        soma = 0;
                        for (j = 0; j <= 8 + i; j++) soma += d[j] * (10 + i - j);

                        v[i] = (soma * 10) % 11;
                        if (v[i] == 10) v[i] = 0;
                    }
                    return (v[0] == d[9] & v[1] == d[10]);
                }
                // se a quantidade de dígitos numérios for igual a 14
                // iremos verificar como CNPJ
                else if (SoNumero.Length == 14)
                {
                    Sequencia = "6543298765432";
                    for (i = 0; i <= 13; i++) d[i] = Convert.ToInt32(SoNumero.Substring(i, 1));
                    for (i = 0; i <= 1; i++)
                    {
                        soma = 0;
                        for (j = 0; j <= 11 + i; j++)
                            soma += d[j] * Convert.ToInt32(Sequencia.Substring(j + 1 - i, 1));

                        v[i] = (soma * 10) % 11;
                        if (v[i] == 10) v[i] = 0;
                    }
                    return (v[0] == d[12] & v[1] == d[13]);
                }
                // CPF ou CNPJ inválido se
                // a quantidade de dígitos numérios for diferente de 11 e 14
                else return false;
            }
        }

        public static int GetIdadeDataNascimento(DateTime dataNascimento)
        {
            int idade = DateTime.Now.Year - dataNascimento.Year;
            if (DateTime.Now.DayOfYear < dataNascimento.DayOfYear)
            {
                idade -= 1;
            }

            return idade;
        }
    }
}