using System;
using System.Collections.Generic;
using System.Linq;
using System.Linq.Dynamic.Core;
using System.Text.RegularExpressions;
using System.Threading.Tasks;
using AutoMapper.QueryableExtensions;
using Microsoft.EntityFrameworkCore;
using NLog;
using SistemaInfo.BBC.Application.Interface.Portador;
using SistemaInfo.BBC.Application.Interface.Veiculo;
using SistemaInfo.BBC.Application.Objects.Api.Veiculo;
using SistemaInfo.BBC.Application.Objects.Base;
using SistemaInfo.BBC.Application.Objects.Mobile.Veiculo;
using SistemaInfo.BBC.Application.Objects.Web.Abastecimento;
using SistemaInfo.BBC.Application.Objects.Web.Portador;
using SistemaInfo.BBC.Application.Objects.Web.Veiculo;
using SistemaInfo.BBC.Application.Objects.Web.VeiculoCombustivel;
using SistemaInfo.BBC.Domain.Enum;
using SistemaInfo.BBC.Domain.Grid;
using SistemaInfo.BBC.Domain.Helper;
using SistemaInfo.BBC.Domain.Models.AutorizacaoAbastecimento.Repository;
using SistemaInfo.BBC.Domain.Models.CentroCusto.Repository;
using SistemaInfo.BBC.Domain.Models.Cidade.Repository;
using SistemaInfo.BBC.Domain.Models.Combustivel.Repository;
using SistemaInfo.BBC.Domain.Models.Empresa.Repository;
using SistemaInfo.BBC.Domain.Models.Portador.Commands;
using SistemaInfo.BBC.Domain.Models.Portador.Repository;
using SistemaInfo.BBC.Domain.Models.PortadorCentroCusto.Repository;
using SistemaInfo.BBC.Domain.Models.PostoCombustivel.Repository;
using SistemaInfo.BBC.Domain.Models.Usuario.Repository;
using SistemaInfo.BBC.Domain.Models.Veiculo.Commands;
using SistemaInfo.BBC.Domain.Models.Veiculo.Repository;
using SistemaInfo.BBC.Domain.Models.VeiculoCombustivel.Repository;
using SistemaInfo.BBC.Domain.Models.VeiculoEmpresa.Commands;
using SistemaInfo.BBC.Domain.Models.VeiculoEmpresa.Repository;
using SistemaInfo.Framework.DomainDrivenDesign.Application.Service;
using SistemaInfo.Framework.DomainDrivenDesign.Domain.Engine;
using SistemaInfo.Framework.Utils;

namespace SistemaInfo.BBC.Application.Services.Veiculo
{
    public class 
        VeiculoAppService : AppService<Domain.Models.Veiculo.Veiculo,
        IVeiculoReadRepository, IVeiculoWriteRepository>, IVeiculoAppService
    {
        private readonly IPortadorAppService _portadorAppService;
        private readonly IVeiculoEmpresaReadRepository _veiculoEmpresaReadRepository;
        private readonly IEmpresaReadRepository _empresaReadRepository;
        private readonly ICidadeReadRepository _cidadeReadRepository;
        private readonly ICombustivelReadRepository _combustivelReadRepository;
        private readonly IPortadorReadRepository _portadorReadRepository;
        private readonly ICentroCustoReadRepository _centroCustoReadRepository;
        private readonly IPostoCombustivelReadRepository _postoCombustivelReadRepository;
        private readonly IVeiculoCombustivelReadRepository _veiculoCombustivelReadRepository;
        private readonly IPortadorCentroCustoReadRepository _portadorCentroCustoReadRepository;
        private readonly IAutorizacaoAbastecimentoReadRepository _autorizacaoAbastecimentoReadRepository;
        private readonly IUsuarioReadRepository _usuarioReadRepository;

        public VeiculoAppService(
            IAppEngine engine,
            IVeiculoReadRepository readRepository,
            IVeiculoWriteRepository writeRepository,
            IEmpresaReadRepository empresaReadRepository,
            ICombustivelReadRepository combustivelReadRepository,
            IPortadorReadRepository portadorReadRepository,
            ICentroCustoReadRepository centroCustoReadRepository,
            IPortadorAppService portadorAppService, 
            IVeiculoEmpresaReadRepository veiculoEmpresaReadRepository,
            IPostoCombustivelReadRepository postoCombustivelReadRepository,
            IVeiculoCombustivelReadRepository veiculoCombustivelReadRepository,
            IAutorizacaoAbastecimentoReadRepository autorizacaoAbastecimentoReadRepository,
            IUsuarioReadRepository usuarioReadRepository,
            IPortadorCentroCustoReadRepository portadorCentroCustoReadRepository, ICidadeReadRepository cidadeReadRepository) : base(
            engine, readRepository, writeRepository)
        {
            _portadorAppService = portadorAppService;
            _veiculoEmpresaReadRepository = veiculoEmpresaReadRepository;
            _empresaReadRepository = empresaReadRepository;
            _combustivelReadRepository = combustivelReadRepository;
            _portadorReadRepository = portadorReadRepository;
            _centroCustoReadRepository = centroCustoReadRepository;
            _postoCombustivelReadRepository = postoCombustivelReadRepository;
            _veiculoCombustivelReadRepository = veiculoCombustivelReadRepository;
            _portadorCentroCustoReadRepository = portadorCentroCustoReadRepository;
            _cidadeReadRepository = cidadeReadRepository;
            _autorizacaoAbastecimentoReadRepository = autorizacaoAbastecimentoReadRepository;
            _usuarioReadRepository = usuarioReadRepository;
        }

        public ConsultarGridVeiculoResponse ConsultarGridVeiculoCombo(int proprietarioId, int take, int page,
            OrderFilters orderFilters, List<QueryFilters> filters)
        {
            var lVeiculo = Repository.Query.GetAll().Where(a =>
                a.PortadorProprietarioId == proprietarioId && a.Status == StatusVeiculo.Ativo);

            //Veiculo x empresa
            if (Engine.User.EmpresaId > 0)
            {
                var lVeiculosEmp = _veiculoEmpresaReadRepository.Where(x => x.EmpresaId == Engine.User.EmpresaId)
                    .ToList();

                lVeiculo = lVeiculo.Where(x => lVeiculosEmp.Select(c => c.VeiculoId).Contains(x.Id));
            }

            lVeiculo = lVeiculo.AplicarFiltrosDinamicos<Domain.Models.Veiculo.Veiculo>(filters);
            lVeiculo = string.IsNullOrWhiteSpace(orderFilters?.Campo)
                ? lVeiculo.OrderByDescending(o => o.Id)
                : lVeiculo.OrderBy($"{orderFilters.Campo} {orderFilters.Operador.DescriptionAttr()}");

            var retorno = lVeiculo.Skip((page - 1) * take)
                .Take(take)
                .ProjectTo<ConsultarGridVeiculo>().ToList();

            foreach (var item in retorno)
            {
                item.placa = item.placa.ToPlacaFormato();
            }

            var lCount = lVeiculo.Count();

            return new ConsultarGridVeiculoResponse
            {
                items = retorno,
                totalItems = lCount
            };
        }

        public ConsultarGridVeiculoResponse ConsultarGridVeiculo(int take, int page, OrderFilters orderFilters,
            List<QueryFilters> filters)
        {
            foreach (var item in filters)
                if (item.Campo == "placa")
                    item.Valor = item.Valor.Replace("-", "");

            IQueryable<Domain.Models.Veiculo.Veiculo> lVeiculo;

            lVeiculo = Repository.Query.GetAll()
                .Include(x => x.Portador)
                .Include(x => x.Filial);

            //Veiculo x empresa
            if (Engine.User.EmpresaId > 0)
            {
                lVeiculo = lVeiculo.Where(x => x.EmpresaId.Equals(Engine.User.EmpresaId) || x.EmpresaId == null);
            }
            
            var tipoVeiculoFiltro = filters.Find(f => f.Campo == "TipoVeiculo");
            if (tipoVeiculoFiltro != null)
            {
                var tipos = tipoVeiculoFiltro.Valor.Split(',').Select(int.Parse).ToList();
                lVeiculo = lVeiculo.Where(v => tipos.Contains(v.TipoVeiculo));

                // Remover o filtro da lista para evitar aplicação duplicada
                filters.Remove(tipoVeiculoFiltro);
            }
            
            var filtrarUsuario = filters.Find(q => q.Campo == "FiltrarUsuario")?.Valor == "true";
            filters.Remove(filters.Find(q => q.Campo == "FiltrarUsuario"));

            if (filtrarUsuario)
            {
                var lUser = _usuarioReadRepository.GetUsuarioFull(User.Id);
                if (lUser.UsuarioCentroCusto.Count != 0 && User.EmpresaId > 0)
                {
                    var lCentroCustoUsuario = lUser.UsuarioCentroCusto.Select(x => x.CentroCustoId).ToList();
                    lVeiculo = lVeiculo
                        .Where(a => lCentroCustoUsuario.Contains(a.CentroCustoId.Value));
                }

                if (lUser.UsuarioFilial.Count != 0 && User.EmpresaId > 0)
                {
                    var lFilialUsuario = lUser.UsuarioFilial.Select(x => x.FilialId).ToList();
                    lVeiculo = lVeiculo
                        .Where(a => lFilialUsuario.Contains(a.FilialId.Value));
                }
            }
           
            lVeiculo = lVeiculo.AplicarFiltrosDinamicos(filters);
            lVeiculo = string.IsNullOrWhiteSpace(orderFilters?.Campo)
                ? lVeiculo.OrderByDescending(o => o.Id)
                : lVeiculo.OrderBy($"{orderFilters.Campo} {orderFilters.Operador.DescriptionAttr()}");

            var lCount = lVeiculo.Count();
            var retorno = lVeiculo.Skip((page - 1) * take)
                .Take(take).ProjectTo<ConsultarGridVeiculo>(Engine.Mapper.ConfigurationProvider).ToList();

            foreach (var item in retorno)
            {
                item.placa = item.placa.ToPlacaFormato();
                item.ativo = item.status == StatusVeiculo.Ativo ? 1 : 0;
            }

            return new ConsultarGridVeiculoResponse
            {
                items = retorno,
                totalItems = lCount
            };
        }

        public VeiculoResponse ConsultarPorId(int idVeiculo)
        {
            return Mapper.Map<VeiculoResponse>(Repository.Query
                .Include(a => a.Portador)
                .Include(x => x.Filial)
                .Include(x => x.CentroCusto)
                .Include(x => x.Fabricante)
                .Include(x => x.Modelo)
                .Include(x=> x.VeiculoCombustiveis)
                .ThenInclude(x => x.Combustivel)
                .Include(x => x.CombustivelPreferencial)
                .Include(a => a.Empresa)
                .FirstOrDefault(a => a.Id == idVeiculo));
            
        }

        public async Task<VeiculoResponse> ConsultarPorPlaca(string placa)
        {
            var lVeiculo = await Repository.Query
                .Include(a => a.Portador)
                .FirstOrDefaultAsync(a => a.Placa.ToUpper() == placa.ToUpper());
            return Mapper.Map<VeiculoResponse>(lVeiculo);
        }
        
        public async Task<RespPadrao> ConsultaVeiculoEmpresaPorPlaca(VeiculoRequest lVeiculoReq, bool integracao)
        {
            try
            {
                var lVeiculo = await Repository.Query
                    .FirstOrDefaultAsync(x => x.Placa.ToUpper() == lVeiculoReq.Placa.ToUpper());
                
                var lEmpresaId = Engine.User.EmpresaId != 0 ? Engine.User.EmpresaId : lVeiculo.EmpresaId;

                if (!string.IsNullOrEmpty(lVeiculoReq.Id) && lVeiculoReq.Id != "0")
                {
                    return new RespPadrao
                    {
                        sucesso = true,
                        mensagem = "Veículo disponivel para cadastro",
                        data = lVeiculo
                    };
                }
                
                //Veiculo x empresa
                var lVeiculosEmp = _veiculoEmpresaReadRepository
                    .Include(x => x.Veiculo);
                
                var lCadastradaOutraEmp = lVeiculosEmp
                    .Where(x => x.EmpresaId != lEmpresaId && x.Veiculo.Placa.ToUpper() == lVeiculoReq.Placa.ToUpper())
                    .ToList();
                
                var lJaCadastradoParaEmpresa = lVeiculosEmp
                    .Where(x => x.EmpresaId == lEmpresaId && x.Veiculo.Placa.ToUpper() == lVeiculoReq.Placa.ToUpper())
                    .ToList();
                
                
                if (lCadastradaOutraEmp.Count > 0 && lJaCadastradoParaEmpresa.Count == 0)
                {
                    return new RespPadrao
                    {
                        sucesso = false,
                        mensagem = "Veículo cadastrado para outra empresa.",
                        data = new
                        {
                            CadastradoEmOutraEmpresa = true,
                            Veiculo = Mapper.Map<VeiculoResponse>(Repository.Query.Include(a => a.Portador)
                                .FirstOrDefault(a => a.Placa.ToUpper() == lVeiculoReq.Placa.ToUpper()))
                        }
                    };
                }

                //var lVeiculoCad = ConsultarPorPlaca(lVeiculoReq.Placa).Result;

                if (!lJaCadastradoParaEmpresa.IsEmpty())
                {
                    return new RespPadrao
                    {
                        sucesso = false,
                        mensagem = "Veículo já cadastrado para esta empresa."
                    };
                }
                

                return new RespPadrao
                {
                    sucesso = true,
                    mensagem = "Veículo disponivel para cadastro",
                    data = lVeiculo
                };
            }
            catch (Exception e)
            {
                return new RespPadrao
                {
                    sucesso = false,
                    mensagem = e.Message
                };
            }
        }
        
        public async Task<RespPadrao> Save(VeiculoRequest lVeiculoReq, bool integracao)
        {
            try
            {
                var lEmpresaId = Engine.User.EmpresaId != 0 ? Engine.User.EmpresaId : lVeiculoReq.EmpresaId;
                
                var lVeiculo = Repository.Query.FirstOrDefault(v => v.Renavam == lVeiculoReq.Renavam.Trim());

                if (lVeiculo != null && lVeiculo.Id != (string.IsNullOrEmpty(lVeiculoReq.Id) ? 0 : lVeiculoReq.Id.ToInt()))
                {
                    return new RespPadrao
                    {
                        sucesso = false,
                        mensagem = "Renavam informado já cadastrado."
                    };
                }

                if (lVeiculo != null)
                {
                    Repository.Query.Detach(lVeiculo);
                }

                if (string.IsNullOrEmpty(lVeiculoReq.Id) || lVeiculoReq.Id == "0")
                {
                    var lVeiculoCad = ConsultarPorPlaca(lVeiculoReq.Placa).Result;

                    if (lVeiculoCad != null)
                    {
                        return new RespPadrao()
                        {
                            sucesso = false,
                            mensagem = "Veículo já cadastrado."
                        };
                    }
                }

                if (string.IsNullOrEmpty(lVeiculoReq.Id) || lVeiculoReq.Id == "0")
                {
                    //Veiculo x empresa
                    var lVeiculosEmp = _veiculoEmpresaReadRepository.Include(x => x.Veiculo);
                    var lCadastradaOutraEmp = lVeiculosEmp.Where(x => x.EmpresaId != lEmpresaId && x.Veiculo.Placa.ToUpper() == lVeiculoReq.Placa.ToUpper()).ToList();
                    var lJaCadastradoParaEmpresa = lVeiculosEmp.Where(x => x.EmpresaId == lEmpresaId && x.Veiculo.Placa.ToUpper() == lVeiculoReq.Placa.ToUpper()).ToList();
                    
                    if (lEmpresaId == 0 || lEmpresaId == null)
                        return new RespPadrao
                        {
                            sucesso = false,
                            mensagem = "Empresa não encontrada."
                        };


                    if (lCadastradaOutraEmp.Count > 0 && lJaCadastradoParaEmpresa.Count == 0)
                    {
                        if (!integracao && Engine.User.EmpresaId != 0)
                        {
                            return new RespPadrao
                            {
                                sucesso = false,
                                mensagem = "Veículo cadastrado para outra empresa.",
                                data = new
                                {
                                    CadastradoEmOutraEmpresa = true,
                                    VeiculoId = lCadastradaOutraEmp.Select(x => x.VeiculoId).FirstOrDefault(),
                                    EmpresaId = lEmpresaId
                                }
                            };
                        }

                        await SalvarVeiculoEmpresa(new VeiculoEmpresaRequest
                        {
                            EmpresaId = lEmpresaId.Value,
                            VeiculoId = lCadastradaOutraEmp.Select(x => x.VeiculoId).FirstOrDefault()
                        });

                        return new RespPadrao
                        {
                            sucesso = true,
                            mensagem = "Registro salvo com sucesso!"
                        };
                    }
                }

                var command = Mapper.Map<VeiculoSalvarComRetornoCommand>(lVeiculoReq);
                var retorno = await Engine.CommandBus.SendCommandAsync<Domain.Models.Veiculo.Veiculo>(command);

                if (string.IsNullOrEmpty(lVeiculoReq.Id) || lVeiculoReq.Id == "0")
                {
                   await SalvarVeiculoEmpresa(new VeiculoEmpresaRequest
                   {
                       EmpresaId = lEmpresaId ?? 0,
                       VeiculoId = retorno.Id
                   });
                }

                return new RespPadrao
                {
                    id = retorno.Id,
                    sucesso = true,
                    mensagem = "Registro salvo com sucesso!"
                };
            }
            catch (Exception e)
            {
                return new RespPadrao()
                {
                    sucesso = false,
                    mensagem = e.Message
                };
            }
        }
        
        public async Task<RespPadrao> CadastrarVeiculoCiot(VeiculoCiotRequest veiculoCiotRequest, bool integracao)
        {
            try
            {
                var portadorVeiculo = await _portadorReadRepository.GetByCpfCnpjAsync(veiculoCiotRequest.PortadorProprietarioCpfCnpj.OnlyNumbers());

                if (portadorVeiculo == null)
                {
                    if (!string.IsNullOrWhiteSpace(veiculoCiotRequest.PortadorProprietarioCpfCnpj))
                    {
                        var newPortadorProprietario = await CadastrarPortador(veiculoCiotRequest.PortadorProprietarioCpfCnpj);

                        if (newPortadorProprietario.Id > 0)
                        {
                            veiculoCiotRequest.PortadorProprietarioId = newPortadorProprietario.Id;
                            var retornoCadastroVeiculoSemPortador = await Save(veiculoCiotRequest, false);
                            if (retornoCadastroVeiculoSemPortador.sucesso)
                            {
                                return new RespPadrao(true, "Veículo cadastrado com sucesso!", retornoCadastroVeiculoSemPortador);
                            }
                        }
                    }
                    else
                    {
                        return new RespPadrao( false, "Campo CpfCnpjContratado é obrigatório.");
                    }
                }
                else
                {
                    veiculoCiotRequest.PortadorProprietarioId = portadorVeiculo.Id;
                }
                
                
                var retornoCadastroVeiculoPortadorExistente = await Save(veiculoCiotRequest, false);
                return retornoCadastroVeiculoPortadorExistente.sucesso 
                    ? new RespPadrao(true, "Veículo cadastrado com sucesso!", retornoCadastroVeiculoPortadorExistente) 
                    : new RespPadrao(false, retornoCadastroVeiculoPortadorExistente.mensagem);
            }
            catch (Exception e)
            {
                return new RespPadrao()
                {
                    sucesso = false,
                    mensagem = e.Message
                };
            }
        }
        
        public async Task<Domain.Models.Portador.Portador> CadastrarPortador(string cpfcnpj)
        {
            var lLog = LogManager.GetCurrentClassLogger();

            var lPortadorId = (await _portadorReadRepository.GetIdByCpfCnpj(cpfcnpj)).ToIntSafe();

            if (lPortadorId != 0)
                throw new InvalidOperationException("Não é possível cadastrar um Portador que já existe.");

            lLog.Info($"Portador não cadastrado para CPF/CNPJ: {cpfcnpj}.");

            var nomePortador = "Cadastro Automatico";
            
            var portador = new PortadorRequest();
            var cidade = await _cidadeReadRepository.FirstOrDefaultAsync();
            portador.CpfCnpj = cpfcnpj;
            portador.Bairro = "Bairro Padrão";
            portador.Nome = nomePortador;
            portador.EmpresaId = User.EmpresaId;
            portador.Visibilidade = EVisibilidadePortador.Transportador;
            portador.CidadeId = cidade.Id;
            portador.EstadoId = cidade.EstadoId;
            portador.TipoPessoa = cpfcnpj.Length > 11 ? 2 : 1;

            var command = Mapper.Map<PortadorSalvarComRetornoCommand>(portador);
            var retorno = Engine.CommandBus.SendCommand<Domain.Models.Portador.Portador>(command);

            lLog.Info($"Portador Cadastrado Automaticamente para CPF/CNPJ {cpfcnpj} com Id {retorno.Id}.");

            return retorno;
        }

        public async Task<RespPadraoApi> Integrar(VeiculoIntegrarApiRequest request)
        {
            var veiculoSave = new VeiculoRequest();

            var consultaVeiculo = false;
            
            string validaRenavam = @"^\d+$";
            string validaPlaca = @"^[a-zA-Z]{3}[0-9][A-Za-z0-9][0-9]{2}$";
            
            var matchPlaca = Regex.Match(request.Placa.Replace("-", ""), validaPlaca);
            var matchRenavam = Regex.Match(request.Renavam, validaRenavam);

            if (!matchPlaca.Success)
            {
                return new RespPadraoApi()
                {
                    sucesso = false,
                    mensagem = "Placa informada é inválida."
                }; 
            }
            veiculoSave.Placa = request.Placa.Replace("-", "");

            var veiculoConsultaBanco = Mapper.Map<VeiculoRequest>(Repository.Query.Where(x => x.Placa == veiculoSave.Placa)
                .Include(x=>x.VeiculoCombustiveis)
                .ThenInclude(x=>x.Combustivel).FirstOrDefault());

            if (veiculoConsultaBanco != null)
            {
                veiculoSave = veiculoConsultaBanco;
                consultaVeiculo = true;
            }

            veiculoSave.Id = veiculoSave.Id == "" ? "0" : veiculoSave.Id;
            veiculoSave.Odometro = veiculoSave.Odometro != request.Odometro || veiculoSave.Odometro == null ? request.Odometro : veiculoSave.Odometro;
            veiculoSave.Ano = veiculoSave.Ano != request.Ano ? request.Ano : veiculoSave.Ano;
            veiculoSave.QuantidadeEixos = veiculoSave.QuantidadeEixos != request.QuantidadeEixos || veiculoSave.QuantidadeEixos == null? request.QuantidadeEixos : veiculoSave.QuantidadeEixos;
            veiculoSave.NumeroFrota = veiculoSave.NumeroFrota != request.Frota || veiculoSave.NumeroFrota == null? request.Frota : veiculoSave.NumeroFrota;
            veiculoSave.ControlaAutonomia = veiculoSave.ControlaAutonomia != request.ControlaAutonomia ? request.ControlaAutonomia : veiculoSave.ControlaAutonomia;
            veiculoSave.TipoAbastecimento = veiculoSave.TipoAbastecimento != request.TipoAbastecimento.GetHashCode() || veiculoSave.TipoAbastecimento == null? request.TipoAbastecimento.GetHashCode() : veiculoSave.TipoAbastecimento;

            if (!request.Frota.IsNullOrWhiteSpace())
            {
                var empresaCnpj = _empresaReadRepository.FirstOrDefault(x => x.Id == User.EmpresaId).Cnpj;
                
                var portador = _portadorAppService.ConsultarPorCpfCnpj(empresaCnpj);

                if (portador == null)
                {
                    return new RespPadraoApi()
                    {
                        sucesso = false,
                        mensagem = "Favor cadastrar um proprietario, no cadastro de portador para empresa de CNPJ "+empresaCnpj+"."
                    };
                } 
                veiculoSave.PortadorProprietarioId = veiculoSave.PortadorProprietarioId != portador.Id ? 
                    portador.Id : veiculoSave.PortadorProprietarioId;
            }
            else if (!request.ProprietarioCpfCnpj.IsNullOrWhiteSpace())
            {
                var portador = _portadorAppService.ConsultarPorCpfCnpj(request.ProprietarioCpfCnpj);

                if (portador == null)
                {
                    return new RespPadraoApi()
                    {
                        sucesso = false,
                        mensagem = "Portador não encontrado"
                    };
                } 
                veiculoSave.PortadorProprietarioId = veiculoSave.PortadorProprietarioId != portador.Id ||
                                                     veiculoSave?.PortadorProprietarioId == null? portador.Id : 
                    veiculoSave.PortadorProprietarioId;
                
            }
            else
            {
                return new RespPadraoApi()
                {
                    sucesso = false,
                    mensagem = "Portador não informado"
                };
            }
            
            var centroCusto = _centroCustoReadRepository.Where(x => x.CodigoCentroCusto == request.CentroCusto
                                && x.Ativo == 1)?.FirstOrDefault();

            if (centroCusto == null)
            {
                return new RespPadraoApi()
                {
                    sucesso = false,
                    mensagem = "Centro de custo não encontrado"
                };
            }
            
            veiculoSave.CentroCustoId = veiculoSave.CentroCustoId != centroCusto.Id || veiculoSave.CentroCustoId == null ? centroCusto.Id : veiculoSave.CentroCustoId;
            veiculoSave.FilialId = centroCusto.FilialId;
            
            if (veiculoSave.Id.ToInt() > 0)
            {
                var combustiveisDoBanco = Repository.Query
                    .Where(x => x.Id == veiculoSave.Id.ToInt())
                    .SelectMany(x => x.VeiculoCombustiveis)
                    .ToList();

                var combustiveisDaRequest = request.VeiculoCombustiveis
                    .Select(rc => rc.Combustivel.ToLower().Trim())
                    .ToList();

                // Carrega todos os combustíveis do banco no objeto
                veiculoSave.VeiculoCombustiveis = combustiveisDoBanco
                    .Where(cb => combustiveisDaRequest.Contains(cb.Combustivel.Nome.ToLower().Trim()))
                    .Select(cb => new VeiculoCombustivelRequest
                    {
                        Id = cb.Id,
                        CombustivelId = cb.CombustivelId,
                        Capacidade = cb.Capacidade,
                        Autonomia = cb.Autonomia,
                        VeiculoId = cb.VeiculoId,
                        DataCadastro = cb.DataCadastro
                    }).ToList();
            }
            else
            {
                veiculoSave.VeiculoCombustiveis = new List<VeiculoCombustivelRequest>();
            }
            
            
            foreach (var lCombustivel in request.VeiculoCombustiveis)
            {
                var combustivel = _combustivelReadRepository.Where(x => x.Nome == lCombustivel.Combustivel)
                    ?.FirstOrDefault();

                if (combustivel == null || combustivel.Id <= 0 )
                {
                    return new RespPadraoApi()
                    {
                        sucesso = false,
                        mensagem = $"Combustível {lCombustivel.Combustivel} não encontrado!"
                    };
                }

                var combustivelObj = new VeiculoCombustivelRequest();
                
                combustivelObj.CombustivelId = combustivel.Id;
                combustivelObj.Capacidade = lCombustivel.Capacidade;
                combustivelObj.Autonomia = 0;
                combustivelObj.VeiculoId = 0;
                
                if(veiculoSave.VeiculoCombustiveis.Exists(x => x.CombustivelId == combustivelObj.CombustivelId))
                {
                    var combustivelVerificaTanque =
                        veiculoSave.VeiculoCombustiveis.First(x => x.CombustivelId == combustivelObj.CombustivelId);

                    if (combustivelVerificaTanque.Capacidade != combustivelObj.Capacidade)
                    {
                        veiculoSave.VeiculoCombustiveis.Where(x => x.Id == combustivelVerificaTanque.Id).First()
                            .Capacidade = combustivelObj.Capacidade;
                    } 
                }
                
                if (!veiculoSave.VeiculoCombustiveis.Exists(x => x.CombustivelId == combustivelObj.CombustivelId))
                {
                    veiculoSave.VeiculoCombustiveis.Add(combustivelObj);
                }
            }

            if (request.Renavam.Length > 11 || !matchRenavam.Success)
            {
                return new RespPadraoApi()
                {
                    sucesso = false,
                    mensagem = "Renavam informado é inválido."
                };
            }
            // teste
            veiculoSave.Renavam = veiculoSave.Renavam != request.Renavam || veiculoSave.Renavam == null? request.Renavam : veiculoSave.Renavam;
            
            var response = await Save(veiculoSave, true);
            var veiculoId = response.id;
            
            if (response.sucesso)
            {
                if (consultaVeiculo)
                {
                    return new RespPadraoApi()
                    {
                        sucesso = true,
                        mensagem = "Veículo id: " + veiculoId + ", atualizado com sucesso!"
                    };
                }
                return new RespPadraoApi()
                {
                    sucesso = true,
                    mensagem = $"Veículo salvo com sucesso."
                };
            }
            return new RespPadraoApi()
            {
                sucesso = false,
                mensagem = response.mensagem ?? "Erro ao salvar!"
            };
            
        }

        public async Task AlterarStatus(VeiculoStatusRequest lVeiculoStatus)
        {
            await Engine.CommandBus.SendCommandAsync(Mapper.Map<VeiculoAlterarStatusCommand>(lVeiculoStatus));
        }

        public async Task<VeiculoConsultarApiResponse> Consultar(VeiculoConsultarApiRequest request)
        {
            var lVeiculo = Repository.Query.GetAll();

            //Veiculo x empresa
            if (Engine.User.EmpresaId > 0)
            {
                var lVeiculosEmp = await _veiculoEmpresaReadRepository.Where(x => x.EmpresaId == Engine.User.EmpresaId)
                    .ToListAsync();

                lVeiculo = lVeiculo.Where(x => lVeiculosEmp.Select(c => c.VeiculoId).Contains(x.Id));
            }

            if (!string.IsNullOrEmpty(request.Placa))
            {
                lVeiculo = lVeiculo.Where(v => v.Placa == request.Placa);
            }

            if (!string.IsNullOrEmpty(request.ProprietarioCpfCnpj))
            {
                lVeiculo = lVeiculo.Where(v => v.Portador.CpfCnpj == request.ProprietarioCpfCnpj);
            }

            var lista = await lVeiculo.ProjectTo<VeiculoApiResponse>().ToListAsync();
            var count = lista.Count;

            return new VeiculoConsultarApiResponse()
            {
                TotalItems = count,
                Page = request.Page,
                Items = lista.Skip((request.Page - 1) * request.Take).Take(request.Take).ToList()
            };
        }

        public async Task<RespPadrao> SalvarVeiculoEmpresa(VeiculoEmpresaRequest lVeiculoEmpresa)
        {
            try
            {
                lVeiculoEmpresa.EmpresaId =
                    Engine.User.EmpresaId == 0 ? lVeiculoEmpresa.EmpresaId : Engine.User.EmpresaId;

                await Engine.CommandBus.SendCommandAsync(Mapper.Map<VeiculoEmpresaSalvarCommand>(lVeiculoEmpresa));

                return new RespPadrao
                {
                    sucesso = true,
                    mensagem = ""
                };
            }
            catch (Exception e)
            {
                return new RespPadrao()
                {
                    sucesso = false,
                    mensagem = e.Message
                };
            }
        }

        public async Task<RespPadrao> ConsultarVeiculoAbastecimento(string placa, string frota, int portadorId,
            int postoIdMobile = 0)
        {
            try
            {
                var portador = await _portadorReadRepository.GetByIdIncludesAsync(portadorId);

                if (portador == null)
                    return new RespPadrao(false, "Portador não encontrado.", null);

                var veiculo = string.IsNullOrWhiteSpace(placa) 
                    ? await Repository.Query.GetByFrota(frota) 
                    : await Repository.Query.GetByPlaca(placa);

                if (veiculo == null)
                    return new RespPadrao(false, "Portador sem permissão para o veículo apresentado.", null);

                var portadorPlacas = new List<string>();

                if (!string.IsNullOrWhiteSpace(portador.Placa))
                    portadorPlacas.Add(portador.Placa);

                if (!string.IsNullOrWhiteSpace(portador.Carreta?.Placa))
                    portadorPlacas.Add(portador.Carreta?.Placa);

                if (!string.IsNullOrWhiteSpace(portador.Carreta2?.Placa))
                    portadorPlacas.Add(portador.Carreta2?.Placa);

                if (!string.IsNullOrWhiteSpace(portador.Carreta3?.Placa))
                    portadorPlacas.Add(portador.Carreta3?.Placa);

                //Consulta de placa por centro de custo vinculados ao portador e veiculo
                if (portador.PortadorCentroCusto.Any() && portador.ControlaAbastecimentoCentroCusto == 1)
                {
                    foreach (var portadorCentroCusto in portador.PortadorCentroCusto)
                    {
                        var veiculoConsulta = await Repository.Query
                            .FirstOrDefaultAsync(x => x.CentroCustoId == portadorCentroCusto.CentroCustoId && x.Placa == veiculo.Placa);

                        if (!string.IsNullOrWhiteSpace(veiculoConsulta?.Placa))
                            portadorPlacas.Add(veiculoConsulta.Placa);
                    }
                }

                if (!portadorPlacas.Contains(veiculo.Placa))
                    return new RespPadrao(false, "Portador sem permissão para o veículo apresentado.", null);

                var postoId = postoIdMobile != 0 ? postoIdMobile : User.AdministradoraId;

                var veiculoCombustiveis = await _veiculoCombustivelReadRepository
                    .GetByVeiculoIdAsync(veiculo.Id);
                
                if (!veiculoCombustiveis.Any())
                    return new RespPadrao(false, "Veículo sem vínculo de combustível.", null);
                
                var veiculoCombustiveisList = Mapper.Map(veiculoCombustiveis, new List<VeiculoCombustiveisList>());

                var postoCombustivel = new List<CombustiveisList>();

                var combustiveisIds = veiculoCombustiveisList
                    .Where(c => c.CombustivelId.HasValue)
                    .Select(c => c.CombustivelId.ToInt())
                    .ToList();
                
                var combustiveis = await _postoCombustivelReadRepository
                    .GetByCombustiveisIdsAndPostoIdAsync(postoId, combustiveisIds);

                if (!combustiveis.Any())
                    return new RespPadrao(false, "Posto sem cadastro de combustível.", null);
                
                var combustivelToAdd = Mapper.Map(combustiveis, new List<CombustiveisList>());
                postoCombustivel.AddRange(combustivelToAdd);
                
                var veiculoFuncionario = new ConsultaVeiculoAbastecimentoResponse();
                veiculoFuncionario.VeiculoId = veiculo.Id;
                veiculoFuncionario.Placa = veiculo.Placa;
                veiculoFuncionario.Frota = veiculo.NumeroFrota;
                veiculoFuncionario.Odometro = veiculo.Odometro;
                veiculoFuncionario.ListaCombustiveis = postoCombustivel;

                return new RespPadrao(true, "Veículo consultado com sucesso.", veiculoFuncionario);
            }
            catch (Exception e)
            {
                LogManager.GetCurrentClassLogger().Error(e);
                return new RespPadrao(false, "Não foi possível consultar o veículo. Mensagem: " + e.Message);
            }
        }

        public RespPadrao ConsultaVeiculoAbastecimento(ConsultaVeiculoAbastecimentoMobileRequest placaFrotaPosto, string portadorCpfCnpj)
        {
            var controlaCentroCusto = _portadorReadRepository
                .FirstOrDefault(x => x.CpfCnpj == portadorCpfCnpj)
                .ControlaAbastecimentoCentroCusto == 1;

            var frota = "";
            var placa = "";
            
            if (controlaCentroCusto)
            {
                frota = placaFrotaPosto.PlacaFrota;
            }
            else
            {
                placa = placaFrotaPosto.PlacaFrota;
            }

            var portadorId = _portadorReadRepository.FirstOrDefault(x => x.CpfCnpj == portadorCpfCnpj).Id;

            var retornoVeiculoMobile = ConsultarVeiculoAbastecimento(placa, frota, portadorId, placaFrotaPosto.PostoId).Result;

            if (retornoVeiculoMobile.sucesso)
            {
                return new RespPadrao()
                {
                    sucesso = true,
                    data = Mapper.Map<ConsultaVeiculoAbastecimentoMobileResponse>(retornoVeiculoMobile.data)
                };
            }

            return new RespPadrao()
            {
                sucesso = false,
                mensagem = retornoVeiculoMobile.mensagem
            };
        }
        
        public async Task<RespPadrao> ConsultarVeiculoFilial(int filialId, int modeloId = 0)
        {
            try
            {
                //Consulta de veiculos e autorizações existente paraq filial solicitada
                var lVeiculoFilial = await Repository.Query.Where(x=>x.Filial.Id==filialId)
                    .Include(x=>x.VeiculoCombustiveis)
                    .ThenInclude(x=>x.Combustivel).ToListAsync();

                if (modeloId > 0)
                {
                    lVeiculoFilial = lVeiculoFilial.Where(x => x.ModeloId == modeloId).ToList();
                }

                var lAutorizacaoAbastecimento = await _autorizacaoAbastecimentoReadRepository
                    .Where(x => x.FilialId == filialId && x.DataCadastro.Month == DateTime.Now.Month).ToListAsync();

                //Se localizada autorização os veiculos que japossuem autorização são removidos da lista

                var lVeiculoRemover = new List<Domain.Models.Veiculo.Veiculo>();
                
                if (lAutorizacaoAbastecimento.Count > 0)
                {
                    foreach (var veiculo in lVeiculoFilial)
                    {
                        foreach (var lCombustivelVeiculo in veiculo.VeiculoCombustiveis)
                        {
                            foreach (var veiculoAutorizacao in lAutorizacaoAbastecimento)
                            {
                                if (veiculo.Id == veiculoAutorizacao.VeiculoId && lCombustivelVeiculo.CombustivelId == veiculoAutorizacao.CombustivelId)
                                {
                                    lVeiculoRemover.Add(veiculo);
                                }
                            }
                        }
                    }

                    foreach (var lRemove in lVeiculoRemover)
                    {
                        lVeiculoFilial.Remove(lRemove);
                    }
                }

                if (lVeiculoFilial.Count > 0)
                {
                    var lVeiculoConsulta = new VeiculoFilialConsultaResponse();
                    lVeiculoConsulta.Veiculos = new List<VeiculosList>();
                    lVeiculoConsulta.VeiculoCombustiveis = new List<VeiculoCombustiveis>();

                    foreach (var lVeiculos in lVeiculoFilial)
                    {
                        var veiculo = new VeiculosList();
                        veiculo.LConbustiveis = new List<int>();
                        
                        veiculo.VeiculoId = lVeiculos.Id;
                        veiculo.Metodo = lVeiculos.TipoAbastecimento.GetHashCode();
                        veiculo.VeiculoPlaca = lVeiculos.Placa;

                        foreach (var lConbustivelVeiculo in lVeiculos.VeiculoCombustiveis)
                        {
                            veiculo.LConbustiveis.Add(lConbustivelVeiculo.CombustivelId);
                        }

                        if (!lVeiculoConsulta.Veiculos.Exists(x => x.VeiculoId == veiculo.VeiculoId))
                        {
                            lVeiculoConsulta.Veiculos.Add(veiculo);
                        }

                        foreach (var lCombustivel in lVeiculos.VeiculoCombustiveis)
                        {
                            var combustivel = new VeiculoCombustiveis();
                            combustivel.CombustivelId = lCombustivel.CombustivelId;
                            combustivel.CombustivelNome = lCombustivel.Combustivel.Nome;

                            if (!lVeiculoConsulta.VeiculoCombustiveis.Exists(x =>
                                x.CombustivelId == combustivel.CombustivelId))
                            {
                                lVeiculoConsulta.VeiculoCombustiveis.Add(combustivel);
                            }
                        }

                    }

                    return new RespPadrao()
                    {
                        data = lVeiculoConsulta,
                        sucesso = true
                    };
                }
                
                return new RespPadrao()
                {
                    sucesso = false,
                    mensagem = "Filial não possui veículos disponíveis para gerar autorizações"
                };
            }
            catch (Exception e)
            {
                return new RespPadrao()
                {
                    sucesso = false,
                    mensagem = e.Message
                };
            }
        }

        public async Task<RespPadrao> ConsultarVeiculoCombustivel(int veiculoId)
        {
            var lVeiculoConbustiveis = Repository.Query.Where(x => x.Id == veiculoId)
                .Include(x => x.VeiculoCombustiveis)
                .ThenInclude(x => x.Combustivel)
                .FirstOrDefault()?.VeiculoCombustiveis;

            if (lVeiculoConbustiveis == null 
                || lVeiculoConbustiveis.Count == 0)
            {
                return new RespPadrao()
                {
                    sucesso = false
                };
            }
            
            var lAutorizacaoAbastecimento = await _autorizacaoAbastecimentoReadRepository
                .Where(x => x.VeiculoId == veiculoId 
                            && x.DataCadastro.Month == DateTime.Now.Month
                            && x.Status == StatusAutorizacaoAbastecimento.Aberto)
                .ToListAsync();

            //Se localizada autorização os veiculos que japossuem autorização são removidos da lista
                
            var veiculoCombustiveisCombo = new List<VeiculoCombustiveis>();

            foreach (var lCombustivel in lVeiculoConbustiveis)
            {
                var existeAutorizacaoCombustivel = false;
                
                var combustivel = new VeiculoCombustiveis();
                combustivel.CombustivelId = lCombustivel.CombustivelId;
                combustivel.CombustivelNome = lCombustivel.Combustivel.Nome;

                if (lAutorizacaoAbastecimento.Count != 0)
                {
                    foreach (var veiculoAutorizacao in lAutorizacaoAbastecimento)
                    {
                        if (lCombustivel.CombustivelId != veiculoAutorizacao.CombustivelId)
                        {
                            if (!existeAutorizacaoCombustivel)
                            {
                                if (!veiculoCombustiveisCombo.Any(x => x.CombustivelId == combustivel.CombustivelId) 
                                    && !lAutorizacaoAbastecimento.Any(x => x.CombustivelId == combustivel.CombustivelId))
                                {
                                    veiculoCombustiveisCombo.Add(combustivel);
                                }
                            }
                        }
                        else
                        {
                            existeAutorizacaoCombustivel = true;
                        }
                    }
                }
                else
                {
                    if (!veiculoCombustiveisCombo.Any(x => x.CombustivelId == combustivel.CombustivelId))
                    {
                        veiculoCombustiveisCombo.Add(combustivel);
                    }
                }
                
            }

            return new RespPadrao()
            {
                data = veiculoCombustiveisCombo,
                sucesso = true
            };
        }

        public async Task<ConsultarGridVeiculoCiotResponse> ConsultarGridVeiculoPortadorCiotCombo(string cpfCnpjProprietario, int requestTake,
            int requestPage, OrderFilters requestOrder, List<QueryFilters> requestFilters)
        {
            var lVeiculo = Repository.Query.GetAll().Where(a =>
                a.Portador.CpfCnpj == cpfCnpjProprietario.OnlyNumbers() && a.Status == StatusVeiculo.Ativo);

            //Veiculo x empresa
            if (Engine.User.EmpresaId > 0)
            {
                var lVeiculosEmp = _veiculoEmpresaReadRepository.Where(x => x.EmpresaId == Engine.User.EmpresaId).ToList();

                lVeiculo = lVeiculo.Where(x => lVeiculosEmp.Select(c => c.VeiculoId).Contains(x.Id));
            }

            lVeiculo = lVeiculo.AplicarFiltrosDinamicos(requestFilters);
            lVeiculo = string.IsNullOrWhiteSpace(requestOrder?.Campo)
                ? lVeiculo.OrderByDescending(o => o.Id)
                : lVeiculo.OrderBy($"{requestOrder.Campo} {requestOrder.Operador.DescriptionAttr()}");

            var retorno = await lVeiculo.Skip((requestPage - 1) * requestTake).Take(requestTake)
                .ProjectTo<ConsultarGridVeiculoItem>().ToListAsync();

            var lCount = lVeiculo.Count();

            return new ConsultarGridVeiculoCiotResponse
            {
                items = retorno,
                totalItems = lCount
            };
        }
    }
}
