﻿using System;
using System.Collections.Generic;
using System.Linq;
using System.Linq.Dynamic.Core;
using System.Threading.Tasks;
using AutoMapper.QueryableExtensions;
using SistemaInfo.BBC.Application.Interface.PostoContato;
using SistemaInfo.BBC.Application.Objects.Base;
using SistemaInfo.BBC.Application.Objects.Web.PostoContato;
using SistemaInfo.BBC.Application.Objects.Web.PostoContato.Request;
using SistemaInfo.BBC.Domain.Grid;
using SistemaInfo.BBC.Domain.Helper;
using SistemaInfo.BBC.Domain.Models.PostoContato.Commands;
using SistemaInfo.BBC.Domain.Models.PostoContato.Repository;
using SistemaInfo.Framework.DomainDrivenDesign.Application.Service;
using SistemaInfo.Framework.DomainDrivenDesign.Domain.Engine;

namespace SistemaInfo.BBC.Application.Services.PostoContato
{
    public class PostoContatoAppService : AppService<Domain.Models.PostoContato.PostoContato, IPostoContatoReadRepository, IPostoContatoWriteRepository>, 
        IPostoContatoAppService
    {
        public PostoContatoAppService(IAppEngine engine, IPostoContatoReadRepository readRepository, IPostoContatoWriteRepository writeRepository) : base(engine, readRepository, writeRepository)
        {
        }

        public ConsultarGridPostoContatoResponse ConsultarGridPostoContato(int take, int page, OrderFilters orderFilters, List<QueryFilters> filters)
        {
            var lPostoContato = Repository.Query.GetAll();

            var lCount = lPostoContato.Count();
            
            lPostoContato = lPostoContato.AplicarFiltrosDinamicos(filters);
            lPostoContato = string.IsNullOrWhiteSpace(orderFilters?.Campo)
                ? lPostoContato.OrderByDescending(o => o.Id)
                : lPostoContato.OrderBy($"{orderFilters.Campo} {orderFilters.Operador.DescriptionAttr()}");

            var retorno = lPostoContato.Skip((page - 1) * take)
                .Take(take)
                .ProjectTo<ConsultarPostoContatoGrid>(Engine.Mapper.ConfigurationProvider).ToList();
            
            return new ConsultarGridPostoContatoResponse
            {
                items = retorno,
                totalItems = lCount
            };
        }

        public ConsultarPostoContatoIdResponse ConsultarPorId(int idPostoContato)
        {
            return Mapper.Map<ConsultarPostoContatoIdResponse>(Repository.Query
                .FirstOrDefault(a => a.Id == idPostoContato));
        }
        
        public  async Task<RespPadrao> Save(PostoContatoRequest lPostoContatoReq)
        {
            try
            {
                var lPostoContato = Mapper.Map<PostoContatoSaveComRetornoCommand>(lPostoContatoReq);
                
                var result = await Engine.CommandBus.SendCommandAsync<Domain.Models.PostoContato.PostoContato>(lPostoContato);
                
                return new RespPadrao()
                {
                    id = result.Id,
                    sucesso = true,
                    mensagem = ""
                };
            }
            catch (Exception e)
            {
                return new RespPadrao()
                {
                    sucesso = false,
                    mensagem = e.Message
                };
            }
        }
    }
}