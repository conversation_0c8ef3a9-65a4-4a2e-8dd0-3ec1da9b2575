using System;
using System.Collections.Generic;
using System.Globalization;
using System.Linq;
using System.Linq.Dynamic.Core;
using System.Threading.Tasks;
using AutoMapper.QueryableExtensions;
using Microsoft.EntityFrameworkCore;
using Microsoft.EntityFrameworkCore.Internal;
using Newtonsoft.Json;
using NLog;
using SistemaInfo.BBC.Application.Interface.Empresa;
using SistemaInfo.BBC.Application.Interface.MDRPrazos;
using SistemaInfo.BBC.Application.Interface.PagamentoAbastecimento;
using SistemaInfo.BBC.Application.Interface.Parametros;
using SistemaInfo.BBC.Application.Interface.Posto;
using SistemaInfo.BBC.Application.Objects.Base;
using SistemaInfo.BBC.Application.Objects.Api.PagamentoAbastecimento;
using SistemaInfo.BBC.Application.Objects.Web.Abastecimento;
using SistemaInfo.BBC.Application.Objects.Web.PagamentoAbastecimento;
using SistemaInfo.BBC.Application.Objects.Web.PagamentoLoteRequest;
using SistemaInfo.BBC.Application.Objects.Web.ProtocoloAbastecimento;
using SistemaInfo.BBC.Domain.Enum;
using SistemaInfo.BBC.Domain.External.Conductor.DTO.Cartao;
using SistemaInfo.BBC.Domain.External.Conductor.DTO.Transferencia;
using SistemaInfo.BBC.Domain.External.Conductor.Interface;
using SistemaInfo.BBC.Domain.Helper;
using SistemaInfo.BBC.Domain.Models.Abastecimento.Commands;
using SistemaInfo.BBC.Domain.Models.Abastecimento.Repository;
using SistemaInfo.BBC.Domain.Models.LotePagamento;
using SistemaInfo.BBC.Domain.Models.LotePagamento.Commands;
using SistemaInfo.BBC.Domain.Models.LotePagamento.Repository;
using SistemaInfo.BBC.Domain.Models.PagamentoAbastecimento.Commands;
using SistemaInfo.BBC.Domain.Models.PagamentoAbastecimento.Repository;
using SistemaInfo.BBC.Domain.Models.Parametros.Repository;
using SistemaInfo.BBC.Domain.Models.ProtocoloAbastecimento.Commands;
using SistemaInfo.BBC.Domain.Models.ProtocoloAbastecimento.Repository;
using SistemaInfo.BBC.Infra.Reports.Interfaces;
using SistemaInfo.BBC.Infra.Reports.Objects;
using SistemaInfo.Framework.DomainDrivenDesign.Application.Service;
using SistemaInfo.Framework.DomainDrivenDesign.Domain.Engine;
using SistemaInfo.Framework.Utils;

namespace SistemaInfo.BBC.Application.Services.PagamentoAbastecimento
{
    public class PagamentoAbastecimentoAppService :
        AppService<Domain.Models.PagamentoAbastecimento.PagamentoAbastecimento,
            IPagamentoAbastecimentoReadRepository,
            IPagamentoAbastecimentoWriteRepository>,
        IPagamentoAbastecimentoAppService
    {
        private readonly ICartaoRepository _cartaoRepository;
        private readonly IAbastecimentoReadRepository _abastecimentoReadRepository;
        private readonly IParametrosAppService _parametrosAppService;
        private readonly ITransferenciaRepository _transferenciaRepository;
        private readonly IPostoAppService _postoAppService;
        private readonly IMDRPrazosAppService _mdrPrazosAppService;
        private readonly IEmpresaAppService _empresaAppService;
        private readonly IProtocoloAbastecimentoReadRepository _protocoloAbastecimentoReadRepository;
        private readonly IProtocoloAbastecimentoWriteRepository _protocoloAbastecimentoWriteRepository;
        private readonly IParametrosReadRepository _parametrosReadRepository;
        private readonly ILotePagamentoReadRepository _lotePagamentoReadRepository;
        private readonly ILotePagamentoWriteRepository _lotePagamentoWriteRepository;
        private readonly IReportExporterFinanceiro _reportExporterFinanceiro;

        public PagamentoAbastecimentoAppService(IAppEngine engine,
            IPagamentoAbastecimentoReadRepository readRepository,
            IPagamentoAbastecimentoWriteRepository writeRepository,
            ICartaoRepository cartaoRepository,
            IAbastecimentoReadRepository abastecimentoReadRepository,
            IParametrosAppService parametrosAppService,
            ITransferenciaRepository transferenciaRepository,
            IPostoAppService postoAppService,
            IMDRPrazosAppService mdrPrazosAppService,
            IEmpresaAppService empresaAppService,
            IProtocoloAbastecimentoReadRepository protocoloAbastecimentoReadRepository,
            IProtocoloAbastecimentoWriteRepository protocoloAbastecimentoWriteRepository,
            ILotePagamentoReadRepository lotePagamentoReadRepository,
            ILotePagamentoWriteRepository lotePagamentoWriteRepository,
            IParametrosReadRepository parametrosReadRepository, IReportExporterFinanceiro reportExporterFinanceiro) : base(engine, readRepository, writeRepository)
        {
            _cartaoRepository = cartaoRepository;
            _abastecimentoReadRepository = abastecimentoReadRepository;
            _parametrosAppService = parametrosAppService;
            _transferenciaRepository = transferenciaRepository;
            _postoAppService = postoAppService;
            _mdrPrazosAppService = mdrPrazosAppService;
            _empresaAppService = empresaAppService;
            _protocoloAbastecimentoReadRepository = protocoloAbastecimentoReadRepository;
            _protocoloAbastecimentoWriteRepository = protocoloAbastecimentoWriteRepository;
            _parametrosReadRepository = parametrosReadRepository;
            _reportExporterFinanceiro = reportExporterFinanceiro;
            _lotePagamentoReadRepository = lotePagamentoReadRepository;
            _lotePagamentoWriteRepository = lotePagamentoWriteRepository;
        }

        public Task<ConsultarContaResp> ConsultaContaCPFCNPJ(string cpfCnpj)
        {
            if (cpfCnpj.Length > 11)
            {
                var aPj = _cartaoRepository.ConusltaContaPessoaJuridica(cpfCnpj).Result;

                if (aPj?.results?.FirstOrDefault()?.statusSPD == null)
                    return _cartaoRepository.ConsultarContas(null, null, cpfCnpj);
                
                foreach (var lBloqueioSpd in aPj.results.First().statusSPD)
                {
                    if (lBloqueioSpd.statusId == 1 && lBloqueioSpd.createDate < DateTime.Now.AddDays(-15))
                    {
                        throw new Exception("Erro StautsSPD Origem Data");
                    }

                    if (lBloqueioSpd.statusId != 4 && lBloqueioSpd.statusId != 10 &&
                        lBloqueioSpd.statusId != 11 && lBloqueioSpd.statusId != 12 &&
                        lBloqueioSpd.statusId != 18 && lBloqueioSpd.statusId != 19 &&
                        lBloqueioSpd.statusId != 20 && lBloqueioSpd.statusId != 1)
                    {
                        throw new Exception("Erro StautsSPD Origem");

                    }
                }
                return _cartaoRepository.ConsultarContas(null, null, cpfCnpj);
            }

            var consultarContaResp = _cartaoRepository.ConsultarContas(null, null, cpfCnpj).Result;
            
            var lValidacaoStatusSpdContaOrigem = _cartaoRepository
                .ConsultarPessoa(consultarContaResp?.content?.FirstOrDefault()?.idPessoa.ToDecimalSafe() ?? 0);

            if (lValidacaoStatusSpdContaOrigem.Result.statusSPD == null)
                return _cartaoRepository.ConsultarContas(null, null, cpfCnpj);
            
            foreach (var lBloqueioSpd in lValidacaoStatusSpdContaOrigem.Result.statusSPD)
            {
                if (lBloqueioSpd.statusId != 4 && lBloqueioSpd.statusId != 10 &&
                    lBloqueioSpd.statusId != 11 && lBloqueioSpd.statusId != 12 &&
                    lBloqueioSpd.statusId != 18 && lBloqueioSpd.statusId != 19 &&
                    lBloqueioSpd.statusId != 20)
                {
                    throw new Exception("Erro StautsSPD Origem");
                }
            }

            return _cartaoRepository.ConsultarContas(null, null, cpfCnpj);
        }

        private async Task<Domain.Models.Abastecimento.Abastecimento> ConsultaAbastecimento(int abastecimentoId)
        {
            return await _abastecimentoReadRepository.GetAbastecimentos()
                .Where(x => x.Id == abastecimentoId)
                .Include(x => x.Portador)
                .Include(x => x.Empresa)
                .Include(x => x.Posto)
                .Include(x => x.Combustivel)
                .Include(x => x.Veiculo)
                .FirstOrDefaultAsync();
        }

        private async Task<Domain.Models.Empresa.Empresa> ConsultarEmpresaFrota(int empresaFrotaId)
        {
            return await _empresaAppService.Repository.Query
                .FirstOrDefaultAsync(y => y.Id == empresaFrotaId);
        }

        private async Task<bool> Saldo(string contaId, decimal valorTransacao)
        {
            var retorno = await _cartaoRepository.ConsultarSaldo(contaId);

            if (retorno == null)
            {
                return false;
            }

            if (retorno.saldoDisponivelGlobal < valorTransacao)
            {
                return false;
            }

            return true;
        }

        private async Task<RespPadrao> VerificarTransacaoExistente(string idContaOrigem, string idContaDestino,
            decimal valorTransacao, DateTime dataCadastro, string descricao)
        {
            try
            {
                var lPagamentosRequest = new ConsultarPagamentosContaRequest()
                {
                    ContaDestino = idContaDestino,
                    ContaOrigem = idContaOrigem,
                    Valor = Math.Round(valorTransacao, 2).ToString().Replace(',', '.'),
                    DataPagamento = dataCadastro.ToString("yyyy-MM-dd") + " 00:00:00"
                };
                
                var lRetornoExtratoPagamento =
                    await _cartaoRepository.ConsultarPagamentosConta(lPagamentosRequest);

                var lExtraoPagamentos = JsonConvert.DeserializeObject<List<Transferencia>>(lRetornoExtratoPagamento,
                    new JsonSerializerSettings {DateTimeZoneHandling = DateTimeZoneHandling.Local});

                foreach (var lPagamento in lExtraoPagamentos)
                {
                    if (lPagamento.description == descricao)
                    {
                        return new RespPadrao()
                        {
                            sucesso = true,
                            mensagem = "Pagamento já efetuado!"
                        };
                    }
                }
            }
            catch (Exception e)
            {
                return new RespPadrao()
                {
                    sucesso = false,
                    mensagem = "Erro ao realizar verificação!" + e
                };
            }

            return new RespPadrao()
            {
                sucesso = false,
                mensagem = "Pagamentos não duplicado!"
            };
        }

        public async Task<RespPadrao> IntegrarRetencao(int abastecimentoId, bool integracao = false)
        {
            try
            {
                #region Coleta de dados para contrucao de objetos e realizar transferencia

                //Abastecimento
                var lAbastecimento = ConsultaAbastecimento(abastecimentoId)?.Result;
                if (lAbastecimento == null)
                {
                    return new RespPadrao {sucesso = false, mensagem = "Abastecimento não localizado!"};
                }

                var consultaRegistroDuplicidade = Repository.Query.FirstOrDefault(x =>
                    x.AbastecimentoId == lAbastecimento.Id &&
                    x.TipoOperacao == TipoOperacaoPagamentoAbastecimento.Retencao &&
                    x.Status == StatusPagamentoAbastecimento.Baixado);

                if (consultaRegistroDuplicidade != null)
                {
                    return new RespPadrao
                    {
                        id = consultaRegistroDuplicidade.Id,
                        sucesso = true,
                        mensagem = "Registro salvo com sucesso!"
                    };
                }

                //Conta da empresa do Portador
                var empresaPortador = ConsultarEmpresaFrota(lAbastecimento.Portador.EmpresaIdFrota.ToInt())?.Result;
                if (empresaPortador == null) return new RespPadrao (false,  "Portador não vinculado a uma empresa!");

                    var lContaEmpresaPortador = empresaPortador.ContaAbastecimento;
                if (lContaEmpresaPortador == null) return new RespPadrao (false,  "Conta não localizada!");
                

                var lIdContaParametro = _parametrosAppService.GetParametrosAsync(-1,
                    Domain.Models.Parametros.Parametros.TipoDoParametro.CodigoContaTransferenciaValorRetencao,
                    Domain.Models.Parametros.Parametros.TipoDoValor.Number).Result.Valor;
                if (lIdContaParametro == null) return new RespPadrao (false,  "Parametro conta retenção não localizado!");
                

                //TaxaAcrecimo
                var taxaAcrecimo = _empresaAppService.Repository.Query
                    .FirstOrDefault(x => x.Id == empresaPortador.Id).TaxaAbastecimento;
                
                if (taxaAcrecimo == null) return new RespPadrao (false, "% taxa de acrecimo não informado!");

                //Mdr do portador e Desconto Mdr
                var mdrPosto = _postoAppService.Repository.Query.FirstOrDefault(x => x.Id == lAbastecimento.PostoId)
                    .MDRId;
                
                if (mdrPosto == null) return new RespPadrao (false, "MDR não localizado no posto informado!");
                

                var descontoMDR = _mdrPrazosAppService.Repository.Query
                    .FirstOrDefault(x => x.Id == mdrPosto).MDR;
               
                if (descontoMDR == null) return new RespPadrao(false,"% desconto MDR não informado!");
                
                #endregion

                #region ObjetoPagamentoAbastecimento-Retencao

                var lPagamentoAbastecimentoSave = new AbastecimentoTransacaoIntegrarApiRequest();

                lPagamentoAbastecimentoSave.ContaOrigem = lContaEmpresaPortador.ToInt();
                lPagamentoAbastecimentoSave.ContaDestino = lIdContaParametro.ToInt();
                lPagamentoAbastecimentoSave.AbastecimentoId = lAbastecimento.Id;
                lPagamentoAbastecimentoSave.ValorAbastecimento = lAbastecimento.ValorAbastecimento;

                lPagamentoAbastecimentoSave.TaxaAbastecimento =
                    (decimal) (lAbastecimento.ValorAbastecimento * (taxaAcrecimo / 100));
                lPagamentoAbastecimentoSave.DescontoMDR =
                    (decimal) (lAbastecimento.ValorAbastecimento * (descontoMDR / 100));

                lPagamentoAbastecimentoSave.ValorAbastecimentoAcrescimoTaxa = taxaAcrecimo == 0
                    ? lAbastecimento.ValorAbastecimento
                    : (lAbastecimento.ValorAbastecimento + lPagamentoAbastecimentoSave.TaxaAbastecimento)
                    .ToDecimalSafe();

                lPagamentoAbastecimentoSave.Status = StatusPagamentoAbastecimento.Processando;
                lPagamentoAbastecimentoSave.TipoOperacao = TipoOperacaoPagamentoAbastecimento.Retencao;
                lPagamentoAbastecimentoSave.ImpostoPISTransacao = lAbastecimento.Empresa.ImpostoPIS;
                lPagamentoAbastecimentoSave.ImpostoCSLLTransacao = lAbastecimento.Empresa.ImpostoCSLL;
                lPagamentoAbastecimentoSave.ImpostoIRRFTransacao = lAbastecimento.Empresa.ImpostoIRRF;
                lPagamentoAbastecimentoSave.ImpostoCOFINSTransacao = lAbastecimento.Empresa.ImpostoCOFINS;
                lPagamentoAbastecimentoSave.CashbackTransacao = lAbastecimento.Empresa.Cashback;

                #endregion

                #region Salvamento da retencao

                var retornoRegistro = await Engine.CommandBus
                    .SendCommandAsync<Domain.Models.PagamentoAbastecimento.PagamentoAbastecimento>(Mapper
                        .Map<PagamentoAbastecimentoAdicionarCommand>(lPagamentoAbastecimentoSave));
                if (retornoRegistro != null)
                {
                    //monta observacao para ser inserida em transacao
                    var observacaoJson = new PagamentoAbastecimentoObservacao()
                    {
                        PlacaVeiculo = lAbastecimento.Veiculo.Placa,
                        CodigoOperacao = retornoRegistro.Id.ToString(),
                        TipoOperacao = "Retencao",
                        RetencaoId = "0"
                    };

                    lPagamentoAbastecimentoSave.Id = retornoRegistro.Id;
                    lPagamentoAbastecimentoSave.Observacao = JsonConvert.SerializeObject(observacaoJson);
                }

                var retorno = await Engine.CommandBus
                    .SendCommandAsync<Domain.Models.PagamentoAbastecimento.PagamentoAbastecimento>(Mapper
                        .Map<PagamentoAbastecimentoAtualizarCommand>(lPagamentoAbastecimentoSave));

                #endregion

                #region Processo de traferencia do pagamento para a conta transitoria

                var lTranferenciaRetencao = new TransferenciaEntreContaResp()
                {
                    Sucesso = false
                };

                var message = "";

                var verificarSaldo = Saldo(lPagamentoAbastecimentoSave.ContaOrigem.ToString(),
                        lPagamentoAbastecimentoSave.ValorAbastecimentoAcrescimoTaxa)
                    .Result;

                if (verificarSaldo)
                {
                    if (VerificarTransacaoExistente(lPagamentoAbastecimentoSave.ContaOrigem.ToString(),
                        lPagamentoAbastecimentoSave.ContaDestino.ToString(),
                        lPagamentoAbastecimentoSave.ValorAbastecimentoAcrescimoTaxa,
                        lPagamentoAbastecimentoSave.DataCadastro,
                        lPagamentoAbastecimentoSave.Observacao).Result.sucesso)
                    {
                        if (retorno.Id > 0)
                        {
                            var status = StatusPagamentoAbastecimento.Baixado;

                            var retornoatualizacao = await AtualizarStatus(retorno.Id, status);

                            if (retornoatualizacao.sucesso)
                            {
                                return new RespPadrao
                                {
                                    id = retorno.Id,
                                    sucesso = true,
                                    mensagem = "Registro salvo com sucesso!"
                                };
                            }
                        }
                    }

                    lTranferenciaRetencao = RealizaTransferenciaEntreContas(new Transferencia
                    {
                        amount = Math.Round(lPagamentoAbastecimentoSave.ValorAbastecimentoAcrescimoTaxa, 2),
                        description = lPagamentoAbastecimentoSave.Observacao,
                        originalAccount = lPagamentoAbastecimentoSave.ContaOrigem.ToInt(),
                        destinationAccount = lPagamentoAbastecimentoSave.ContaDestino.ToInt32()
                    }).Result;
                }
                else
                {
                    message = "Não foi possível realizar o abastecimento entre em contato com gestor!";
                }

                if (lTranferenciaRetencao.Sucesso && retorno.Id > 0)
                {
                    var status = StatusPagamentoAbastecimento.Baixado;

                    var retornoatualizacao = await AtualizarStatus(retorno.Id, status);

                    if (retornoatualizacao.sucesso)
                    {
                        return new RespPadrao
                        {
                            id = retorno.Id,
                            sucesso = true,
                            mensagem = "Registro salvo com sucesso!"
                        };
                    }
                }

                #region atualizacao de status para erro de traferencia referente a retencao registrada anteriormente

                if (!lTranferenciaRetencao.Sucesso)
                {
                    var status = StatusPagamentoAbastecimento.Erro;
                    var motivo = "Erro ao realizar transferência! " + lTranferenciaRetencao.message + ", " +
                                 lPagamentoAbastecimentoSave.Observacao;

                    var retornoatualizacao = await AtualizarStatus(retorno.Id, status, motivo);

                    if (retornoatualizacao.sucesso)
                    {
                        return new RespPadrao
                        {
                            id = retorno.Id,
                            sucesso = false,
                            mensagem = "Erro ao realizar transferência! " + message + " Motivo: " +
                                       lTranferenciaRetencao.message
                        };
                    }
                }

                #endregion

                return new RespPadrao
                {
                    sucesso = false,
                    mensagem = "Tranferencia não realizada! Verificar com a gestão"
                };

                #endregion
            }
            catch (Exception e)
            {
                return new RespPadrao()
                {
                    sucesso = false,
                    mensagem = e.Message
                };
            }
        }

        public async Task<RespPadrao> IntegrarRetencaoProtocolo(int protocoloId, bool prazo = false)
        {
            var lLog = LogManager.GetCurrentClassLogger();
            try
            {
                #region Coleta de dados para contrucao de objetos e realizar transferencia
                
                if (protocoloId <= 0)
                    return new RespPadrao {sucesso = false, mensagem = "Protocolo não informado."};
                
                //Abastecimento
                var lAbastecimentos = await _abastecimentoReadRepository
                    .Where(x => x.ProtocoloAbastecimentoId == protocoloId && 
                                x.TipoDebito == (prazo ? ETipoDebitoAbastecimento.Prazo : ETipoDebitoAbastecimento.Protocolo))
                    .ToListAsync();

                if (!lAbastecimentos.Any())
                    return new RespPadrao {sucesso = false, mensagem = "Abastecimento(s) não localizado(s)."};

                #region Verificação de duplicidade de registro individual por abastecimentos

                foreach (var lAbst in lAbastecimentos)
                {
                    var consultaDuplicidadeRetencao = await Repository.Query
                        .FirstOrDefaultAsync(x =>
                            x.AbastecimentoId == lAbst.Id &&
                            x.Abastecimento.EmpresaId == lAbst.EmpresaId &&
                            x.TipoOperacao == TipoOperacaoPagamentoAbastecimento.Retencao &&
                            x.Status == StatusPagamentoAbastecimento.Baixado);

                    if (consultaDuplicidadeRetencao != null)
                        return new RespPadrao {id = consultaDuplicidadeRetencao.Id, sucesso = true, mensagem = "Registro salvo com sucesso!"};
                }

                #endregion

                //Conta da empresa do Portador
                var empresaPortador = await ConsultarEmpresaFrota(lAbastecimentos.First().EmpresaId.ToIntSafe());

                if (empresaPortador == null)
                    return new RespPadrao {sucesso = false, mensagem = "Portador não vinculado a uma empresa!"};

                var lContaEmpresaPortador = (await ConsultaContaCPFCNPJ(empresaPortador.Cnpj))?.content?.FirstOrDefault()?.id;

                if (lContaEmpresaPortador == null)
                    return new RespPadrao {sucesso = false, mensagem = "Conta não localizada!"};

                var lIdContaParametro = (await _parametrosAppService.GetParametrosAsync(-1,
                    Domain.Models.Parametros.Parametros.TipoDoParametro.CodigoContaTransferenciaValorRetencao,
                    Domain.Models.Parametros.Parametros.TipoDoValor.Number))?.Valor;

                if (lIdContaParametro == null)
                    return new RespPadrao {sucesso = false, mensagem = "Parametro conta retenção não localizado!"};

                //TaxaAcrecimo
                var taxaAcrecimo = (await _empresaAppService.Repository.Query.GetByIdAsync(empresaPortador.Id))
                    ?.TaxaAbastecimento;

                if (taxaAcrecimo == null)
                    return new RespPadrao {sucesso = false, mensagem = "% taxa de acrecimo não informado!"};

                //Mdr do portador e Desconto Mdr
                var mdrPosto = (await _postoAppService.Repository.Query.GetByIdAsync(lAbastecimentos.First().PostoId))
                    ?.MDRId;

                if (mdrPosto == null)
                    return new RespPadrao {sucesso = false, mensagem = "MDR não localizado no posto informado!"};

                var descontoMdr = (await _mdrPrazosAppService.Repository.Query.GetByIdAsync(mdrPosto))
                    ?.MDR;

                if (descontoMdr == null)
                    return new RespPadrao {sucesso = false, mensagem = "% desconto MDR não informado!"};

                #endregion

                #region Objeto de lote de retenção e save lote

                var lValor = lAbastecimentos.Sum(x => x.ValorAbastecimento);

                var postoId = lAbastecimentos.FirstOrDefault()?.PostoId;

                if (postoId == null || postoId <= 0) 
                    return new RespPadrao {sucesso = false, mensagem = "Posto não informado!"};

                var dataPrazo = new DateTime();

                if (prazo)
                {
                    var dataPrazoPagamentoRetencao = lAbastecimentos.First().DataPrazoPagamentoRetencao;
                    if (dataPrazoPagamentoRetencao != null) dataPrazo = (DateTime) dataPrazoPagamentoRetencao;
                }

                var loteSave = new PagamentoLoteRequest();
                loteSave.ValorPagamento = lValor.ToDecimal();
                loteSave.PostoId = postoId.ToInt();
                loteSave.DataPrevisaoPagamento = prazo ? dataPrazo : DateTime.Now;
                loteSave.DataCadastro = DateTime.Now;
                loteSave.EmpresaId = lAbastecimentos.First().EmpresaId;
                loteSave.TipoLote = TipoLote.Retencao;

                loteSave.ObservacaoJson = JsonConvert.SerializeObject(new LoteReceitaAbastecimentoObservacao()
                {
                    EmpresaId = lAbastecimentos.First().EmpresaId.ToString(),
                    Protocolo = lAbastecimentos.First().ProtocoloAbastecimentoId.ToString(),
                    TipoOperacao = prazo ? "Débito prazo" : "Débito protocolo",
                    DataPrevPagamento = (prazo ? dataPrazo : DateTime.Now).ToStringBr()
                });

                LotePagamento loteRetorno;

                try
                {
                    #region Verificação de duplicidade por lote de e abastecimento

                    var consultaDuplicidadeLote = _lotePagamentoReadRepository
                        .Include(x => x.ProtocolosAbastecimento)
                        .FirstOrDefault(x => 
                            (prazo 
                                ? x.ProtocolosAbastecimento.FirstOrDefault().Id == 
                                  lAbastecimentos.FirstOrDefault(y => y.TipoDebito == ETipoDebitoAbastecimento.Prazo && y.LotePagamento != null).ProtocoloAbastecimentoId 
                                : x.ProtocolosAbastecimento.FirstOrDefault().Id == 
                                  lAbastecimentos.FirstOrDefault(y => y.TipoDebito == ETipoDebitoAbastecimento.Protocolo && y.LotePagamento != null).ProtocoloAbastecimentoId) 
                            && x.ProtocolosAbastecimento.FirstOrDefault().EmpresaId == lAbastecimentos.FirstOrDefault().EmpresaId);

                    if (consultaDuplicidadeLote == null)
                    {
                        var loteCommand = Mapper.Map<LotePagamentoSalvarComRetornoCommand>(loteSave);

                        loteRetorno = await Engine.CommandBus.SendCommandAsync<LotePagamento>(loteCommand);
                    }
                    else
                    {
                        loteRetorno = consultaDuplicidadeLote;
                    }

                    #endregion

                    if (loteRetorno.Id <= 0)
                    {
                        return new RespPadrao
                        {
                            sucesso = false,
                            mensagem = "Erro ao gerar registro de lote de retenção!"
                        };
                    }
                }
                catch (Exception e)
                {
                    return new RespPadrao
                    {
                        sucesso = false,
                        mensagem = $"Erro ao gerar registro de lote de retenção! {e.Message}"
                    };
                }

                #endregion

                #region Atualizar loteId em Abastecimento

                foreach (var lAbst in lAbastecimentos)
                {
                    var abastecimentosUpdate = new AbastecimentoLoteRetencaoRequest()
                    {
                        Id = lAbst.Id,
                        LoteRetencaoId = loteRetorno.Id
                    };

                    _abastecimentoReadRepository.Detach(lAbst);

                    var protocoloSave = Mapper.Map<AbastecimentoSalvarLoteRetencaoCommand>(abastecimentosUpdate);
                    await Engine.CommandBus.SendCommandAsync(protocoloSave);
                }

                #endregion

                #region Objeto pagamento abastecimento retencao e save retenção do lote

                var lPagamentoAbastecimentoSave = new AbastecimentoTransacaoIntegrarApiRequest();

                lPagamentoAbastecimentoSave.ContaOrigem = lContaEmpresaPortador.ToInt();
                lPagamentoAbastecimentoSave.ContaDestino = lIdContaParametro.ToInt();
                lPagamentoAbastecimentoSave.LotePagamentoId = loteRetorno.Id;
                lPagamentoAbastecimentoSave.LoteRetencaoId = loteRetorno.Id;
                lPagamentoAbastecimentoSave.ValorAbastecimento = loteRetorno.ValorPagamento;

                lPagamentoAbastecimentoSave.TaxaAbastecimento = (decimal) (loteRetorno.ValorPagamento * (taxaAcrecimo / 100));
                lPagamentoAbastecimentoSave.DescontoMDR = (decimal) (loteRetorno.ValorPagamento * (descontoMdr / 100));

                lPagamentoAbastecimentoSave.ValorAbastecimentoAcrescimoTaxa = taxaAcrecimo == 0 ? loteRetorno.ValorPagamento
                    : (loteRetorno.ValorPagamento + lPagamentoAbastecimentoSave.TaxaAbastecimento).ToDecimalSafe();

                lPagamentoAbastecimentoSave.Observacao = loteRetorno.ObservacaoJson;
                lPagamentoAbastecimentoSave.Status = StatusPagamentoAbastecimento.Aprovado;
                lPagamentoAbastecimentoSave.TipoOperacao = TipoOperacaoPagamentoAbastecimento.Retencao;
                lPagamentoAbastecimentoSave.ImpostoPISTransacao = lAbastecimentos.FirstOrDefault()?.Empresa.ImpostoPIS;
                lPagamentoAbastecimentoSave.ImpostoCSLLTransacao = lAbastecimentos.FirstOrDefault()?.Empresa.ImpostoCSLL;
                lPagamentoAbastecimentoSave.ImpostoIRRFTransacao = lAbastecimentos.FirstOrDefault()?.Empresa.ImpostoIRRF;
                lPagamentoAbastecimentoSave.ImpostoCOFINSTransacao = lAbastecimentos.FirstOrDefault()?.Empresa.ImpostoCOFINS;
                lPagamentoAbastecimentoSave.CashbackTransacao = lAbastecimentos.FirstOrDefault()?.Empresa.Cashback;
                lPagamentoAbastecimentoSave.DataPrevisaoPagamento = loteRetorno.DataPrevisaoPagamento;

                #region Verificação de duplicidade por lote de e abastecimento

                var consultaDuplicidadeOrdenRetencao = Repository.Query
                    .FirstOrDefault(x => 
                        (prazo 
                            ? x.LotePagamento.ProtocolosAbastecimento.FirstOrDefault().Id == 
                              lAbastecimentos.FirstOrDefault(y => y.TipoDebito == ETipoDebitoAbastecimento.Prazo && y.LotePagamento != null).ProtocoloAbastecimentoId 
                            : x.LotePagamento.ProtocolosAbastecimento.FirstOrDefault().Id == 
                              lAbastecimentos.FirstOrDefault(y => y.TipoDebito == ETipoDebitoAbastecimento.Protocolo && y.LotePagamento != null).ProtocoloAbastecimentoId) 
                        && x.LotePagamento.ProtocolosAbastecimento.FirstOrDefault().EmpresaId == lAbastecimentos.FirstOrDefault().EmpresaId);

                if (consultaDuplicidadeOrdenRetencao != null &&
                    consultaDuplicidadeOrdenRetencao.Status == StatusPagamentoAbastecimento.Baixado)
                {
                    return new RespPadrao
                    {
                        id = consultaDuplicidadeOrdenRetencao.Id,
                        sucesso = true,
                        mensagem = "Registro salvo com sucesso!"
                    };
                }

                #endregion

                Domain.Models.PagamentoAbastecimento.PagamentoAbastecimento ordemRetencaoRetorno;

                try
                {
                    if (consultaDuplicidadeOrdenRetencao == null)
                    {
                        var retencaoCommand = Mapper.Map<PagamentoAbastecimentoAdicionarCommand>(lPagamentoAbastecimentoSave);

                        ordemRetencaoRetorno = await Engine.CommandBus.SendCommandAsync<Domain.Models.PagamentoAbastecimento.PagamentoAbastecimento>(retencaoCommand);
                    }
                    else
                    {
                        ordemRetencaoRetorno = consultaDuplicidadeOrdenRetencao;
                    }

                    if (ordemRetencaoRetorno.Id <= 0)
                    {
                        return new RespPadrao
                        {
                            sucesso = false,
                            mensagem = "Erro ao gerar registro de ordem de retenção!"
                        };
                    }
                }
                catch (Exception e)
                {
                    return new RespPadrao
                    {
                        sucesso = false,
                        mensagem = $"Erro ao gerar registro de ordem de retenção! {e.Message}"
                    };
                }

                #endregion

                #region Alteração data parametro para futuros lotes

                if (ordemRetencaoRetorno.LotePagamento.TipoLote == TipoLote.Retencao &&
                    ordemRetencaoRetorno.DataPrevisaoPagamento.HasValue &&
                    ordemRetencaoRetorno.DataPrevisaoPagamento.Value.EndOfDay() == DateTime.Now.EndOfDay())
                {
                    var empresaId = ordemRetencaoRetorno.LotePagamento.Empresa.Id;

                    await _empresaAppService.Repository.Command.AlterarDataParametroPrazo(empresaId);
                }

                //caso prazo processo finzaliza antes do pagamento
                if (ordemRetencaoRetorno.Id > 0 && prazo)
                {
                    return new RespPadrao
                    {
                        sucesso = false,
                        mensagem = $"Ordem de retenção realizada com sucesso!"
                    };
                }

                #endregion

                #region Processo de traferencia do pagamento para a conta transitoria

                var lTranferenciaRetencao = new TransferenciaEntreContaResp()
                {
                    Sucesso = false
                };

                var message = "";

                var verificarSaldo = Saldo(ordemRetencaoRetorno.ContaOrigem.ToString(), ordemRetencaoRetorno.ValorAbastecimentoAcrescimoTaxa).Result;

                if (verificarSaldo)
                {
                    if (VerificarTransacaoExistente(ordemRetencaoRetorno.ContaOrigem.ToString(),
                        ordemRetencaoRetorno.ContaDestino.ToString(),
                        ordemRetencaoRetorno.ValorAbastecimentoAcrescimoTaxa,
                        ordemRetencaoRetorno.DataCadastro,
                        ordemRetencaoRetorno.Observacao).Result.sucesso)
                    {
                        if (ordemRetencaoRetorno.Id > 0)
                        {
                            var status = StatusPagamentoAbastecimento.Baixado;

                            var retornoatualizacao = await AtualizarStatus(ordemRetencaoRetorno.Id, status);

                            if (retornoatualizacao.sucesso)
                            {
                                return new RespPadrao
                                {
                                    id = ordemRetencaoRetorno.Id,
                                    sucesso = true,
                                    mensagem = "Registro salvo com sucesso!"
                                };
                            }
                        }
                    }

                    lTranferenciaRetencao = await RealizaTransferenciaEntreContas(new Transferencia
                    {
                        amount = Math.Round(ordemRetencaoRetorno.ValorAbastecimentoAcrescimoTaxa, 2),
                        description = ordemRetencaoRetorno.Observacao,
                        originalAccount = ordemRetencaoRetorno.ContaOrigem.ToInt(),
                        destinationAccount = ordemRetencaoRetorno.ContaDestino.ToInt32()
                    });
                }
                else
                {
                    message = "Não foi possível realizar o abastecimento entre em contato com gestor!";
                }

                #region atualizacao de status para baixado caso pagamento seja realizado com sucesso

                if (lTranferenciaRetencao.Sucesso && ordemRetencaoRetorno.Id > 0)
                {
                    var status = StatusPagamentoAbastecimento.Baixado;

                    var retornoatualizacao = await AtualizarStatus(ordemRetencaoRetorno.Id, status);

                    if (retornoatualizacao.sucesso)
                    {
                        return new RespPadrao
                        {
                            id = ordemRetencaoRetorno.Id,
                            sucesso = true,
                            mensagem = "Registro salvo com sucesso!"
                        };
                    }
                }

                #endregion

                #region atualizacao de status para erro de traferencia referente a retencao registrada anteriormente

                if (!lTranferenciaRetencao.Sucesso)
                {
                    var status = StatusPagamentoAbastecimento.Erro;
                    var motivo = $"Erro ao realizar transferência! {lTranferenciaRetencao.message}, {lPagamentoAbastecimentoSave.Observacao}";

                    var retornoatualizacao = await AtualizarStatus(ordemRetencaoRetorno.Id, status, motivo);

                    if (retornoatualizacao.sucesso)
                    {
                        return new RespPadrao
                        {
                            id = ordemRetencaoRetorno.Id,
                            sucesso = false,
                            mensagem = "Erro ao realizar transferência! " + message
                        };
                    }
                }

                #endregion

                return new RespPadrao
                {
                    sucesso = false,
                    mensagem = "Tranferencia não realizada! Verificar com a gestão"
                };

                #endregion
            }
            catch (Exception e)
            {
                lLog.Error(e, "BAT_ABST_05 ERRO");
                return new RespPadrao()
                {
                    sucesso = false,
                    mensagem = $"Erro ao integrar retenção. Protocolo Id: {protocoloId}. Exceção: {e.Message}"
                };
            }
        }

        public async Task<RespPadrao> CancelarRetencao(int retencaoId, bool integracao = false)
        {
            try
            {
                #region consulta de dados para processo

                //Retencao
                var lConsultaRetencao = Repository.Query.FirstOrDefault(x => x.Id == retencaoId);
                if (lConsultaRetencao == null)
                {
                    return new RespPadrao {sucesso = false, mensagem = "Retenção não localizado!"};
                }

                #endregion

                #region Tranferencia de valores

                if (VerificarTransacaoExistente(lConsultaRetencao.ContaOrigem.ToString(),
                    lConsultaRetencao.ContaDestino.ToString(),
                    lConsultaRetencao.ValorAbastecimentoAcrescimoTaxa,
                    lConsultaRetencao.DataCadastro,
                    lConsultaRetencao.Observacao).Result.sucesso == false)
                {
                    return new RespPadrao
                    {
                        sucesso = false,
                        mensagem = "Pagamento original não localizado!"
                    };
                }

                var mensagem = "";

                if (lConsultaRetencao.Status == StatusPagamentoAbastecimento.Baixado)
                {
                    var verificarSaldo = Saldo(lConsultaRetencao.ContaDestino.ToString(),
                            lConsultaRetencao.ValorAbastecimentoDesconto)
                        .Result;

                    var lTranferenciaRetencao = new TransferenciaEntreContaResp()
                    {
                        Sucesso = false
                    };

                    if (verificarSaldo)
                    {
                        if (VerificarTransacaoExistente(lConsultaRetencao.ContaDestino.ToString(),
                            lConsultaRetencao.ContaOrigem.ToString(),
                            lConsultaRetencao.ValorAbastecimentoAcrescimoTaxa,
                            lConsultaRetencao.DataCadastro,
                            lConsultaRetencao.Observacao).Result.sucesso)
                        {
                            var status = StatusPagamentoAbastecimento.Baixado;

                            var retornoatualizacao = await AtualizarStatus(lConsultaRetencao.Id, status);

                            if (retornoatualizacao.sucesso)
                            {
                                return new RespPadrao
                                {
                                    sucesso = true,
                                    mensagem = "Estorno realizado com sucesso!"
                                };
                            }
                        }

                        lTranferenciaRetencao = RealizaTransferenciaEntreContas(new Transferencia
                        {
                            amount = Math.Round(lConsultaRetencao.ValorAbastecimentoAcrescimoTaxa, 2),
                            description = lConsultaRetencao.Observacao,
                            originalAccount = lConsultaRetencao.ContaDestino.ToInt32(),
                            destinationAccount = lConsultaRetencao.ContaOrigem.ToInt()
                        }).Result;
                    }
                    else
                    {
                        mensagem = "Saldo insuficiente!";
                    }

                    if (lTranferenciaRetencao.Sucesso)
                    {
                        var status = StatusPagamentoAbastecimento.Cancelado;

                        var retornoatualizacao = await AtualizarStatus(retencaoId, status);

                        if (retornoatualizacao.sucesso)
                        {
                            return new RespPadrao
                            {
                                sucesso = true,
                                mensagem = "Extorno realizado com sucesso!"
                            };
                        }
                    }

                    return new RespPadrao
                    {
                        sucesso = false,
                        mensagem = "Erro ao realizar tranferência! " + mensagem
                    };
                }

                #endregion

                return new RespPadrao
                {
                    sucesso = false,
                    mensagem = "Erro ao realizar tranferência! " + mensagem
                };
            }
            catch (Exception e)
            {
                return new RespPadrao()
                {
                    sucesso = false,
                    mensagem = e.Message
                };
            }
        }

        public async Task<RespPadrao> EnviarPagamento(int pagamentoAbastecimentoId, bool integracao = false)
        {
            var lLog = LogManager.GetCurrentClassLogger();
            try
            {
                #region coleta de informacoes

                var lConsultaPagamentoAbastecimento = await Repository.Query
                    .Where(x => x.Id == pagamentoAbastecimentoId)
                    .Include(x => x.Abastecimento.Empresa)
                    .Include(x => x.Abastecimento)
                        .ThenInclude(x => x.Posto)
                            .ThenInclude(x => x.MdrPrazos)
                    .Include(x => x.LotePagamento.Empresa)
                    .Include(x => x.LotePagamento)
                        .ThenInclude(x => x.Posto)
                            .ThenInclude(x => x.MdrPrazos)
                    .FirstOrDefaultAsync();
                
                
                if (lConsultaPagamentoAbastecimento == null)
                    return new RespPadrao
                    {
                        sucesso = false, 
                        mensagem = "Pagamento não encontrado."
                    };
                
                #endregion

                #region validacoes

                if (lConsultaPagamentoAbastecimento.Status == StatusPagamentoAbastecimento.Baixado)
                    return new RespPadrao
                    {
                        sucesso = true,
                        mensagem = "Pagamento realizado com sucesso!"
                    };

                #endregion

                #region Transacao

                if (lConsultaPagamentoAbastecimento.Status == StatusPagamentoAbastecimento.Aprovado || lConsultaPagamentoAbastecimento.Status == StatusPagamentoAbastecimento.Erro)
                {
                    var valorTransferencia = Math.Round(lConsultaPagamentoAbastecimento.TipoOperacao == TipoOperacaoPagamentoAbastecimento.Pagamento
                            ? lConsultaPagamentoAbastecimento.ValorAbastecimentoDesconto
                            : lConsultaPagamentoAbastecimento.ValorAbastecimentoAcrescimoTaxa, 2);

                    if ((await VerificarTransacaoExistente(lConsultaPagamentoAbastecimento.ContaOrigem.ToString(),
                        lConsultaPagamentoAbastecimento.ContaDestino.ToString(), valorTransferencia,
                        lConsultaPagamentoAbastecimento.DataCadastro, lConsultaPagamentoAbastecimento.Observacao)).sucesso)
                    {
                        var retornoatualizacao = await AtualizarStatus(lConsultaPagamentoAbastecimento.Id, StatusPagamentoAbastecimento.Baixado);

                        //Alteração data parametro para futuros lotes
                        if (lConsultaPagamentoAbastecimento.LotePagamento.TipoLote == TipoLote.Retencao &&
                            lConsultaPagamentoAbastecimento.DataPrevisaoPagamento != null &&
                            lConsultaPagamentoAbastecimento.DataPrevisaoPagamento.Value.EndOfDay() == DateTime.Now.EndOfDay())
                        {
                            var empresaId = lConsultaPagamentoAbastecimento.LotePagamento.Empresa.Id;
                            await _empresaAppService.Repository.Command.AlterarDataParametroPrazo(empresaId);
                        }
                        
                        if (retornoatualizacao.sucesso)
                            return new RespPadrao
                            {
                                sucesso = true,
                                mensagem = "Pagamento realizado com sucesso!"
                            };
                    }

                    var lTranferenciaPagamento = await RealizaTransferenciaEntreContas(new Transferencia
                    {
                        amount = valorTransferencia,
                        description = lConsultaPagamentoAbastecimento.Observacao,
                        originalAccount = lConsultaPagamentoAbastecimento.ContaOrigem.ToInt32(),
                        destinationAccount = lConsultaPagamentoAbastecimento.ContaDestino.ToInt()
                    });

                    if (lTranferenciaPagamento.Sucesso)
                    {
                        var retornoatualizacao = await AtualizarStatus(lConsultaPagamentoAbastecimento.Id, StatusPagamentoAbastecimento.Baixado);

                        //Alteração data parametro para futuros lotes
                        if (lConsultaPagamentoAbastecimento.LotePagamento.TipoLote == TipoLote.Retencao &&
                            lConsultaPagamentoAbastecimento.DataPrevisaoPagamento != null &&
                            lConsultaPagamentoAbastecimento.DataPrevisaoPagamento.Value.EndOfDay() == DateTime.Now.EndOfDay())
                        {
                            var empresaId = lConsultaPagamentoAbastecimento.LotePagamento.Empresa.Id;
                            await _empresaAppService.Repository.Command.AlterarDataParametroPrazo(empresaId);
                        }

                        if (retornoatualizacao.sucesso)
                        {
                            return new RespPadrao
                            {
                                sucesso = true,
                                mensagem = "Pagamento realizado com sucesso!"
                            };
                        }
                    }
                    else
                    {
                        var lNumeroRetentativaEnvioPagamento = (await _parametrosAppService.GetParametrosAsync(-1,
                            Domain.Models.Parametros.Parametros.TipoDoParametro.NumeroRetentativaEnvioPagamento,
                            Domain.Models.Parametros.Parametros.TipoDoValor.Number))?.Valor?.ToDecimalSafe() ?? 0;

                        if (lNumeroRetentativaEnvioPagamento == 0) lNumeroRetentativaEnvioPagamento = 999;
                        
                        var status = lConsultaPagamentoAbastecimento.ContadorTentativas < lNumeroRetentativaEnvioPagamento ? 
                            StatusPagamentoAbastecimento.Aprovado : StatusPagamentoAbastecimento.Erro;

                        var retornoatualizacao = await AtualizarStatus(lConsultaPagamentoAbastecimento.Id, status);

                        if (retornoatualizacao.sucesso)
                        {
                            return new RespPadrao
                            {
                                sucesso = false,
                                mensagem = "Pagamento não realizado!"
                            };
                        }
                    }
                }
                else if (lConsultaPagamentoAbastecimento.Status == StatusPagamentoAbastecimento.Processando)
                {
                    if (lConsultaPagamentoAbastecimento.DataPrevisaoPagamento != null 
                        && lConsultaPagamentoAbastecimento.DataPrevisaoPagamento.Value.EndOfDay() <= DateTime.Now.EndOfDay())
                    {
                        lConsultaPagamentoAbastecimento.Status = StatusPagamentoAbastecimento.Aprovado;
                        var pagamentoAbastecimentoSalvar = Mapper.Map<PagamentoAbastecimentoAtualizarCommand>(lConsultaPagamentoAbastecimento);
                        await Engine.CommandBus.SendCommandAsync<Domain.Models.PagamentoAbastecimento.PagamentoAbastecimento>(pagamentoAbastecimentoSalvar);
                    }
                }

                #endregion

                return new RespPadrao
                {
                    sucesso = false,
                    mensagem = "Pagamento não realizado!"
                };
            }
            catch (Exception e)
            {
                lLog.Error(e);
                return new RespPadrao
                {
                    sucesso = false,
                    mensagem = e.Message
                };
            }
        }

        public async Task<RespPadrao> AtualizarStatus(int idPagamentoAbastecimento, StatusPagamentoAbastecimento status, string motivo = "")
        {
            //Consulta de registro
            var lPagamentoAbastecimentoUpdate = Repository.Query.FirstOrDefault(x => x.Id == idPagamentoAbastecimento);
            //Ajustes de valores do objeto
            lPagamentoAbastecimentoUpdate.Status = status;

            if (!motivo.IsNullOrWhiteSpace())
            {
                lPagamentoAbastecimentoUpdate.Observacao = motivo;
            }

            //Mapper
            var pagamentoAbastecimentoSalvar =
                Mapper.Map<PagamentoAbastecimentoAtualizarCommand>(lPagamentoAbastecimentoUpdate);

            //Save command
            var retorno = await Engine.CommandBus
                .SendCommandAsync<Domain.Models.PagamentoAbastecimento.PagamentoAbastecimento>(
                    pagamentoAbastecimentoSalvar);
            //sucesso
            if (retorno != null)
            {
                return new RespPadrao()
                {
                    sucesso = true,
                    mensagem = "Status atualizado para " + status + " com sucesso."
                };
            }

            //Insucesso
            return new RespPadrao()
            {
                sucesso = false,
                mensagem = "Status não atualizado."
            };
        }

        public Task<TransferenciaEntreContaResp> RealizaTransferenciaEntreContas(Transferencia transf)
        {
            return _transferenciaRepository.RealizaTransferenciaEntreContas(transf);
        }

        public async Task<RespPadrao> ConsultarGridPagamentoAbastecimento(DtoConsultaGridPagamentoAbastecimento request)
        {
            try
            {
                var dtIni = request.dataInicial.ToDateTime();
                var dtFim = request.dataFinal.ToDateTime().AddDays(1).AddSeconds(-1);
                
                var lPagamentoAbastecimento = Repository.Query
                    .Where(x => x.TipoOperacao != TipoOperacaoPagamentoAbastecimento.ReceitaMDR
                                && x.TipoOperacao != TipoOperacaoPagamentoAbastecimento.ReceitaEmpresa)
                    .Include(x => x.LotePagamento)
                    .ThenInclude(x => x.Posto)
                    .Include(x => x.Abastecimento)
                    .ThenInclude(x => x.PagamentoAbastecimentos).AsQueryable();

                if (request.EmpresaId > 0)
                {
                    lPagamentoAbastecimento =
                        lPagamentoAbastecimento.Where(x =>
                            x.Abastecimento.Portador.EmpresaIdFrota == request.EmpresaId);
                }

                if (request.TipoOperacao > 0)
                {
                    lPagamentoAbastecimento =
                        lPagamentoAbastecimento.Where(x => x.TipoOperacao.GetHashCode() == request.TipoOperacao);
                }

                if (request.Status > 0)
                {
                    lPagamentoAbastecimento =
                        lPagamentoAbastecimento.Where(x => x.Status.GetHashCode() == request.Status);
                }

                if (dtIni > ("01/01/0001 00:00:00").ToDateTime() || dtFim > ("01/01/0001 00:00:00").ToDateTime())
                {
                    lPagamentoAbastecimento =
                        lPagamentoAbastecimento.Where(p => p.DataAlteracao >= dtIni && p.DataAlteracao <= dtFim);
                }

                lPagamentoAbastecimento =
                    lPagamentoAbastecimento
                        .AplicarFiltrosDinamicos<Domain.Models.PagamentoAbastecimento.PagamentoAbastecimento>(
                            request.Filters);
                lPagamentoAbastecimento = string.IsNullOrWhiteSpace(request.Order?.Campo)
                    ? lPagamentoAbastecimento.OrderByDescending(o => o.Id)
                    : lPagamentoAbastecimento.OrderBy(
                        $"{request.Order.Campo} {request.Order.Operador.DescriptionAttr()}");

                var lCount = lPagamentoAbastecimento.Count();

                var retorno = await lPagamentoAbastecimento.Skip((request.Page - 1) * request.Take)
                    .Take(request.Take)
                    .ProjectTo<ConsultarGridPagamentoAbastecimentoItem>(Engine.Mapper.ConfigurationProvider)
                    .ToListAsync();

                var retornoItens = new List<ConsultarGridPagamentoAbastecimentoItem>();

                foreach (var pagamentoAbastecimentoItem in retorno)
                {
                    if (pagamentoAbastecimentoItem.TipoOperacao == TipoOperacaoPagamentoAbastecimento.Pagamento
                        || (pagamentoAbastecimentoItem.TipoOperacao == TipoOperacaoPagamentoAbastecimento.Retencao
                            && pagamentoAbastecimentoItem.LotePagamentoId != null))
                    {
                        var listAbastecimentos = _abastecimentoReadRepository
                            .Include(x => x.ProtocoloAbastecimento)
                            .Where(x => (pagamentoAbastecimentoItem.TipoOperacao ==
                                         TipoOperacaoPagamentoAbastecimento.Pagamento
                                ? x.ProtocoloAbastecimento.LotePagamentoId == pagamentoAbastecimentoItem.LotePagamentoId
                                : x.LoteRetencaoId == pagamentoAbastecimentoItem.LotePagamentoId));

                        var lAbastecimentosId = listAbastecimentos.Select(x => x.Id.ToString()).ToList().Distinct()
                            .Join("/").ToString();

                        pagamentoAbastecimentoItem.Abastecimentos = lAbastecimentosId;
                    }
                    else
                    {
                        pagamentoAbastecimentoItem.Abastecimentos = Repository.Query
                            .FirstOrDefault(x => x.Id == pagamentoAbastecimentoItem.Id)
                            .AbastecimentoId.ToString();
                    }

                    retornoItens.Add(pagamentoAbastecimentoItem);
                }

                return new RespPadrao
                {
                    sucesso = true,
                    mensagem = "sucesso",
                    data = new
                    {
                        items = retornoItens,
                        totalItems = lCount
                    }

                };
            }
            catch (Exception e)
            {
                LogManager.GetCurrentClassLogger().Error(e);
                return new RespPadrao(false, e.Message);
            }
        }

        public async Task<RespPadrao> ConsultarPorId(int idPagamentoAbastecimento)
        {
            try
            {
                return new RespPadrao(true, "Sucesso", Mapper.Map<PagamentoAbastecimentoResponse>(await Repository.Query.GetByIdAllInclude(idPagamentoAbastecimento)));
            }
            catch (Exception e)
            {
                LogManager.GetCurrentClassLogger().Error(e);
                return new RespPadrao(false, e.Message);
            }
        }

        public async Task<RespPadrao> AprovarPagamentoPendente(List<AprovarPagamentoRequest> pagamentoId)
        {
            try
            {

                foreach (var lAprovaPagamentos in pagamentoId)
                {
                    var atualizarStatus = await AtualizarStatus(lAprovaPagamentos.IdPagamento,
                        StatusPagamentoAbastecimento.Aprovado);

                }

                return new RespPadrao
                {
                    sucesso = true,
                    mensagem = "Registro aprovado com sucesso!"
                };
            }
            catch (Exception e)
            {
                return new RespPadrao()
                {
                    sucesso = false,
                    mensagem = e.Message
                };
            }
        }

        public async Task<RespPadrao> ReprovarPagamentoPendente(List<AprovarPagamentoRequest> pagamentoId)
        {
            try
            {

                foreach (var lReprovaPagamentos in pagamentoId)
                {
                    var atualizarStatus = await AtualizarStatus(lReprovaPagamentos.IdPagamento,
                        StatusPagamentoAbastecimento.Reprovado);

                }

                return new RespPadrao
                {
                    sucesso = true,
                    mensagem = "Registro reprovado com sucesso!"
                };
            }
            catch (Exception e)
            {
                return new RespPadrao()
                {
                    sucesso = false,
                    mensagem = e.Message
                };
            }
        }

        public async Task<RespPadrao> ReenvioPagamento(List<AprovarPagamentoRequest> pagamentoId, bool integracao = false)
        {
            try
            {
                foreach (var lReenvioPagamentos in pagamentoId)
                {
                    var atualizarStatus = await EnviarPagamento(lReenvioPagamentos.IdPagamento);

                    if (!atualizarStatus.sucesso)
                    {
                        return new RespPadrao
                        {
                            sucesso = false,
                            mensagem = "Pagamento(s) não enviado(s)!"
                        };
                    }
                }

                return new RespPadrao
                {
                    sucesso = true,
                    mensagem = "Pagamento(s) enviado(s) com sucesso!"
                };
            }
            catch (Exception e)
            {
                LogManager.GetCurrentClassLogger().Error(e);
                return new RespPadrao(false, e.Message);
            }
        }

        public async Task<RespPadrao> ConsultarGridReceitaAbastecimento(DtoConsultaGridReceitaAbastecimento request)
        {
            try
            {
                var DtInicial = request.dataInicial.ToDateTime();
                var DtFinal = request.dataFinal.ToDateTime().AddDays(1).AddSeconds(-1);
                
                var lPagamentoAbastecimento = Repository.Query.Where(x =>
                        x.TipoOperacao == TipoOperacaoPagamentoAbastecimento.ReceitaMDR
                        || x.TipoOperacao == TipoOperacaoPagamentoAbastecimento.ReceitaEmpresa)
                    .Include(x => x.Receita)
                    .Include(x => x.Abastecimento)
                    .ThenInclude(x => x.ProtocoloAbastecimento)
                    .AsQueryable();

                var empresaId = User.EmpresaId;

                if (empresaId > 0)
                {
                    lPagamentoAbastecimento =
                        lPagamentoAbastecimento.Where(x => x.Abastecimento.Portador.EmpresaIdFrota == empresaId);
                }

                if (request.Status > 0)
                {
                    lPagamentoAbastecimento = lPagamentoAbastecimento.Where(x => x.Status.GetHashCode() == request.Status);
                }

                if (DtInicial > ("01/01/0001 00:00:00").ToDateTime() || DtFinal > ("01/01/0001 00:00:00").ToDateTime())
                {
                    lPagamentoAbastecimento = lPagamentoAbastecimento.Where(p => p.DataAlteracao >= DtInicial && p.DataAlteracao <= DtFinal);
                }

                lPagamentoAbastecimento = lPagamentoAbastecimento.AplicarFiltrosDinamicos(request.Filters);
                lPagamentoAbastecimento = string.IsNullOrWhiteSpace(request.Order?.Campo)
                    ? lPagamentoAbastecimento.OrderByDescending(o => o.Id)
                    : lPagamentoAbastecimento.OrderBy($"{request.Order.Campo} {request.Order.Operador.DescriptionAttr()}");

                var lCount = lPagamentoAbastecimento.Count();
                var retorno = await lPagamentoAbastecimento.Skip((request.Page - 1) * request.Take)
                    .Take(request.Take).ProjectTo<ConsultarGridReceitaAbastecimentoItem>(Engine.Mapper.ConfigurationProvider).ToListAsync();

                return new RespPadrao()
                {
                    sucesso = true,
                    mensagem = "Sucesso!",
                    data = new { items = retorno, totalItems = lCount }
                };
            }
            catch (Exception e)
            {
                LogManager.GetCurrentClassLogger().Error(e);
                return new RespPadrao(false, e.Message);
            }
        }
        
        public async Task<RespPadrao> ReenviarReceitas(List<ReenviarReceitaAbastecimento> lLoteReceitaId)
        {
            try
            {
                var receitaId = lLoteReceitaId.Select(x => x.IdLoteReceita).Distinct();

                foreach (var lReenvioReceita in receitaId)
                {
                    await PagarReceita(lReenvioReceita);
                }

                return new RespPadrao
                {
                    sucesso = true,
                    mensagem = "Reenvio(s) de transação(ões) realizado(s) com sucesso!"
                };
            }
            catch (Exception e)
            {
                LogManager.GetCurrentClassLogger().Error(e);
                return new RespPadrao(false, e.Message);
            }
        }

        public async Task<RespPadrao> PagarReceita(int? LoteReceitaId, bool integracao = false)
        {
            var lLog = LogManager.GetCurrentClassLogger();

            try
            {
                #region coleta de informacoes

                var lConsultaReceitaAbastecimento = Repository.Query.Where(x => 
                    x.LoteReceitaId == LoteReceitaId && x.Status != StatusPagamentoAbastecimento.Baixado && 
                    (x.TipoOperacao == TipoOperacaoPagamentoAbastecimento.ReceitaEmpresa || x.TipoOperacao == TipoOperacaoPagamentoAbastecimento.ReceitaMDR));

                if (!lConsultaReceitaAbastecimento.Any())
                {
                    return new RespPadrao {sucesso = false, mensagem = "Registro não encontrado."};
                }

                #endregion

                #region Transacao

                var ordemPagamentoNãoPaga = "";

                foreach (var receitaAbastecimentoParaPagamento in lConsultaReceitaAbastecimento)
                {
                    if ((receitaAbastecimentoParaPagamento.Status == StatusPagamentoAbastecimento.Erro || 
                         receitaAbastecimentoParaPagamento.Status == StatusPagamentoAbastecimento.Aprovado)
                        && (receitaAbastecimentoParaPagamento.TipoOperacao == TipoOperacaoPagamentoAbastecimento.ReceitaEmpresa || 
                            receitaAbastecimentoParaPagamento.TipoOperacao == TipoOperacaoPagamentoAbastecimento.ReceitaMDR))
                    {
                        if ((await VerificarTransacaoExistente(receitaAbastecimentoParaPagamento.ContaOrigem.ToString(),
                            receitaAbastecimentoParaPagamento.ContaDestino.ToString(),
                            receitaAbastecimentoParaPagamento.ValorAbastecimentoDesconto,
                            receitaAbastecimentoParaPagamento.DataCadastro,
                            receitaAbastecimentoParaPagamento.Observacao)).sucesso)
                        {
                            var status = StatusPagamentoAbastecimento.Baixado;

                            var retornoatualizacao = await AtualizarStatus(receitaAbastecimentoParaPagamento.Id, status);

                            if (retornoatualizacao.sucesso)
                            {
                                return new RespPadrao
                                {
                                    sucesso = true,
                                    mensagem = "Tranferência já realizada."
                                };
                            }
                        }

                        var lTranferenciaPagamento = await RealizaTransferenciaEntreContas(new Transferencia
                        {
                            amount = Math.Round(receitaAbastecimentoParaPagamento.ValorAbastecimento, 2),
                            description = receitaAbastecimentoParaPagamento.Observacao,
                            originalAccount = receitaAbastecimentoParaPagamento.ContaOrigem.ToInt(),
                            destinationAccount = receitaAbastecimentoParaPagamento.ContaDestino.ToInt()
                        });

                        if (lTranferenciaPagamento.Sucesso)
                        {
                            var retornoatualizacao = await AtualizarStatus(receitaAbastecimentoParaPagamento.Id, StatusPagamentoAbastecimento.Baixado);
                        }
                        else
                        {
                            var retornoatualizacao = await AtualizarStatus(receitaAbastecimentoParaPagamento.Id, StatusPagamentoAbastecimento.Erro);

                            if (!retornoatualizacao.sucesso)
                            { 
                                ordemPagamentoNãoPaga = ordemPagamentoNãoPaga.IsNullOrWhiteSpace() 
                                    ? receitaAbastecimentoParaPagamento.Id.ToString() 
                                    : $"{ordemPagamentoNãoPaga},{receitaAbastecimentoParaPagamento.Id.ToString()}";
                            }
                        }
                    }
                }

                #endregion

                if (!ordemPagamentoNãoPaga.IsNullOrWhiteSpace())
                {
                    lLog.Error($"---> Erro ao realizar tranferencia de valores da receita Id {ordemPagamentoNãoPaga}.");
                }

                return new RespPadrao
                {
                    sucesso = false,
                    mensagem = !ordemPagamentoNãoPaga.IsNullOrWhiteSpace()
                        ? $"Tranferência não realizada para as seguintes ordens de pagamento {ordemPagamentoNãoPaga}."
                        : "Tranferência não realizada."
                };
            }
            catch (Exception e)
            {
                return new RespPadrao()
                {
                    sucesso = false,
                    mensagem = $"Erro ao realizar tranferencia de receita. {e.Message}"
                };
            }
        }

        
        public async Task<RespPadrao> ExportarRelatorio(DtoExportarRelatorioPagamentoAbastecimento request)
        {
            try
            {
                var gridPagamentoAbastecimentoGrid = await ConsultarGridPainelFinanceiro(request.GridPagamentoAbastecimento);

                var lExportList = new List<ExportObject<PagamentoAbastecimentoExport, AbastecimentoExport>>();

                var gridPagamentoAbastecimento = (ConsultarGridPainelFinanceiroResponse)gridPagamentoAbastecimentoGrid.data;
                
                foreach (var pagamentoAbastecimento in gridPagamentoAbastecimento.items)
                {
                    List<Domain.Models.Abastecimento.Abastecimento> abastecimentos;
                    if (pagamentoAbastecimento.TipoOperacao == TipoOperacaoPagamentoAbastecimento.Pagamento ||
                        (pagamentoAbastecimento.TipoOperacao == TipoOperacaoPagamentoAbastecimento.Retencao
                         && pagamentoAbastecimento.LotePagamentoId != null))
                    {
                        abastecimentos = await _abastecimentoReadRepository.GetAll()
                            .Include(x => x.PagamentoAbastecimentos)
                            .Include(x => x.ProtocoloAbastecimento)
                            .ThenInclude(a => a.LotePagamento)
                            .Include(x => x.Veiculo)
                            .Include(x => x.Combustivel)
                            .Where(x => pagamentoAbastecimento.TipoOperacao == TipoOperacaoPagamentoAbastecimento.Pagamento
                                ? x.ProtocoloAbastecimento.LotePagamento.Id == pagamentoAbastecimento.LotePagamentoId
                                : x.LotePagamento.Id == pagamentoAbastecimento.LotePagamentoId)
                            .ToListAsync();
                    }
                    else
                    {
                        abastecimentos = await _abastecimentoReadRepository.GetAll()
                            .Include(x => x.PagamentoAbastecimentos)
                            .Include(x => x.ProtocoloAbastecimento)
                            .Include(x => x.Veiculo)
                            .Include(x => x.Combustivel)
                            .Where(x => x.Id == pagamentoAbastecimento.AbastecimentoId)
                            .ToListAsync();
                    }

                    var lRetorno = Mapper.Map<List<AbastecimentoExport>>(abastecimentos);
                    var lRetornoPagamento = Mapper.Map<PagamentoAbastecimentoExport>(pagamentoAbastecimento);
                    var temp = new ExportObject<PagamentoAbastecimentoExport, AbastecimentoExport>
                    {
                        Agrupador = lRetornoPagamento,
                        Itens = lRetorno
                    };
                    lExportList.Add(temp);
                }

                var headerNames = new HeaderNames()
                {
                    AgrupadorName = "Pagamentos",
                    ItensName = "Abastecimentos"
                };
                return new RespPadrao(true, "Sucesso.", _reportExporterFinanceiro.Export(lExportList, request.TipoExport, request.Fields, headerNames));
            }
            catch (Exception e)
            {
                LogManager.GetCurrentClassLogger().Error(e);
                return new RespPadrao(false, e.Message);
            }
        }

        public async Task<RespPadrao> ConsultarGridPainelFinanceiro(DtoConsultaGridPainelFinanceiro request)
        {
            try
            {
                var lPostoId = User.AdministradoraId;
                
                var lPainelFinanceiro = Repository.Query
                    .Where(x => x.TipoOperacao != TipoOperacaoPagamentoAbastecimento.ReceitaMDR
                                && x.TipoOperacao != TipoOperacaoPagamentoAbastecimento.ReceitaEmpresa)
                    .Include(x => x.Abastecimento)
                    .Include(x => x.LotePagamento)
                    .ThenInclude(x => x.Posto)
                    .AsQueryable();


                if (lPostoId > 0)
                {
                    lPainelFinanceiro = lPainelFinanceiro
                        .Where(x => x.Abastecimento.PostoId == lPostoId
                                    || x.LotePagamento.PostoId == lPostoId);
                }

                if (request.EmpresaId > 0)
                {
                    lPainelFinanceiro = lPainelFinanceiro
                        .Where(x => (x.Abastecimento != null && x.Abastecimento.EmpresaId == request.EmpresaId)
                                    || (x.LotePagamento != null && x.LotePagamento.EmpresaId == request.EmpresaId));
                    
                }

                if (request.Status > 0)
                {
                    lPainelFinanceiro = lPainelFinanceiro
                        .Where(x => x.Status.GetHashCode() == request.Status);
                }

                if (!request.NumeroNota.IsNullOrWhiteSpace())
                {
                    lPainelFinanceiro = lPainelFinanceiro
                        .Where(x =>
                            x.Abastecimento.ProtocoloAbastecimento.NotaFiscal == request.NumeroNota
                            || x.LotePagamento.ProtocolosAbastecimento.FirstOrDefault().NotaFiscal == request.NumeroNota);
                }

                if (!request.NumeroProtocolo.IsNullOrWhiteSpace())
                {
                    lPainelFinanceiro = lPainelFinanceiro.Where(x =>
                        x.Abastecimento.ProtocoloAbastecimentoId == request.NumeroProtocolo.ToInt()
                        || x.LotePagamento.ProtocolosAbastecimento.FirstOrDefault().Id == request.NumeroProtocolo.ToInt());
                }
                
                var dtIni = request.DtInicial.ToDateTime();
                var dtFim = request.DtFinal.ToDateTime().AddDays(1).AddSeconds(-1);

                if (request.IsRelatorio != 0 && dtFim.Subtract(dtIni).Days > 30)
                {
                    dtFim = dtIni.AddDays(32).AddSeconds(-1);
                }

                lPainelFinanceiro = lPainelFinanceiro
                    .Where(p => p.DataCadastro >= dtIni && p.DataCadastro <= dtFim);
                
                if (request.TipoOperacao == TipoOperacaoPagamentoAbastecimento.Retencao)
                {
                    lPainelFinanceiro = lPainelFinanceiro
                        .Where(x =>
                            x.TipoOperacao == TipoOperacaoPagamentoAbastecimento.Pagamento ||
                            x.TipoOperacao == TipoOperacaoPagamentoAbastecimento.Retencao);
                }

                if (request.TipoOperacao == TipoOperacaoPagamentoAbastecimento.Pagamento)
                {
                    lPainelFinanceiro = lPainelFinanceiro
                        .Where(x =>
                            x.TipoOperacao == TipoOperacaoPagamentoAbastecimento.Pagamento);
                }

                lPainelFinanceiro = lPainelFinanceiro.AplicarFiltrosDinamicos(request.Filters);
                lPainelFinanceiro = string.IsNullOrWhiteSpace(request.Order?.Campo)
                    ? lPainelFinanceiro.OrderByDescending(o => o.Id)
                    : lPainelFinanceiro.OrderBy($"{request.Order.Campo} {request.Order.Operador.DescriptionAttr()}");

                var lCount = lPainelFinanceiro.Count();

                List<ConsultarGridPainelFinanceiroItem> retorno;

                if (request.IsRelatorio != 0)
                {
                    retorno = lPainelFinanceiro
                        .ProjectTo<ConsultarGridPainelFinanceiroItem>(Engine.Mapper.ConfigurationProvider)
                        .ToList();
                }
                else
                {
                    retorno = await lPainelFinanceiro
                        .Skip((request.Page - 1) * request.Take)
                        .Take(request.Take)
                        .ProjectTo<ConsultarGridPainelFinanceiroItem>(Engine.Mapper.ConfigurationProvider)
                        .ToListAsync();
                }

                var retornoItens = new List<ConsultarGridPainelFinanceiroItem>();

                foreach (var pagamentoAbastecimentoItem in retorno)
                {
                    if (pagamentoAbastecimentoItem.Litragem == 0)
                    {
                        var listAbastecimentos = _abastecimentoReadRepository
                            .Include(x => x.ProtocoloAbastecimento)
                            .Where(x => x.ProtocoloAbastecimento.LotePagamentoId ==
                                        pagamentoAbastecimentoItem.LotePagamentoId);

                        var lLoteLitragem = listAbastecimentos.Sum(x => x.Litragem);

                        pagamentoAbastecimentoItem.Litragem = lLoteLitragem;

                        if (pagamentoAbastecimentoItem.TipoOperacao == TipoOperacaoPagamentoAbastecimento.Pagamento)
                        {
                            pagamentoAbastecimentoItem.StatusProtocolo = listAbastecimentos.Any()
                                ? listAbastecimentos.First().ProtocoloAbastecimento.Status
                                : EStatusProtocolo.NaoPossui;
                        }

                    }

                    retornoItens.Add(pagamentoAbastecimentoItem);
                }

                var lPagamentosRealizados = retornoItens.Count(x => x.Status == StatusPagamentoAbastecimento.Baixado);

                var totalLitragem = retornoItens.Sum(x => x.Litragem).ToDecimal();

                return new RespPadrao()
                {
                    sucesso = true,
                    mensagem = "Sucesso",
                    data = new ConsultarGridPainelFinanceiroResponse
                    {
                        items = retornoItens,
                        totalItems = lCount,
                        totalPgtos = lPagamentosRealizados,
                        totalizador = totalLitragem
                    }
                };
            }
            catch (Exception e)
            {
                LogManager.GetCurrentClassLogger().Error(e);
                return new RespPadrao(false, e.Message);
            }
        }
        
        public async Task<RespPadrao> ConsultarGridPainelFinanceiroRelatorio(DtoConsultaGridPainelFinanceiro request)
        {
            try
            {
                var lPostoId = User.AdministradoraId;
                var lPainelFinanceiro = Repository.Query
                    .Where(x => x.TipoOperacao != TipoOperacaoPagamentoAbastecimento.ReceitaMDR && x.TipoOperacao != TipoOperacaoPagamentoAbastecimento.ReceitaEmpresa)
                    .Include(x => x.Abastecimento)
                    .Include(x => x.LotePagamento)
                    .ThenInclude(x => x.Posto)
                    .AsQueryable();
            
                if (lPostoId > 0)
                {
                    lPainelFinanceiro = lPainelFinanceiro.Where(x => 
                        x.Abastecimento.PostoId == lPostoId || x.LotePagamento.PostoId == lPostoId);
                }

                if (request.EmpresaId > 0)
                {
                    lPainelFinanceiro = lPainelFinanceiro.Where(x => 
                        x.Abastecimento.EmpresaId == request.EmpresaId || x.LotePagamento.PostoId == request.EmpresaId);
                }

                if (request.Status > 0)
                {
                    lPainelFinanceiro = lPainelFinanceiro.Where(x => 
                        x.Status.GetHashCode() == request.Status);
                }

                if (!request.NumeroNota.IsNullOrWhiteSpace())
                {
                    lPainelFinanceiro = lPainelFinanceiro.Where(x => 
                        x.Abastecimento.ProtocoloAbastecimento.NotaFiscal == request.NumeroNota || 
                        x.LotePagamento.ProtocolosAbastecimento.FirstOrDefault().NotaFiscal == request.NumeroNota);
                }

                if (!request.NumeroProtocolo.IsNullOrWhiteSpace())
                {
                    lPainelFinanceiro = lPainelFinanceiro.Where(x => 
                        x.Abastecimento.ProtocoloAbastecimentoId == request.NumeroProtocolo.ToInt() || 
                        x.LotePagamento.ProtocolosAbastecimento.FirstOrDefault().Id == request.NumeroProtocolo.ToInt());
                }

                
                var dtIni = request.DtInicial.ToDateTime();

                var dtFim = request.DtFinal.ToDateTime().AddDays(1).AddSeconds(-1);

                if (request.IsRelatorio != 0 && dtFim.Subtract(dtIni).Days > 30)
                {
                    dtFim = dtIni.AddDays(32).AddSeconds(-1);
                }

                lPainelFinanceiro = lPainelFinanceiro
                    .Where(p => p.DataCadastro >= dtIni && p.DataCadastro <= dtFim);
                

                if (request.TipoOperacao == TipoOperacaoPagamentoAbastecimento.Retencao)
                {
                    lPainelFinanceiro = lPainelFinanceiro
                        .Where(x =>
                            x.TipoOperacao == TipoOperacaoPagamentoAbastecimento.Pagamento ||
                            x.TipoOperacao == TipoOperacaoPagamentoAbastecimento.Retencao);
                }

                if (request.TipoOperacao == TipoOperacaoPagamentoAbastecimento.Pagamento)
                {
                    lPainelFinanceiro = lPainelFinanceiro
                        .Where(x =>
                            x.TipoOperacao == TipoOperacaoPagamentoAbastecimento.Pagamento);
                }

                lPainelFinanceiro = lPainelFinanceiro.AplicarFiltrosDinamicos(request.Filters);
                
                lPainelFinanceiro = string.IsNullOrWhiteSpace(request.Order?.Campo)
                    ? lPainelFinanceiro.OrderByDescending(o => o.Id)
                    : lPainelFinanceiro.OrderBy($"{request.Order.Campo} {request.Order.Operador.DescriptionAttr()}");

                var lCount = lPainelFinanceiro.Count();
            
                var retorno = await lPainelFinanceiro
                    .Skip((request.Page - 1) * request.Take)
                    .Take(request.Take)
                    .ToListAsync();
                
                var retornoItens = new List<ConsultarGridPainelFinanceiroRelatorioItem>();

                foreach (var pagamentoAbastecimento in retorno)
                {
                    List<Domain.Models.Abastecimento.Abastecimento> abastecimentos;
                    if (pagamentoAbastecimento.TipoOperacao == TipoOperacaoPagamentoAbastecimento.Pagamento || 
                        (pagamentoAbastecimento.TipoOperacao == TipoOperacaoPagamentoAbastecimento.Retencao && 
                         pagamentoAbastecimento.LotePagamentoId != null))
                    {
                        abastecimentos = await _abastecimentoReadRepository.GetAll()
                            .Include(x => x.PagamentoAbastecimentos)
                            .Include(x => x.ProtocoloAbastecimento)
                            .ThenInclude(a => a.LotePagamento)
                            .Include(x => x.Posto)
                            .Include(x => x.Veiculo)
                            .Include(x => x.Combustivel)
                            .Where(x => pagamentoAbastecimento.TipoOperacao == TipoOperacaoPagamentoAbastecimento.Pagamento
                                ? x.ProtocoloAbastecimento.LotePagamento.Id == pagamentoAbastecimento.LotePagamentoId
                                : x.LotePagamento.Id == pagamentoAbastecimento.LotePagamentoId)
                            .ToListAsync();
                    }
                    else
                    {
                        abastecimentos = await _abastecimentoReadRepository.GetAll()
                            .Include(x => x.PagamentoAbastecimentos)
                            .Include(x => x.ProtocoloAbastecimento)
                            .Include(x => x.Veiculo)
                            .Include(x => x.Posto)
                            .Include(x => x.Combustivel)
                            .Where(x => x.Id == pagamentoAbastecimento.AbastecimentoId)
                            .ToListAsync();
                    }

                    if (!abastecimentos.Any()) continue;
                    
                    foreach (var abastecimento in abastecimentos)
                    {
                        var pagamentoComRepeticao = Mapper.Map<ConsultarGridPainelFinanceiroRelatorioItem>(pagamentoAbastecimento);
                        pagamentoComRepeticao.AbastecimentoId = abastecimento.Id;
                        retornoItens.Add(pagamentoComRepeticao);
                    }
                }

                var relatorio = new ConsultarGridPainelFinanceiroRelatorioResponse()
                {
                    items = retornoItens,
                    totalItems = lCount
                };
                
                return new RespPadrao(true, "Dados consultados.", relatorio);
            }
            catch (Exception e)
            {
                LogManager.GetCurrentClassLogger().Error(e);
                return new RespPadrao(false, e.Message);
            }
        }

        #region Tarefas

        public async Task ServiceGerarRegistroRetencao()
        {
            var lLog = LogManager.GetCurrentClassLogger();
            try
            {
                #region Coleta de informações

                var lAbastecimentos = _abastecimentoReadRepository
                    .Where(x =>
                        x.Status == EStatusAbastecimento.Aprovado &&
                        x.ProtocoloAbastecimentoId != null &&
                        x.ProtocoloAbastecimento.Status == EStatusProtocolo.Aprovado &&
                        (x.TipoDebito == ETipoDebitoAbastecimento.Prazo &&
                         x.DataPrazoPagamentoRetencao <= DateTime.Now.EndOfDay() ||
                         x.TipoDebito == ETipoDebitoAbastecimento.Protocolo) &&
                        (x.ProtocoloAbastecimento.LotePagamento.PagamentoAbastecimento.Any(y =>
                             y.Status == StatusPagamentoAbastecimento.Erro) && x.LoteRetencaoId != null ||
                         !x.ProtocoloAbastecimento.LotePagamento.PagamentoAbastecimento.Any() &&
                         x.LoteRetencaoId == null));

                if (!lAbastecimentos.Any()) return;
                
                #endregion

                var lErros = string.Empty;

                var lListProtocoloId = lAbastecimentos
                    .Select(x => x.ProtocoloAbastecimentoId)
                    .Distinct()
                    .ToList();

                foreach (var protocoloId in lListProtocoloId)
                {
                    var tipoDebito = await lAbastecimentos
                        .Where(x => x.ProtocoloAbastecimentoId == protocoloId)
                        .Select(x => x.TipoDebito)
                        .FirstOrDefaultAsync();

                    var empresaId = lAbastecimentos
                        .Where(x => x.ProtocoloAbastecimentoId == protocoloId)
                        .Select(x => x.EmpresaId)
                        .FirstOrDefaultAsync();

                    var retornoIntegracao = await IntegrarRetencaoProtocolo(protocoloId.ToIntSafe(), 
                        tipoDebito == ETipoDebitoAbastecimento.Prazo);

                    if (!retornoIntegracao.sucesso)
                        lErros += $"Empresa Id {await empresaId}, Protocolo Id {protocoloId}: {retornoIntegracao.mensagem};";
                }

                if (!lErros.IsNullOrWhiteSpace()) lLog.Error($"BAT_ABST_05 ERROS: {lErros}");
            }
            catch (Exception e)
            {
                lLog.Error(e, "BAT_ABST_05 ERRO");
            }
        }

        public async Task ServiceGerarRegistroPagamentoAbastecimento()
        {
            var lLog = LogManager.GetCurrentClassLogger();
            try
            {
                await GerarRegistroPagamento();
            }
            catch (Exception e)
            {
                lLog.Info(e, "BAT_ABST_03 ERRO");
            }
        }

        public async Task ServiceRealizarPagamentoAbastecimento()
        {
            var lLog = LogManager.GetCurrentClassLogger();
            try
            {
                var lNumeroRetentativaEnvioPagamento = (await _parametrosAppService.GetParametrosAsync(-1,
                    Domain.Models.Parametros.Parametros.TipoDoParametro.NumeroRetentativaEnvioPagamento,
                    Domain.Models.Parametros.Parametros.TipoDoValor.Number))?.Valor?.ToDecimalSafe() ?? 0;

                if (lNumeroRetentativaEnvioPagamento == 0) lNumeroRetentativaEnvioPagamento = 999;
                
                var lPagamentoAbastecimentos = Repository.Query
                    .Where(x => 
                        (x.TipoOperacao == TipoOperacaoPagamentoAbastecimento.Pagamento && x.ContadorTentativas < lNumeroRetentativaEnvioPagamento 
                         || x.TipoOperacao == TipoOperacaoPagamentoAbastecimento.Retencao)
                        && x.LotePagamentoId != null
                        && (x.Status == StatusPagamentoAbastecimento.Aprovado || x.Status == StatusPagamentoAbastecimento.Processando || x.Status == StatusPagamentoAbastecimento.Erro)
                        && x.DataPrevisaoPagamento <= DateTime.Now.EndOfDay());

                foreach (var lPagamentoAbastecimento in lPagamentoAbastecimentos)
                {
                    var retorno = await EnviarPagamento(lPagamentoAbastecimento.Id, true);

                    if (!retorno.sucesso)
                        lLog.Error($"BAT_ABST_04 ERRO: Pagamento abastecimento {lPagamentoAbastecimento.Id} não enviado. {retorno.mensagem}");
                }
            }
            catch (Exception e)
            {
                lLog.Error(e, "BAT_ABST_04 ERRO");
            }
        }

        public async Task ServiceGerarRegistroPagamentoReceita()
        {
            var lLog = LogManager.GetCurrentClassLogger();
            try
            {
                #region Coletas de retenções baixadas para geração de lotes

                var lDiasRetroativos = (await _parametrosAppService.GetParametrosAsync(-1,
                    Domain.Models.Parametros.Parametros.TipoDoParametro.DiasRetroativosGerarReceita,
                    Domain.Models.Parametros.Parametros.TipoDoValor.Number)).Valor.ToIntSafe();
                
                var lPagamentosAbastecimentoPagos = Repository.Query
                    .Where(pa => pa.TipoOperacao == TipoOperacaoPagamentoAbastecimento.Retencao 
                                 && pa.LoteReceitaId == null
                                 && pa.DataBaixa != null
                                 && pa.DataBaixa.Value.EndOfDay() >= DateTime.Now.AddDays(lDiasRetroativos > 0 ? -lDiasRetroativos : -7).EndOfDay()
                                 && pa.Status == StatusPagamentoAbastecimento.Baixado)
                    .Include(x => x.Abastecimento)
                    .ThenInclude(x => x.Empresa)
                    .Include(x => x.LotePagamento)
                    .Include(x => x.Receita)
                    .ToList();

                if (!lPagamentosAbastecimentoPagos.Any()) return;

                #endregion

                #region Calculo de valor % empresa para geração de ordem de pagamento de % empresa

                decimal valoresReceitaRetencaoImpostosCalculado = 0;

                foreach (var retencao in lPagamentosAbastecimentoPagos)
                {
                    var imposto = retencao.ImpostoCOFINSTransacao + retencao.ImpostoPISTransacao + 
                                  retencao.ImpostoCSLLTransacao + retencao.ImpostoIRRFTransacao;

                        valoresReceitaRetencaoImpostosCalculado += retencao.TaxaAbastecimento - retencao.TaxaAbastecimento * 
                            (imposto.ToDecimalSafe() / 100).ToDecimalSafe();
                }

                #endregion

                #region Calculo de valor MDR para geração de ordem de pagamento MDR

                var valoresReceitaMdr = lPagamentosAbastecimentoPagos
                    .Where(lpa => lpa.TipoOperacao == TipoOperacaoPagamentoAbastecimento.Retencao)
                    .Sum(r => r.DescontoMDR)
                    .ToDecimalSafe();
                #endregion

                #region Criaçao de lote de receita com valor liquido total de % empresa + Mdr

                var lValor = valoresReceitaRetencaoImpostosCalculado + valoresReceitaMdr;
                if(lValor == 0) return;

                var loteSave = new PagamentoLoteRequest();

                loteSave.ValorPagamento = lValor.ToDecimal();
                loteSave.DataPrevisaoPagamento = DateTime.Now;
                loteSave.DataCadastro = DateTime.Now;
                loteSave.EmpresaId = lPagamentosAbastecimentoPagos.First()?.Abastecimento?.EmpresaId
                                     ?? lPagamentosAbastecimentoPagos.First()?.LotePagamento?.EmpresaId
                                     ?? 1;
                loteSave.TipoLote = TipoLote.Receita;
                loteSave.PostoId = null;

                var observacaoJson = new LotePagamentoReceitaObservacao()
                {
                    CNPJ = lPagamentosAbastecimentoPagos.First()?.Abastecimento?.Empresa?.Cnpj
                           ?? lPagamentosAbastecimentoPagos.First()?.LotePagamento?.Empresa?.Cnpj
                           ?? "",
                    ValorBruto = lValor.ToString(CultureInfo.InvariantCulture),
                    ValorEmpresa = valoresReceitaRetencaoImpostosCalculado.ToStringSafe(),
                    ValorMdr = valoresReceitaMdr.ToStringSafe(),
                    DataPrevisao = loteSave.DataPrevisaoPagamento.ToString(CultureInfo.InvariantCulture)
                };

                loteSave.ObservacaoJson = JsonConvert.SerializeObject(observacaoJson);

                #region save lote de receita e atualização de lote receita nas ordens de retenção

                var lReceitaLoteSave = Mapper.Map<LotePagamentoSalvarComRetornoCommand>(loteSave);

                var loteReceitaRetorno = await Engine.CommandBus
                    .SendCommandAsync<LotePagamento>(lReceitaLoteSave);

                foreach (var pagamento in lPagamentosAbastecimentoPagos)
                {
                    var pagamentoRetencaoAddLoteReceita = Mapper.Map<PagamentoAbastecimentoAtualizarCommand>(pagamento);

                    pagamentoRetencaoAddLoteReceita.LoteReceitaId = loteReceitaRetorno.Id;

                    await Engine.CommandBus.SendCommandAsync(pagamentoRetencaoAddLoteReceita);
                }

                #endregion

                #endregion

                Domain.Models.PagamentoAbastecimento.PagamentoAbastecimento retornoReceitaMdr = null;
                Domain.Models.PagamentoAbastecimento.PagamentoAbastecimento retornoReceitaEmpresa = null;
                
                #region Objeto de ordem de pagamento para receita empresa

                if (valoresReceitaRetencaoImpostosCalculado != 0)
                {
                    var receitaEmpresa = new LoteTransacaoIntegrarApiRequest();

                    receitaEmpresa.ContaOrigem = (await _parametrosAppService.GetParametrosAsync(-1,
                        Domain.Models.Parametros.Parametros.TipoDoParametro.CodigoContaTransferenciaValorRetencao,
                        Domain.Models.Parametros.Parametros.TipoDoValor.Number))?.Valor?.ToIntSafe() ?? 0;

                    if (receitaEmpresa.ContaOrigem == 0)
                    {
                        lLog.Error("BAT_ABST_01 ERRO: Parâmetro CodigoContaTransferenciaValorRetencao não configurado.");
                        return;
                    }

                    receitaEmpresa.ContaDestino = (await _parametrosAppService.GetParametrosAsync(-1,
                        Domain.Models.Parametros.Parametros.TipoDoParametro.CodigoContaCorrenteReceita,
                        Domain.Models.Parametros.Parametros.TipoDoValor.Number))?.Valor?.ToIntSafe() ?? 0;

                    if (receitaEmpresa.ContaDestino == 0)
                    {
                        lLog.Error("BAT_ABST_01 ERRO: Parâmetro CodigoContaCorrenteReceita não configurado.");
                        return;
                    }

                    receitaEmpresa.Observacao = "";
                    receitaEmpresa.Status = StatusPagamentoAbastecimento.Aprovado;
                    receitaEmpresa.TipoOperacao = TipoOperacaoPagamentoAbastecimento.ReceitaEmpresa;
                    receitaEmpresa.ValorAbastecimento = valoresReceitaRetencaoImpostosCalculado;
                    receitaEmpresa.LoteReceitaId = loteReceitaRetorno.Id;

                    var receitaEmpresaSave = Mapper.Map<PagamentoAbastecimentoAdicionarCommand>(receitaEmpresa);

                    retornoReceitaEmpresa = await Engine.CommandBus
                        .SendCommandAsync<Domain.Models.PagamentoAbastecimento.PagamentoAbastecimento>(receitaEmpresaSave);

                    if (retornoReceitaEmpresa.Id <= 0) return;
                }

                #endregion

                #region Objeto de ordem de pagamento para receita MDR
                
                if (valoresReceitaMdr != 0)
                {
                    var receitaMdr = new LoteTransacaoIntegrarApiRequest();

                    receitaMdr.ContaOrigem = (await _parametrosAppService.GetParametrosAsync(-1,
                        Domain.Models.Parametros.Parametros.TipoDoParametro.CodigoContaTransferenciaValorRetencao,
                        Domain.Models.Parametros.Parametros.TipoDoValor.Number))?.Valor.ToIntSafe() ?? 0;

                    if (receitaMdr.ContaOrigem == 0)
                    {
                        lLog.Error("BAT_ABST_01 ERRO: Parâmetro CodigoContaTransferenciaValorRetencao não configurado.");
                        return;
                    }

                    receitaMdr.ContaDestino = (await _parametrosAppService.GetParametrosAsync(-1,
                        Domain.Models.Parametros.Parametros.TipoDoParametro.CodigoContaCorrenteReceita,
                        Domain.Models.Parametros.Parametros.TipoDoValor.Number))?.Valor.ToIntSafe() ?? 0;

                    if (receitaMdr.ContaDestino == 0)
                    {
                        lLog.Error("BAT_ABST_01 ERRO: Parâmetro CodigoContaCorrenteReceita não configurado.");
                        return;
                    }

                    receitaMdr.Observacao = "";
                    receitaMdr.Status = StatusPagamentoAbastecimento.Aprovado;
                    receitaMdr.TipoOperacao = TipoOperacaoPagamentoAbastecimento.ReceitaMDR;
                    receitaMdr.ValorAbastecimento = valoresReceitaMdr;
                    receitaMdr.LoteReceitaId = loteReceitaRetorno.Id;

                    var receitaSaveMdr = Mapper.Map<PagamentoAbastecimentoAdicionarCommand>(receitaMdr);

                    retornoReceitaMdr = await Engine.CommandBus
                        .SendCommandAsync<Domain.Models.PagamentoAbastecimento.PagamentoAbastecimento>(receitaSaveMdr);

                    if (retornoReceitaMdr.Id <= 0) return;
                }

                #endregion

                #region Adição de id de ordens em loteReceita

                await _lotePagamentoWriteRepository
                    .AddOrdemPagamentoIds(loteReceitaRetorno.Id, retornoReceitaEmpresa?.Id, retornoReceitaMdr?.Id);

                #endregion

                await PagarReceita(loteReceitaRetorno.Id, true);
            }
            catch (Exception e)
            {
                lLog.Error(e, "BAT_ABST_01 ERRO");
            }
        }

        public async Task ServiceRealizarPagamentoReceita()
        {
            var lLog = LogManager.GetCurrentClassLogger();
            try
            {
                var lNumeroRetentativaEnvioPagamento = (await _parametrosAppService.GetParametrosAsync(-1,
                    Domain.Models.Parametros.Parametros.TipoDoParametro.NumeroRetentativaEnvioPagamento,
                    Domain.Models.Parametros.Parametros.TipoDoValor.Number))?.Valor.ToDecimalSafe() ?? 0;

                if (lNumeroRetentativaEnvioPagamento == 0) lNumeroRetentativaEnvioPagamento = 999;
                
                var lPagamentoReceitas = await Repository.Query.Where(x => 
                        (x.TipoOperacao == TipoOperacaoPagamentoAbastecimento.ReceitaEmpresa || x.TipoOperacao == TipoOperacaoPagamentoAbastecimento.ReceitaMDR) 
                        && (x.Status == StatusPagamentoAbastecimento.Aprovado || x.Status == StatusPagamentoAbastecimento.Processando ||
                            x.Status == StatusPagamentoAbastecimento.Erro && x.LoteReceitaId != null) 
                        && x.ContadorTentativas <= lNumeroRetentativaEnvioPagamento)
                    .Select(x => x.LoteReceitaId)
                    .Distinct()
                    .ToListAsync();

                foreach (var lPagamentoReceita in lPagamentoReceitas)
                {
                    var retorno = await PagarReceita(lPagamentoReceita, true);
                    
                    if (!retorno.sucesso) lLog.Error($"BAT_ABST_02 ERRO: Falha ao reenviar o pagamento de receita  {lPagamentoReceita}. {retorno.mensagem}");
                }
            }
            catch (Exception e)
            {
                lLog.Error(e, "BAT_ABST_02 ERRO");
            }
        }

        #region Metodos auxiliares

        private async Task<RespPadrao> GerarPagamento(LotePagamento lLote, bool integracao = false)
        {
            #region Consulta de abastecimento para coleta de informacoes

            if (lLote == null)
            {
                return new RespPadrao {sucesso = false, mensagem = "Abastecimento não localizado!"};
            }

            var contaPosto = _cartaoRepository.ConsultarContasPostos(null, null, lLote.Posto.Cnpj, isPosto: true);

            if (contaPosto.IsNullOrWhiteSpace())
            {
                return new RespPadrao {sucesso = false, mensagem = "Conta de transferência para posto não localizada!"};
            }

            var lIdContaParametro = _parametrosAppService.GetParametrosAsync(-1,
                Domain.Models.Parametros.Parametros.TipoDoParametro.CodigoContaTransferenciaValorRetencao,
                Domain.Models.Parametros.Parametros.TipoDoValor.Number).Result.Valor;

            var lAprovaPagamentosAutomatico = _parametrosAppService.GetParametrosAsync(-1,
                Domain.Models.Parametros.Parametros.TipoDoParametro.AprovarPagamentosAutomaticamente,
                Domain.Models.Parametros.Parametros.TipoDoValor.Number).Result.Valor.ToDecimalSafe();

            if (lIdContaParametro == null)
            {
                return new RespPadrao {sucesso = false, mensagem = "Parametro conta retenção não localizado!"};
            }

            #endregion

            #region ObjetoPagamentoAbastecimento-Pagamento

            var pagamentoSave = new LoteTransacaoIntegrarApiRequest();

            pagamentoSave.LotePagamentoId = lLote.Id;
            pagamentoSave.ContaOrigem = lIdContaParametro.ToInt();
            pagamentoSave.ContaDestino = contaPosto.ToInt();
            pagamentoSave.DescontoMDR = lLote.Posto.MdrPrazos.MDR != null ? (decimal) (lLote.ValorPagamento * (lLote.Posto.MdrPrazos.MDR / 100)) : 0;

            pagamentoSave.DataPrevisaoPagamento = lLote.DataPrevisaoPagamento;
            pagamentoSave.Observacao = lLote.ObservacaoJson;
            pagamentoSave.Status = lAprovaPagamentosAutomatico == 0 ? StatusPagamentoAbastecimento.PendenteAprovacao 
                : StatusPagamentoAbastecimento.Aprovado;

            pagamentoSave.TipoOperacao = TipoOperacaoPagamentoAbastecimento.Pagamento;
            pagamentoSave.ValorAbastecimento = lLote.ValorPagamento;
            pagamentoSave.ValorAbastecimentoDesconto = lLote.ValorPagamento - pagamentoSave.DescontoMDR;

            var lPagamentoAbastecimentoSave = Mapper.Map<PagamentoAbastecimentoAdicionarCommand>(pagamentoSave);

            #endregion

            #region Salvamento do pagamento

            var retornoRegistro = await Engine.CommandBus
                .SendCommandAsync<Domain.Models.PagamentoAbastecimento.PagamentoAbastecimento>(
                    lPagamentoAbastecimentoSave);
            
            if (retornoRegistro.Id > 0)
            {
                return new RespPadrao
                {
                    id = retornoRegistro.Id,
                    sucesso = true,
                    mensagem = "Registro salvo com sucesso!"
                };
            }

            #endregion

            return new RespPadrao
            {
                sucesso = false,
                mensagem = "Erro ao salvar registro!"
            };
        }

        private async Task GerarLotePagamento(PagamentoLoteRequest lotePagamento, List<int?> lProtocolos)
        {
            var lLog = LogManager.GetCurrentClassLogger();
            var lPagamentoLoteSave = Mapper.Map<LotePagamentoSalvarComRetornoCommand>(lotePagamento);

            var loteRetorno = await Engine.CommandBus.SendCommandAsync<LotePagamento>(lPagamentoLoteSave);

            var obsJson =
                JsonConvert.DeserializeObject<LotePagamentoAbastecimentoObservacao>(loteRetorno.ObservacaoJson);

            obsJson = new LotePagamentoAbastecimentoObservacao()
            {
                CNPJ = obsJson.CNPJ,
                LoteId = loteRetorno.Id.ToString(),
                ValorPagamento = obsJson.ValorPagamento,
                DataPrevisao = obsJson.DataPrevisao
            };

            loteRetorno.ObservacaoJson = JsonConvert.SerializeObject(obsJson);

            loteRetorno = await Engine.CommandBus.SendCommandAsync<LotePagamento>(lPagamentoLoteSave);

            foreach (var protocolo in lProtocolos)
            {
                var protocoloUpdate = new ProtocoloLoteRequest()
                {
                    Id = protocolo.ToInt(),
                    LotePagamentoId = loteRetorno.TipoLote == 0 ? loteRetorno.Id : 0
                };

                var protocoloSave = Mapper.Map<ProtocoloAbastecimentoSalvarLoteCommand>(protocoloUpdate);

                await Engine.CommandBus.SendCommandAsync(protocoloSave);
            }

            var gerarPagamentoAbastecimento = await GerarPagamento(loteRetorno, false);

            if (!gerarPagamentoAbastecimento.sucesso)
                lLog.Error("BAT_ABST_03 ERRO: Registro de pagamento de abastecimento não realizado: " + gerarPagamentoAbastecimento.mensagem);
        }

        private async Task MontarLotesPagamento(List<int> lListPostoId, IQueryable<Domain.Models.Abastecimento.Abastecimento> lAbastecimentos)
        {
            foreach (var postoId in lListPostoId)
            {
                var lGrupoAbastecimentosPorDia = lAbastecimentos
                    .Where(x => x.PostoId == postoId && x.ProtocoloAbastecimentoId != null)
                    .ToList()
                    .GroupBy(x => x.ProtocoloAbastecimento.DataAprovacao.Value.ToString("dd/MM/yyyy"))
                    .ToList();

                foreach (var lGrupoAbastecimento in lGrupoAbastecimentosPorDia)
                {
                    var lValor = lGrupoAbastecimento.Sum(x => x.ValorAbastecimento);

                    var loteSave = new PagamentoLoteRequest();
                    var mdrPrazo = lGrupoAbastecimento.First().Posto?.MdrPrazos?.Prazo ?? 1;

                    loteSave.ValorPagamento = lValor.ToDecimal();
                    loteSave.PostoId = postoId;
                    loteSave.DataPrevisaoPagamento = lGrupoAbastecimento.FirstOrDefault().ProtocoloAbastecimento.DataAprovacao.Value.AddDays(mdrPrazo);
                    loteSave.DataCadastro = DateTime.Now;
                    loteSave.EmpresaId = lGrupoAbastecimento.First().Posto.Filial?.EmpresaId ?? 1;
                    loteSave.TipoLote = TipoLote.Pagamento;

                    var observacaoJson = new LotePagamentoAbastecimentoObservacao()
                    {
                        CNPJ = lGrupoAbastecimento.First().Posto.Cnpj,
                        LoteId = "",
                        ValorPagamento = lValor.ToString(CultureInfo.InvariantCulture),
                        DataPrevisao = loteSave.DataPrevisaoPagamento.ToString(CultureInfo.InvariantCulture)
                    };

                    loteSave.ObservacaoJson = JsonConvert.SerializeObject(observacaoJson);

                    await GerarLotePagamento(loteSave, lGrupoAbastecimento.Select(x => x.ProtocoloAbastecimentoId).Distinct().ToList());
                }
            }
        }

        private async Task GerarRegistroPagamento()
        {
            #region SQL Lote retenção

            var lAbastecimentosRetencaoLoteRetencao = _abastecimentoReadRepository
                .Include(x => x.ProtocoloAbastecimento)
                .Include(x => x.LotePagamento)
                    .ThenInclude(x => x.PagamentoAbastecimento)
                .Include(x => x.Posto)
                    .ThenInclude(x => x.MdrPrazos)
                .Where(x => 
                    x.ProtocoloAbastecimento.Status == EStatusProtocolo.Aprovado 
                    && x.LotePagamento.PagamentoAbastecimento.All(a => a.TipoOperacao != TipoOperacaoPagamentoAbastecimento.Pagamento)
                    && x.LotePagamento.PagamentoAbastecimento.Any(a => 
                        a.TipoOperacao == TipoOperacaoPagamentoAbastecimento.Retencao 
                        && (a.Status == StatusPagamentoAbastecimento.Baixado 
                            || x.TipoDebito == ETipoDebitoAbastecimento.Prazo && (a.Status == StatusPagamentoAbastecimento.Processando || a.Status == StatusPagamentoAbastecimento.Aprovado)))
                    && x.ProtocoloAbastecimento.DataAprovacao != null
                    && x.ProtocoloAbastecimento.LotePagamentoId == null)
                .Select(x => x)
                .ToList();

            lAbastecimentosRetencaoLoteRetencao.AddRange(_abastecimentoReadRepository
                .Include(x => x.ProtocoloAbastecimento)
                .Include(x => x.PagamentoAbastecimentos)
                .Include(x => x.Posto)
                    .ThenInclude(x => x.MdrPrazos)
                .Where(x =>
                    x.ProtocoloAbastecimento.Status == EStatusProtocolo.Aprovado 
                    && x.PagamentoAbastecimentos.All(a => a.TipoOperacao != TipoOperacaoPagamentoAbastecimento.Pagamento)
                    && x.PagamentoAbastecimentos.Any(a => a.TipoOperacao == TipoOperacaoPagamentoAbastecimento.Retencao && a.Status == StatusPagamentoAbastecimento.Baixado)
                    && x.ProtocoloAbastecimento.DataAprovacao != null
                    && x.ProtocoloAbastecimento.LotePagamentoId == null)
                .Select(x => x)
                .ToList());

            if (!lAbastecimentosRetencaoLoteRetencao.Any()) return;

            var lListPostoId = lAbastecimentosRetencaoLoteRetencao.AsQueryable()
                .Select(x => x.PostoId)
                .Distinct()
                .ToList();

            await MontarLotesPagamento(lListPostoId, lAbastecimentosRetencaoLoteRetencao.AsQueryable());

            #endregion
        }

        #endregion
        
        #endregion
    }
}