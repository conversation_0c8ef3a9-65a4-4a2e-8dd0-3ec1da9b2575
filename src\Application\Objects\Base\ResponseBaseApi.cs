using System;
using Microsoft.AspNetCore.Mvc;
using NLog;

namespace SistemaInfo.BBC.Application.Objects.Base
{
    public class ResponseBaseApi
    {
        public static Logger _logger = LogManager.GetCurrentClassLogger();
        
        public static JsonResult Responder(bool sucesso, string msg, object data, string error = null)
        {
            return BigJson(new
            {
                mensagem = msg.Length > 250 ? msg.Substring(0, 250) : msg ?? "",
                sucesso = sucesso,
                data,
                error
            });
        }
        
        public static JsonResult ResponderSucesso(object data)
        {
            return BigJson(new
            {
                mensagem = "Operação realizada com sucesso!",
                sucesso = true,
                data = data
            });
        }
        
        public static JsonResult ResponderErro(string message)
        {
            return Responder(false, message.Length > 250 ? message.Substring(0, 250) : message, null);
        }
        
        public static JsonResult ResponderErro(Exception ex)
        {
            _logger.Error(ex);

            var msg = ex.Message.Length > 250 ? ex.Message.Substring(0, 250) : ex.Message;
            if (!string.IsNullOrWhiteSpace(ex.InnerException?.Message))
                msg += " / " + ex.InnerException;

            return Responder(false, msg,null);
        }
        
        public static JsonResult BigJson(object data)
        {
            return new JsonResult(data);
        }
        
        public static JsonResult ResponderSimples(object data)
        {
            return BigJson(new
            {
                data
            });
        }
    }
}